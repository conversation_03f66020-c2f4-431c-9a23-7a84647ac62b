<?php
/**
 * Footer Template
 */

// Get settings
$settings = Settings::getInstance();

// Get categories for footer
$categoryObj = new Category();
$footerCategories = $categoryObj->getPopular(6);

// Get cities for footer
$locationObj = new Location();
$footerCities = $locationObj->getPopularCities(6);

// Get recent blog posts for footer
$blogObj = new Blog();
$footerPosts = $blogObj->getRecentPosts(3);
?>
<footer class="site-footer">
    <!-- Footer Top -->
    <div class="footer-top bg-dark text-light py-5">
        <div class="container">
            <div class="row">
                <!-- About -->
                <div class="col-lg-3 col-md-6">
                    <div class="footer-widget">
                        <h3>About Us</h3>
                        <div class="footer-about">
                            <img src="<?php echo getAssetUrl($settings->getSiteLogo()); ?>" alt="<?php echo $settings->getSiteName(); ?>" class="footer-logo">
                            <p><?php echo $settings->getSiteDescription(); ?></p>
                            <div class="social-links">
                                <?php $socialLinks = $settings->getSocialLinks(); ?>
                                <?php if (!empty($socialLinks['facebook'])): ?>
                                    <a href="<?php echo $socialLinks['facebook']; ?>" target="_blank"><i class="fab fa-facebook-f"></i></a>
                                <?php endif; ?>
                                <?php if (!empty($socialLinks['twitter'])): ?>
                                    <a href="<?php echo $socialLinks['twitter']; ?>" target="_blank"><i class="fab fa-twitter"></i></a>
                                <?php endif; ?>
                                <?php if (!empty($socialLinks['instagram'])): ?>
                                    <a href="<?php echo $socialLinks['instagram']; ?>" target="_blank"><i class="fab fa-instagram"></i></a>
                                <?php endif; ?>
                                <?php if (!empty($socialLinks['linkedin'])): ?>
                                    <a href="<?php echo $socialLinks['linkedin']; ?>" target="_blank"><i class="fab fa-linkedin-in"></i></a>
                                <?php endif; ?>
                                <?php if (!empty($socialLinks['youtube'])): ?>
                                    <a href="<?php echo $socialLinks['youtube']; ?>" target="_blank"><i class="fab fa-youtube"></i></a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Links -->
                <div class="col-lg-3 col-md-6">
                    <div class="footer-widget">
                        <h3>Quick Links</h3>
                        <ul class="footer-links">
                            <li><a href="<?php echo getBaseUrl(); ?>">Home</a></li>
                            <li><a href="<?php echo getBaseUrl(); ?>about.php">About Us</a></li>
                            <li><a href="<?php echo getBaseUrl(); ?>categories.php">Categories</a></li>
                            <li><a href="<?php echo getBaseUrl(); ?>cities.php">Cities</a></li>
                            <li><a href="<?php echo getBaseUrl(); ?>blog.php">Blog</a></li>
                            <li><a href="<?php echo getBaseUrl(); ?>contact.php">Contact Us</a></li>
                            <li><a href="<?php echo getBaseUrl(); ?>register.php?type=coaching_owner">List Your Coaching</a></li>
                            <li><a href="<?php echo getBaseUrl(); ?>faq.php">FAQ</a></li>
                        </ul>
                    </div>
                </div>
                
                <!-- Popular Categories -->
                <div class="col-lg-3 col-md-6">
                    <div class="footer-widget">
                        <h3>Popular Categories</h3>
                        <ul class="footer-links">
                            <?php foreach ($footerCategories as $category): ?>
                                <li><a href="<?php echo getCategoryUrl($category['slug']); ?>"><?php echo $category['category_name']; ?></a></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>
                
                <!-- Recent Posts -->
                <div class="col-lg-3 col-md-6">
                    <div class="footer-widget">
                        <h3>Recent Posts</h3>
                        <ul class="footer-posts">
                            <?php foreach ($footerPosts as $post): ?>
                                <li>
                                    <a href="<?php echo getBlogPostUrl($post['slug']); ?>">
                                        <div class="post-image">
                                            <?php if (!empty($post['featured_image'])): ?>
                                                <img src="<?php echo getUploadUrl($post['featured_image']); ?>" alt="<?php echo $post['title']; ?>">
                                            <?php else: ?>
                                                <img src="<?php echo getAssetUrl('images/blog-default.jpg'); ?>" alt="<?php echo $post['title']; ?>">
                                            <?php endif; ?>
                                        </div>
                                        <div class="post-content">
                                            <h4><?php echo truncate($post['title'], 40); ?></h4>
                                            <span><i class="far fa-calendar"></i> <?php echo formatDate($post['published_at']); ?></span>
                                        </div>
                                    </a>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Footer Middle -->
    <div class="footer-middle bg-dark-light text-light py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <div class="contact-info">
                        <h3>Contact Information</h3>
                        <ul>
                            <li><i class="fas fa-map-marker-alt"></i> <?php echo $settings->getContactAddress(); ?></li>
                            <li><i class="fas fa-phone"></i> <?php echo $settings->getContactPhone(); ?></li>
                            <li><i class="fas fa-envelope"></i> <?php echo $settings->getContactEmail(); ?></li>
                        </ul>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="newsletter">
                        <h3>Subscribe to Newsletter</h3>
                        <p>Stay updated with our latest news and offers</p>
                        <form action="subscribe.php" method="POST" class="newsletter-form">
                            <div class="input-group">
                                <input type="email" name="email" class="form-control" placeholder="Your Email Address" required>
                                <button type="submit" class="btn btn-primary">Subscribe</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Footer Bottom -->
    <div class="footer-bottom bg-darker text-light py-3">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="copyright">
                        <p><?php echo $settings->getFooterText(); ?></p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="footer-bottom-links text-md-end">
                        <a href="<?php echo getBaseUrl(); ?>terms.php">Terms & Conditions</a>
                        <a href="<?php echo getBaseUrl(); ?>privacy.php">Privacy Policy</a>
                        <a href="<?php echo getBaseUrl(); ?>sitemap.php">Sitemap</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</footer>

<!-- Back to Top Button -->
<a href="#" class="back-to-top" id="backToTop"><i class="fas fa-arrow-up"></i></a>

<!-- Google Analytics -->
<?php if (!empty($settings->getGoogleAnalytics())): ?>
    <?php echo $settings->getGoogleAnalytics(); ?>
<?php endif; ?>