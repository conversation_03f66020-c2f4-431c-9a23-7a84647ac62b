<?php
/**
 * City Class
 * Handles operations related to cities
 */
class City {
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Get all cities
     * @param array $filters Optional filters
     * @param int $page Page number
     * @param int $limit Records per page
     * @return array Cities and pagination info
     */
    public function getAll($filters = [], $page = 1, $limit = 10) {
        $where = "1=1";
        $params = [];
        
        // Apply filters
        if (isset($filters['status']) && !empty($filters['status'])) {
            $where .= " AND c.status = ?";
            $params[] = $filters['status'];
        }
        
        if (isset($filters['state_id']) && !empty($filters['state_id'])) {
            $where .= " AND c.state_id = ?";
            $params[] = $filters['state_id'];
        }
        
        if (isset($filters['search']) && !empty($filters['search'])) {
            $where .= " AND (c.city_name LIKE ? OR s.state_name LIKE ?)";
            $params[] = "%{$filters['search']}%";
            $params[] = "%{$filters['search']}%";
        }
        
        // Count total records
        $countSql = "SELECT COUNT(*) as count FROM cities c 
                     LEFT JOIN states s ON c.state_id = s.state_id 
                     WHERE {$where}";
        $countResult = $this->db->fetchRow($countSql, $params);
        $totalCount = $countResult['count'] ?? 0;
        $totalPages = ceil($totalCount / $limit);
        
        // Ensure page is within valid range
        $page = max(1, min($page, $totalPages));
        $offset = ($page - 1) * $limit;
        
        // Get cities
        $sql = "SELECT c.*, s.state_name 
                FROM cities c 
                LEFT JOIN states s ON c.state_id = s.state_id 
                WHERE {$where} 
                ORDER BY c.city_name ASC 
                LIMIT ?, ?";
        $params[] = $offset;
        $params[] = $limit;
        
        $cities = $this->db->fetchAll($sql, $params);
        
        return [
            'cities' => $cities,
            'total_count' => $totalCount,
            'total_pages' => $totalPages,
            'current_page' => $page
        ];
    }
    
    /**
     * Get all cities for dropdown
     * @param int $stateId State ID (optional)
     * @param string $status Status filter (optional)
     * @return array Cities
     */
    public function getAllForDropdown($stateId = null, $status = 'active') {
        $sql = "SELECT city_id, city_name FROM cities";
        $params = [];
        $where = [];
        
        if (!empty($status)) {
            $where[] = "status = ?";
            $params[] = $status;
        }
        
        if (!empty($stateId)) {
            $where[] = "state_id = ?";
            $params[] = $stateId;
        }
        
        if (!empty($where)) {
            $sql .= " WHERE " . implode(' AND ', $where);
        }
        
        $sql .= " ORDER BY city_name ASC";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Get cities by state ID
     * @param int $stateId State ID
     * @param string $status Status filter (optional)
     * @return array Cities
     */
    public function getByStateId($stateId, $status = 'active') {
        // Validate state ID
        $stateId = (int)$stateId;
        if ($stateId <= 0) {
            return [];
        }
        
        // Build query
        $sql = "SELECT city_id, city_name, state_id, status FROM cities WHERE state_id = ?";
        $params = [$stateId];
        
        if (!empty($status)) {
            $sql .= " AND status = ?";
            $params[] = $status;
        }
        
        $sql .= " ORDER BY city_name ASC";
        
        // Log the query for debugging
        error_log("City query: $sql with params: " . json_encode($params));
        
        // Execute query and return results
        $result = $this->db->fetchAll($sql, $params);
        error_log("City query result count: " . count($result));
        
        return $result;
    }
    
    /**
     * Get city by ID
     * @param int $cityId City ID
     * @return array|null City data
     */
    public function getById($cityId) {
        $sql = "SELECT c.*, s.state_name 
                FROM cities c 
                LEFT JOIN states s ON c.state_id = s.state_id 
                WHERE c.city_id = ?";
        return $this->db->fetchRow($sql, [$cityId]);
    }
    
    /**
     * Create new city
     * @param array $data City data
     * @return int|bool New city ID or false on failure
     */
    public function create($data) {
        return $this->db->insert('cities', $data);
    }
    
    /**
     * Update city
     * @param int $cityId City ID
     * @param array $data City data
     * @return bool Success or failure
     */
    public function update($cityId, $data) {
        return $this->db->update('cities', $data, 'city_id = ?', [$cityId]);
    }
    
    /**
     * Delete city
     * @param int $cityId City ID
     * @return bool Success or failure
     */
    public function delete($cityId) {
        return $this->db->delete('cities', 'city_id = ?', [$cityId]);
    }
    
    /**
     * Get count by status
     * @param string $status Status
     * @return int Count
     */
    public function getCountByStatus($status) {
        return $this->db->count('cities', 'status = ?', [$status]);
    }
}