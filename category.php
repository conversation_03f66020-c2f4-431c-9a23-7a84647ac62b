<?php
/**
 * Category Page
 * Shows coaching centers in a specific category
 */
require_once 'includes/autoload.php';

// Get settings
$settings = Settings::getInstance();

// No dummy data - using database only

// Get category slug from URL
$slug = isset($_GET['slug']) ? $_GET['slug'] : '';

// Remove any query parameters from the slug
$slug = preg_replace('/\?.*$/', '', $slug);

// Extract slug from URL if it's not in $_GET
if (empty($slug)) {
    // Try to extract from REQUEST_URI for rewrite rules
    $uri = $_SERVER['REQUEST_URI'];
    if (preg_match('#/category/([^/?]+)#', $uri, $matches)) {
        $slug = $matches[1];
    }
}

// Debug the slug
error_log("Processing category page for slug: " . $slug);

if (empty($slug)) {
    redirect('categories.php');
}

// Get category details from database
$categoryObj = new Category();
$currentCategory = $categoryObj->getBySlug($slug);

// Debug the category data
// (debug removed)

// If category not found, redirect to categories page
if (empty($currentCategory)) {
    error_log("Category not found for slug: " . $slug);
    redirect('categories.php');
}





// Get coaching centers from database
$coachingObj = new CoachingCenter();
$result = $coachingObj->getByCategory($currentCategory['category_id']);

// Extract coaching centers and pagination info
$coachings = isset($result['coachings']) ? $result['coachings'] : [];
$pagination = isset($result['pagination']) ? $result['pagination'] : null;

// Page title and meta
$pageTitle = $currentCategory['category_name'];
$pageDescription = 'Find the best ' . $currentCategory['category_name'] . ' coaching centers on ' . $settings->getSiteName();
$pageKeywords = $currentCategory['category_name'] . ', coaching, ' . $settings->getMetaKeywords();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?php echo getMetaTags($pageTitle . ' - ' . $settings->getSiteName(), $pageDescription, $pageKeywords); ?>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo getAssetUrl($settings->getSiteFavicon()); ?>" type="image/x-icon">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo getAssetUrl('css/style.css'); ?>">
</head>
<body>

    
    <!-- Header -->
    <?php include 'templates/header.php'; ?>
    
    <!-- Main Content -->
    <main>
        <!-- Page Header -->
        <section class="page-header">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <h1 class="page-title"><?php echo $currentCategory['category_name']; ?> Coaching Centers</h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="index.php">Home</a></li>
                                <li class="breadcrumb-item"><a href="<?php echo getBaseUrl(); ?>categories">Categories</a></li>
                                <li class="breadcrumb-item active" aria-current="page"><?php echo $currentCategory['category_name']; ?></li>
                            </ol>
                        </nav>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Category Description -->
        <section class="category-description section-padding">
            <div class="container">
                <div class="row">
                    <div class="col-md-12">
                        <div class="category-info">
                            <div class="row align-items-center">
                                <div class="col-md-2">
                                    <div class="category-icon-large">
                                        <?php if (!empty($currentCategory['icon'])): ?>
                                            <img src="<?php echo getAssetUrl($currentCategory['icon']); ?>" alt="<?php echo $currentCategory['category_name']; ?>">
                                        <?php else: ?>
                                            <i class="fas fa-graduation-cap"></i>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="col-md-10">
                                    <div class="category-content">
                                        <h2><?php echo $currentCategory['category_name']; ?></h2>
                                        <p class="category-description-text">
                                            <?php echo !empty($currentCategory['description']) ? $currentCategory['description'] : 'Find the best ' . $currentCategory['category_name'] . ' coaching centers with experienced faculty, comprehensive study material, and proven track record of success.'; ?>
                                        </p>
                                        <div class="category-stats">
                                            <span class="stat-item">
                                                <i class="fas fa-building"></i>
                                                <?php echo isset($currentCategory['coaching_count']) ? $currentCategory['coaching_count'] : count($coachings); ?> Coaching Centers
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Coaching Centers Section -->
        <section class="coaching-centers-section section-padding bg-light">
            <div class="container">
                <div class="row">
                    <div class="col-md-12">
                        <div class="section-header">
                            <h2>Best <?php echo $currentCategory['category_name']; ?> Coaching Centers</h2>
                            <p>Discover top-rated coaching institutes for <?php echo strtolower($currentCategory['category_name']); ?></p>
                        </div>
                    </div>
                </div>
                
                <?php if (!empty($coachings)): ?>
                    <div class="coaching-list">
                        <?php foreach ($coachings as $index => $coaching): ?>
                            <div class="coaching-list-item">
                                <div class="coaching-card">
                                    <div class="row">
                                        <div class="col-lg-3 col-md-4">
                                            <div class="coaching-image">
                                                <img src="<?php echo !empty($coaching['logo']) ? getUploadUrl($coaching['logo']) : getAssetUrl('images/dummy/coaching-' . (($index % 6) + 1) . '.png'); ?>" alt="<?php echo $coaching['coaching_name']; ?>" class="img-fluid">
                                                <?php if (isset($coaching['is_featured']) && $coaching['is_featured']): ?>
                                                    <span class="featured-badge"><i class="fas fa-star"></i> Featured</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <div class="col-lg-6 col-md-5">
                                            <div class="coaching-content">
                                                <h3 class="coaching-title">
                                                    <a href="<?php echo getCoachingUrl($coaching['slug']); ?>"><?php echo $coaching['coaching_name']; ?></a>
                                                </h3>
                                                <div class="coaching-meta">
                                                    <div class="meta-item">
                                                        <i class="fas fa-map-marker-alt"></i>
                                                        <span><?php echo isset($coaching['city_name']) ? $coaching['city_name'] : 'N/A'; ?><?php echo isset($coaching['state_name']) ? ', ' . $coaching['state_name'] : ''; ?></span>
                                                    </div>
                                                    <div class="meta-item">
                                                        <i class="fas fa-star"></i>
                                                        <span><?php echo isset($coaching['avg_rating']) ? number_format($coaching['avg_rating'], 1) : '4.5'; ?> (<?php echo isset($coaching['total_reviews']) ? $coaching['total_reviews'] : '0'; ?> reviews)</span>
                                                    </div>
                                                    <?php if (isset($coaching['established_year'])): ?>
                                                        <div class="meta-item">
                                                            <i class="fas fa-calendar-alt"></i>
                                                            <span>Est. <?php echo $coaching['established_year']; ?></span>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="coaching-categories">
                                                    <?php if (isset($coaching['categories']) && is_array($coaching['categories'])): ?>
                                                        <?php foreach (array_slice($coaching['categories'], 0, 3) as $cat): ?>
                                                            <a href="<?php echo getCategoryUrl($cat['category_slug']); ?>" class="category-badge"><?php echo $cat['category_name']; ?></a>
                                                        <?php endforeach; ?>
                                                    <?php endif; ?>
                                                </div>
                                                <p class="coaching-description">
                                                    <?php echo isset($coaching['description']) ? substr(strip_tags($coaching['description']), 0, 150) . '...' : 'Quality coaching with experienced faculty and comprehensive study material.'; ?>
                                                </p>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-md-3">
                                            <div class="coaching-actions">
                                                <div class="coaching-contact">
                                                    <?php if (isset($coaching['phone'])): ?>
                                                        <p class="contact-item">
                                                            <i class="fas fa-phone"></i>
                                                            <a href="tel:<?php echo $coaching['phone']; ?>"><?php echo $coaching['phone']; ?></a>
                                                        </p>
                                                    <?php endif; ?>
                                                    <?php if (isset($coaching['email'])): ?>
                                                        <p class="contact-item">
                                                            <i class="fas fa-envelope"></i>
                                                            <a href="mailto:<?php echo $coaching['email']; ?>"><?php echo $coaching['email']; ?></a>
                                                        </p>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="action-buttons">
                                                    <a href="<?php echo getCoachingUrl($coaching['slug']); ?>" class="btn btn-primary btn-sm hover-glow">
                                                        <i class="fas fa-eye"></i> View Details
                                                    </a>
                                                    <?php if (isset($coaching['website']) && !empty($coaching['website'])): ?>
                                                        <a href="<?php echo $coaching['website']; ?>" target="_blank" class="btn btn-outline-primary btn-sm">
                                                            <i class="fas fa-external-link-alt"></i> Website
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if (isset($pagination) && $pagination['total_pages'] > 1): ?>
                        <div class="pagination-wrapper">
                            <nav aria-label="Coaching centers pagination">
                                <ul class="pagination justify-content-center">
                                    <?php if ($pagination['current_page'] > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $pagination['current_page'] - 1; ?>">
                                                <i class="fas fa-chevron-left"></i> Previous
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                    
                                    <?php for ($i = 1; $i <= $pagination['total_pages']; $i++): ?>
                                        <li class="page-item <?php echo $i == $pagination['current_page'] ? 'active' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                                        </li>
                                    <?php endfor; ?>
                                    
                                    <?php if ($pagination['current_page'] < $pagination['total_pages']): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $pagination['current_page'] + 1; ?>">
                                                Next <i class="fas fa-chevron-right"></i>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="no-results">
                        <div class="text-center">
                            <i class="fas fa-search fa-3x text-muted mb-3"></i>
                            <h3>No Coaching Centers Found</h3>
                            <p>We couldn't find any coaching centers in this category at the moment.</p>
                            <a href="<?php echo getBaseUrl(); ?>search.php" class="btn btn-primary">
                                <i class="fas fa-search"></i> Search All Categories
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </section>
 
    </main>
    
    <!-- Footer -->
    <?php include 'templates/footer.php'; ?>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="<?php echo getAssetUrl('js/main.js'); ?>"></script>
</body>
</html>
