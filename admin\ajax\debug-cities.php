<?php
/**
 * Debug Cities
 */
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: text/plain');

try {
    require_once '../../includes/autoload.php';
    
    // Get state ID from GET
    $stateId = isset($_GET['state_id']) ? (int)$_GET['state_id'] : 0;
    
    echo "State ID: {$stateId}\n\n";
    
    // Get cities for the state
    $cityObj = new City();
    
    // Get raw SQL
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    // Check if there are any cities in the database
    $result = $conn->query("SELECT COUNT(*) as count FROM cities");
    $row = $result->fetch_assoc();
    echo "Total cities in database: {$row['count']}\n\n";
    
    // Check if there are any cities for this state
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM cities WHERE state_id = ?");
    $stmt->bind_param("i", $stateId);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    echo "Cities for state {$stateId}: {$row['count']}\n\n";
    
    // Get all cities for this state
    $stmt = $conn->prepare("SELECT * FROM cities WHERE state_id = ?");
    $stmt->bind_param("i", $stateId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    echo "Cities for state {$stateId}:\n";
    while ($row = $result->fetch_assoc()) {
        echo "- {$row['city_id']}: {$row['city_name']} (status: {$row['status']})\n";
    }
    echo "\n";
    
    // Try using the getAll method
    $citiesData = $cityObj->getAll(['state_id' => $stateId]);
    
    echo "Cities from getAll method:\n";
    echo "Total count: " . ($citiesData['total_count'] ?? 'N/A') . "\n";
    echo "Cities: " . print_r($citiesData['cities'] ?? [], true) . "\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>