<?php
/**
 * AJAX Mark Enquiry as Read
 */
error_reporting(E_ALL);
ini_set('display_errors', 0);

header('Content-Type: application/json');

try {
    require_once '../../includes/autoload.php';
    
    // Check user authentication
    $user = new User();
    if (!$user->isLoggedIn() || !$user->isAdmin()) {
        throw new Exception('Unauthorized access');
    }
    
    // Get enquiry ID from GET
    $enquiryId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    
    if ($enquiryId <= 0) {
        throw new Exception('Invalid enquiry ID');
    }
    
    // Mark enquiry as read
    $enquiryObj = new Enquiry();
    $success = $enquiryObj->update($enquiryId, ['status' => 'read']);
    
    // Return result as JSON
    echo json_encode([
        'success' => $success,
        'message' => $success ? 'Enquiry marked as read' : 'Failed to mark enquiry as read'
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}