<?php
/**
 * Admin Class
 * Handles admin authentication and functionality
 */
class Admin {
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Login admin
     * @param string $email Admin email
     * @param string $password Admin password
     * @param bool $remember Remember login
     * @return bool|string True if login successful, error message otherwise
     */
    public function login($email, $password, $remember = false) {
        // Get admin by email
        $admin = $this->db->fetchRow(
            "SELECT * FROM admins WHERE email = ? AND status = 'active'",
            [$email]
        );
        
        if (!$admin) {
            return 'Invalid email or password.';
        }
        
        // Verify password
        if (!password_verify($password, $admin['password'])) {
            return 'Invalid email or password.';
        }
        
        // Set session
        $_SESSION['admin_id'] = $admin['admin_id'];
        $_SESSION['admin_name'] = $admin['name'];
        $_SESSION['admin_email'] = $admin['email'];
        $_SESSION['admin_role'] = $admin['role'];
        
        // Set remember cookie if requested
        if ($remember) {
            $token = bin2hex(random_bytes(32));
            $expires = time() + (86400 * 30); // 30 days
            
            // Store token in database
            $this->db->update(
                'admins',
                [
                    'remember_token' => $token,
                    'remember_expires' => date('Y-m-d H:i:s', $expires)
                ],
                ['admin_id' => $admin['admin_id']]
            );
            
            // Set cookie
            setcookie('admin_remember', $token, $expires, '/');
        }
        
        // Log activity
        $this->logActivity('login', 'Admin logged in', $admin['admin_id']);
        
        return true;
    }
    
    /**
     * Logout admin
     * @return void
     */
    public function logout() {
        // Log activity
        if (isset($_SESSION['admin_id'])) {
            $this->logActivity('logout', 'Admin logged out', $_SESSION['admin_id']);
        }
        
        // Clear session
        unset($_SESSION['admin_id']);
        unset($_SESSION['admin_name']);
        unset($_SESSION['admin_email']);
        unset($_SESSION['admin_role']);
        
        // Clear remember cookie
        if (isset($_COOKIE['admin_remember'])) {
            setcookie('admin_remember', '', time() - 3600, '/');
        }
    }
    
    /**
     * Check if admin is logged in
     * @return bool True if logged in
     */
    public function isLoggedIn() {
        if (isset($_SESSION['admin_id'])) {
            return true;
        }
        
        // Check remember cookie
        if (isset($_COOKIE['admin_remember'])) {
            $token = $_COOKIE['admin_remember'];
            
            // Get admin by token
            $admin = $this->db->fetchRow(
                "SELECT * FROM admins WHERE remember_token = ? AND remember_expires > NOW() AND status = 'active'",
                [$token]
            );
            
            if ($admin) {
                // Set session
                $_SESSION['admin_id'] = $admin['admin_id'];
                $_SESSION['admin_name'] = $admin['name'];
                $_SESSION['admin_email'] = $admin['email'];
                $_SESSION['admin_role'] = $admin['role'];
                
                // Log activity
                $this->logActivity('auto_login', 'Admin auto-logged in', $admin['admin_id']);
                
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Get admin by ID
     * @param int $adminId Admin ID
     * @return array|null Admin data
     */
    public function getById($adminId) {
        return $this->db->fetchRow(
            "SELECT * FROM admins WHERE admin_id = ?",
            [$adminId]
        );
    }
    
    /**
     * Get all admins
     * @param int $page Page number
     * @param int $limit Records per page
     * @return array Admins
     */
    public function getAll($page = 1, $limit = RECORDS_PER_PAGE) {
        $offset = ($page - 1) * $limit;
        
        return $this->db->fetchAll(
            "SELECT * FROM admins ORDER BY name ASC LIMIT ?, ?",
            [$offset, $limit]
        );
    }
    
    /**
     * Get total count of admins
     * @return int Total count
     */
    public function getTotalCount() {
        $result = $this->db->fetchRow("SELECT COUNT(*) as count FROM admins");
        return $result['count'];
    }
    
    /**
     * Add a new admin
     * @param array $data Admin data
     * @return int|bool Admin ID or false on failure
     */
    public function add($data) {
        // Check if email already exists
        $existingAdmin = $this->db->fetchRow(
            "SELECT * FROM admins WHERE email = ?",
            [$data['email']]
        );
        
        if ($existingAdmin) {
            return false;
        }
        
        // Hash password
        $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        
        // Set created_at
        $data['created_at'] = date('Y-m-d H:i:s');
        
        // Insert admin
        $adminId = $this->db->insert('admins', $data);
        
        if ($adminId) {
            // Log activity
            $this->logActivity('add_admin', 'Added new admin: ' . $data['name'], $_SESSION['admin_id']);
        }
        
        return $adminId;
    }
    
    /**
     * Update admin
     * @param int $adminId Admin ID
     * @param array $data Admin data
     * @return bool True if update successful
     */
    public function update($adminId, $data) {
        // Check if email already exists for another admin
        if (isset($data['email'])) {
            $existingAdmin = $this->db->fetchRow(
                "SELECT * FROM admins WHERE email = ? AND admin_id != ?",
                [$data['email'], $adminId]
            );
            
            if ($existingAdmin) {
                return false;
            }
        }
        
        // Hash password if provided
        if (isset($data['password']) && !empty($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        } else {
            unset($data['password']);
        }
        
        // Set updated_at
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        // Update admin
        $result = $this->db->update('admins', $data, ['admin_id' => $adminId]);
        
        if ($result) {
            // Log activity
            $this->logActivity('update_admin', 'Updated admin: ' . $adminId, $_SESSION['admin_id']);
        }
        
        return $result;
    }
    
    /**
     * Delete admin
     * @param int $adminId Admin ID
     * @return bool True if delete successful
     */
    public function delete($adminId) {
        // Check if admin exists
        $admin = $this->getById($adminId);
        
        if (!$admin) {
            return false;
        }
        
        // Delete admin
        $result = $this->db->delete('admins', ['admin_id' => $adminId]);
        
        if ($result) {
            // Log activity
            $this->logActivity('delete_admin', 'Deleted admin: ' . $admin['name'], $_SESSION['admin_id']);
        }
        
        return $result;
    }
    
    /**
     * Log admin activity
     * @param string $activityType Activity type
     * @param string $description Activity description
     * @param int $userId User ID (admin or coaching)
     * @param string $userType User type (admin or coaching)
     * @return int|bool Activity ID or false on failure
     */
    public function logActivity($activityType, $description, $userId, $userType = 'admin') {
        try {
            // Check if activity_logs table exists
            $tableExists = $this->db->fetchRow("SHOW TABLES LIKE 'activity_logs'");
            
            if (!$tableExists) {
                // Create activity_logs table
                $sql = "CREATE TABLE `activity_logs` (
                    `activity_id` int(11) NOT NULL AUTO_INCREMENT,
                    `activity_type` varchar(50) NOT NULL,
                    `description` text NOT NULL,
                    `user_id` int(11) NOT NULL,
                    `user_type` enum('admin','coaching','user') NOT NULL,
                    `ip_address` varchar(45) DEFAULT NULL,
                    `user_agent` text,
                    `created_at` datetime NOT NULL,
                    PRIMARY KEY (`activity_id`),
                    KEY `user_id` (`user_id`),
                    KEY `user_type` (`user_type`),
                    KEY `activity_type` (`activity_type`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
                
                $this->db->query($sql);
            }
            
            $data = [
                'activity_type' => $activityType,
                'description' => $description,
                'user_id' => $userId,
                'user_type' => $userType,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            return $this->db->insert('activity_logs', $data);
        } catch (Exception $e) {
            // Log the error
            error_log(date('Y-m-d H:i:s') . " - Error in logActivity: " . $e->getMessage() . "\n", 3, BASE_PATH . '/logs/admin_dashboard.log');
            
            // Return false
            return false;
        }
    }
    
    /**
     * Get recent activities
     * @param int $limit Number of activities to get
     * @param string $userType User type (admin, coaching, or all)
     * @return array Recent activities
     */
    public function getRecentActivities($limit = 10, $userType = 'all') {
        try {
            // Check if activity_logs table exists
            $tableExists = $this->db->fetchRow("SHOW TABLES LIKE 'activity_logs'");
            
            if (!$tableExists) {
                // Create activity_logs table
                $sql = "CREATE TABLE `activity_logs` (
                    `activity_id` int(11) NOT NULL AUTO_INCREMENT,
                    `activity_type` varchar(50) NOT NULL,
                    `description` text NOT NULL,
                    `user_id` int(11) NOT NULL,
                    `user_type` enum('admin','coaching','user') NOT NULL,
                    `ip_address` varchar(45) DEFAULT NULL,
                    `user_agent` text,
                    `created_at` datetime NOT NULL,
                    PRIMARY KEY (`activity_id`),
                    KEY `user_id` (`user_id`),
                    KEY `user_type` (`user_type`),
                    KEY `activity_type` (`activity_type`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
                
                $this->db->query($sql);
                
                // Add some sample activity logs
                $activities = [
                    [
                        'activity_type' => 'login',
                        'description' => 'Admin logged in',
                        'user_id' => 1,
                        'user_type' => 'admin',
                        'ip_address' => '127.0.0.1',
                        'user_agent' => 'Mozilla/5.0',
                        'created_at' => date('Y-m-d H:i:s', strtotime('-1 hour'))
                    ],
                    [
                        'activity_type' => 'system',
                        'description' => 'System initialized',
                        'user_id' => 1,
                        'user_type' => 'admin',
                        'ip_address' => '127.0.0.1',
                        'user_agent' => 'Mozilla/5.0',
                        'created_at' => date('Y-m-d H:i:s')
                    ]
                ];
                
                foreach ($activities as $activity) {
                    $this->db->insert('activity_logs', $activity);
                }
            }
            
            $sql = "SELECT al.*, 
                    CASE 
                        WHEN al.user_type = 'admin' THEN COALESCE(a.name, 'Admin')
                        WHEN al.user_type = 'coaching' THEN COALESCE(c.coaching_name, 'Coaching')
                        WHEN al.user_type = 'user' THEN COALESCE(u.username, 'User')
                        ELSE 'Unknown'
                    END as user_name
                    FROM activity_logs al
                    LEFT JOIN admins a ON al.user_id = a.admin_id AND al.user_type = 'admin'
                    LEFT JOIN coaching_centers c ON al.user_id = c.coaching_id AND al.user_type = 'coaching'
                    LEFT JOIN users u ON al.user_id = u.user_id AND (al.user_type = 'user' OR a.name IS NULL)";
            
            $params = [];
            
            if ($userType !== 'all') {
                $sql .= " WHERE al.user_type = ?";
                $params[] = $userType;
            }
            
            $sql .= " ORDER BY al.created_at DESC LIMIT ?";
            $params[] = $limit;
            
            return $this->db->fetchAll($sql, $params);
        } catch (Exception $e) {
            // Log the error
            error_log(date('Y-m-d H:i:s') . " - Error in getRecentActivities: " . $e->getMessage() . "\n", 3, BASE_PATH . '/logs/admin_dashboard.log');
            
            // Return empty array
            return [];
        }
    }
    
    /**
     * Reset admin password
     * @param string $email Admin email
     * @return bool|string True if reset successful, error message otherwise
     */
    public function resetPassword($email) {
        // Get admin by email
        $admin = $this->db->fetchRow(
            "SELECT * FROM admins WHERE email = ? AND status = 'active'",
            [$email]
        );
        
        if (!$admin) {
            return 'Admin not found with this email.';
        }
        
        // Generate reset token
        $token = bin2hex(random_bytes(32));
        $expires = date('Y-m-d H:i:s', time() + 3600); // 1 hour
        
        // Store token in database
        $this->db->update(
            'admins',
            [
                'reset_token' => $token,
                'reset_expires' => $expires
            ],
            ['admin_id' => $admin['admin_id']]
        );
        
        // Send reset email
        $resetUrl = getBaseUrl() . 'admin/reset-password.php?token=' . $token;
        $subject = 'Password Reset Request';
        $message = "Hello {$admin['name']},\n\n";
        $message .= "You have requested to reset your password. Please click the link below to reset your password:\n\n";
        $message .= $resetUrl . "\n\n";
        $message .= "This link will expire in 1 hour.\n\n";
        $message .= "If you did not request this, please ignore this email.\n\n";
        $message .= "Regards,\n";
        $message .= "The " . Settings::getInstance()->getSiteName() . " Team";
        
        $emailSent = sendEmail($admin['email'], $subject, $message);
        
        if (!$emailSent) {
            return 'Failed to send reset email. Please try again.';
        }
        
        // Log activity
        $this->logActivity('reset_password_request', 'Password reset requested for admin: ' . $admin['name'], $admin['admin_id']);
        
        return true;
    }
    
    /**
     * Verify reset token
     * @param string $token Reset token
     * @return array|bool Admin data if token valid, false otherwise
     */
    public function verifyResetToken($token) {
        // Get admin by token
        $admin = $this->db->fetchRow(
            "SELECT * FROM admins WHERE reset_token = ? AND reset_expires > NOW() AND status = 'active'",
            [$token]
        );
        
        if (!$admin) {
            return false;
        }
        
        return $admin;
    }
    
    /**
     * Complete password reset
     * @param string $token Reset token
     * @param string $password New password
     * @return bool True if reset successful
     */
    public function completeReset($token, $password) {
        // Verify token
        $admin = $this->verifyResetToken($token);
        
        if (!$admin) {
            return false;
        }
        
        // Hash password
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        
        // Update admin
        $result = $this->db->update(
            'admins',
            [
                'password' => $hashedPassword,
                'reset_token' => null,
                'reset_expires' => null,
                'updated_at' => date('Y-m-d H:i:s')
            ],
            ['admin_id' => $admin['admin_id']]
        );
        
        if ($result) {
            // Log activity
            $this->logActivity('reset_password_complete', 'Password reset completed for admin: ' . $admin['name'], $admin['admin_id']);
        }
        
        return $result;
    }
    
    /**
     * Check if admin has permission
     * @param string $permission Permission to check
     * @return bool True if admin has permission
     */
    public function hasPermission($permission) {
        if (!isset($_SESSION['admin_id'])) {
            return false;
        }
        
        // Get admin
        $admin = $this->getById($_SESSION['admin_id']);
        
        if (!$admin) {
            return false;
        }
        
        // Super admin has all permissions
        if ($admin['role'] === 'super_admin') {
            return true;
        }
        
        // Get admin permissions
        $permissions = $this->db->fetchAll(
            "SELECT p.permission_name
             FROM admin_permissions ap
             JOIN permissions p ON ap.permission_id = p.permission_id
             WHERE ap.admin_id = ?",
            [$admin['admin_id']]
        );
        
        // Check if admin has the requested permission
        foreach ($permissions as $p) {
            if ($p['permission_name'] === $permission) {
                return true;
            }
        }
        
        return false;
    }
}
