<?php
/**
 * Admin Add Blog Post
 */
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    require_once '../includes/autoload.php';
    
    // Check user authentication
    $user = new User();
    if (!$user->isLoggedIn() || !$user->isAdmin()) {
        header('Location: login.php');
        exit;
    }
    
    // Get admin info
    $adminInfo = $user->getUserData();
    
    // Initialize settings
    $settings = Settings::getInstance();
    
    // Initialize blog object
    $blogObj = new Blog();
    
    // Get categories for dropdown
    $categoryObj = new Category();
    $categories = $categoryObj->getAll(['type' => 'blog', 'status' => 'active']);
    
    // Handle form submission
    $message = '';
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Validate form data
        $errors = [];
        
        // Required fields
        $requiredFields = ['title', 'content'];
        foreach ($requiredFields as $field) {
            if (empty($_POST[$field])) {
                $errors[] = ucfirst($field) . ' is required.';
            }
        }
        
        // If no errors, process form
        if (empty($errors)) {
            // Prepare blog post data
            $postData = [
                'title' => $_POST['title'],
                'slug' => Utility::generateSlug($_POST['title']),
                'content' => $_POST['content'],
                'excerpt' => !empty($_POST['excerpt']) ? $_POST['excerpt'] : substr(strip_tags($_POST['content']), 0, 200) . '...',
                'category_id' => !empty($_POST['category_id']) ? (int)$_POST['category_id'] : null,
                'meta_title' => !empty($_POST['meta_title']) ? $_POST['meta_title'] : $_POST['title'],
                'meta_description' => !empty($_POST['meta_description']) ? $_POST['meta_description'] : substr(strip_tags($_POST['content']), 0, 160),
                'meta_keywords' => !empty($_POST['meta_keywords']) ? $_POST['meta_keywords'] : '',
                'status' => $_POST['status'],
                'author_id' => $adminInfo['user_id'],
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            // Create blog post
            $postId = $blogObj->create($postData);
            
            if ($postId) {
                // Handle featured image upload
                if (!empty($_FILES['featured_image']['name'])) {
                    $uploadDir = '../uploads/blog/';
                    if (!is_dir($uploadDir)) {
                        mkdir($uploadDir, 0755, true);
                    }
                    
                    $fileName = 'post_' . $postId . '_' . time() . '.' . pathinfo($_FILES['featured_image']['name'], PATHINFO_EXTENSION);
                    $targetFile = $uploadDir . $fileName;
                    
                    if (move_uploaded_file($_FILES['featured_image']['tmp_name'], $targetFile)) {
                        $imagePath = 'uploads/blog/' . $fileName;
                        $blogObj->update($postId, ['featured_image' => $imagePath]);
                    }
                }
                
                // Redirect to blog posts page
                header('Location: blog-posts.php?message=created');
                exit;
            } else {
                $message = '<div class="alert alert-danger">Failed to create blog post. Please try again.</div>';
            }
        } else {
            $message = '<div class="alert alert-danger"><ul class="mb-0">';
            foreach ($errors as $error) {
                $message .= '<li>' . $error . '</li>';
            }
            $message .= '</ul></div>';
        }
    }
    
} catch (Exception $e) {
    die('<div style="color:red;padding:20px;border:2px solid red;margin:20px;">'
        . '<h2>Error</h2>'
        . '<p><strong>Message:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>'
        . '<p><strong>File:</strong> ' . htmlspecialchars($e->getFile()) . '</p>'
        . '<p><strong>Line:</strong> ' . htmlspecialchars($e->getLine()) . '</p>'
        . '</div>');
}

// Page title
$pageTitle = 'Add Blog Post';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Admin Panel | <?php echo $settings->getSiteName(); ?></title>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo getAssetUrl($settings->getSiteFavicon()); ?>" type="image/x-icon">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Summernote CSS -->
    <link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs5.min.css" rel="stylesheet">
    
    <!-- Admin CSS -->
    <link rel="stylesheet" href="assets/css/admin.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <?php include 'templates/sidebar.php'; ?>
        
        <!-- Main Content -->
        <div class="admin-main" id="adminMain">
            <!-- Header -->
            <?php include 'templates/header.php'; ?>
            
            <!-- Content -->
            <div class="admin-content">
                <div class="page-title">
                    <div>
                        <h1><?php echo $pageTitle; ?></h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="dashboard.php">Home</a></li>
                                <li class="breadcrumb-item"><a href="blog-posts.php">Blog Posts</a></li>
                                <li class="breadcrumb-item active" aria-current="page"><?php echo $pageTitle; ?></li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <a href="blog-posts.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
                
                <?php echo $message; ?>
                
                <!-- Add Blog Post Form -->
                <div class="admin-card">
                    <div class="admin-card-header">
                        <h2 class="admin-card-title">
                            <i class="fas fa-edit me-2"></i> Blog Post Information
                        </h2>
                    </div>
                    <div class="admin-card-body">
                        <form action="" method="post" enctype="multipart/form-data">
                            <div class="row">
                                <!-- Main Content -->
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="title" name="title" value="<?php echo isset($_POST['title']) ? htmlspecialchars($_POST['title']) : ''; ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="content" class="form-label">Content <span class="text-danger">*</span></label>
                                        <textarea class="form-control summernote" id="content" name="content" rows="10" required><?php echo isset($_POST['content']) ? htmlspecialchars($_POST['content']) : ''; ?></textarea>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="excerpt" class="form-label">Excerpt</label>
                                        <textarea class="form-control" id="excerpt" name="excerpt" rows="3"><?php echo isset($_POST['excerpt']) ? htmlspecialchars($_POST['excerpt']) : ''; ?></textarea>
                                        <small class="text-muted">A short summary of the post. If left empty, it will be automatically generated from the content.</small>
                                    </div>
                                </div>
                                
                                <!-- Sidebar -->
                                <div class="col-md-4">
                                    <div class="admin-card mb-3">
                                        <div class="admin-card-header">
                                            <h3 class="admin-card-title">Publish</h3>
                                        </div>
                                        <div class="admin-card-body">
                                            <div class="mb-3">
                                                <label for="status" class="form-label">Status</label>
                                                <select class="form-select" id="status" name="status">
                                                    <option value="published" <?php echo isset($_POST['status']) && $_POST['status'] === 'published' ? 'selected' : ''; ?>>Published</option>
                                                    <option value="draft" <?php echo isset($_POST['status']) && $_POST['status'] === 'draft' ? 'selected' : ''; ?>>Draft</option>
                                                </select>
                                            </div>
                                            
                                            <div class="d-grid gap-2">
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fas fa-save"></i> Save Post
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="admin-card mb-3">
                                        <div class="admin-card-header">
                                            <h3 class="admin-card-title">Category</h3>
                                        </div>
                                        <div class="admin-card-body">
                                            <div class="mb-3">
                                                <select class="form-select" id="category_id" name="category_id">
                                                    <option value="">Select Category</option>
                                                    <?php foreach ($categories as $category): ?>
                                                        <option value="<?php echo $category['category_id']; ?>" <?php echo isset($_POST['category_id']) && $_POST['category_id'] == $category['category_id'] ? 'selected' : ''; ?>>
                                                            <?php echo htmlspecialchars($category['category_name']); ?>
                                                        </option>
                                                    <?php endforeach; ?>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="admin-card mb-3">
                                        <div class="admin-card-header">
                                            <h3 class="admin-card-title">Featured Image</h3>
                                        </div>
                                        <div class="admin-card-body">
                                            <div class="mb-3">
                                                <input type="file" class="form-control" id="featured_image" name="featured_image" accept="image/*">
                                                <small class="text-muted">Recommended size: 1200x630 pixels</small>
                                            </div>
                                            <div id="imagePreview" class="mt-2 d-none">
                                                <img src="" alt="Preview" class="img-fluid rounded">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="admin-card">
                                        <div class="admin-card-header">
                                            <h3 class="admin-card-title">SEO Settings</h3>
                                        </div>
                                        <div class="admin-card-body">
                                            <div class="mb-3">
                                                <label for="meta_title" class="form-label">Meta Title</label>
                                                <input type="text" class="form-control" id="meta_title" name="meta_title" value="<?php echo isset($_POST['meta_title']) ? htmlspecialchars($_POST['meta_title']) : ''; ?>">
                                                <small class="text-muted">If left empty, the post title will be used.</small>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="meta_description" class="form-label">Meta Description</label>
                                                <textarea class="form-control" id="meta_description" name="meta_description" rows="3"><?php echo isset($_POST['meta_description']) ? htmlspecialchars($_POST['meta_description']) : ''; ?></textarea>
                                                <small class="text-muted">If left empty, an excerpt from the content will be used.</small>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="meta_keywords" class="form-label">Meta Keywords</label>
                                                <input type="text" class="form-control" id="meta_keywords" name="meta_keywords" value="<?php echo isset($_POST['meta_keywords']) ? htmlspecialchars($_POST['meta_keywords']) : ''; ?>">
                                                <small class="text-muted">Separate keywords with commas.</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- Footer -->
            <?php include 'templates/footer.php'; ?>
        </div>
    </div>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Summernote JS -->
    <script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs5.min.js"></script>
    
    <!-- Admin JS -->
    <script src="assets/js/admin.js"></script>
    
    <script>
        $(document).ready(function() {
            // Initialize Summernote
            $('.summernote').summernote({
                height: 300,
                toolbar: [
                    ['style', ['style']],
                    ['font', ['bold', 'underline', 'clear']],
                    ['color', ['color']],
                    ['para', ['ul', 'ol', 'paragraph']],
                    ['table', ['table']],
                    ['insert', ['link', 'picture']],
                    ['view', ['fullscreen', 'codeview', 'help']]
                ],
                callbacks: {
                    onImageUpload: function(files) {
                        // Upload image to server and insert URL
                        for (let i = 0; i < files.length; i++) {
                            uploadImage(files[i], this);
                        }
                    }
                }
            });
            
            // Image preview
            $('#featured_image').change(function() {
                const file = this.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        $('#imagePreview').removeClass('d-none');
                        $('#imagePreview img').attr('src', e.target.result);
                    }
                    reader.readAsDataURL(file);
                } else {
                    $('#imagePreview').addClass('d-none');
                }
            });
            
            // Auto-generate slug from title
            $('#title').keyup(function() {
                const title = $(this).val();
                const slug = title.toLowerCase()
                    .replace(/[^\w\s-]/g, '')
                    .replace(/\s+/g, '-')
                    .replace(/-+/g, '-');
                $('#slug').val(slug);
            });
            
            // Upload image for Summernote
            function uploadImage(file, editor) {
                const formData = new FormData();
                formData.append('file', file);
                
                $.ajax({
                    url: 'ajax/upload-image.php',
                    method: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(data) {
                        if (data.success) {
                            $(editor).summernote('insertImage', data.url);
                        } else {
                            alert('Failed to upload image: ' + data.message);
                        }
                    },
                    error: function() {
                        alert('Failed to upload image. Please try again.');
                    }
                });
            }
        });
    </script>
</body>
</html>