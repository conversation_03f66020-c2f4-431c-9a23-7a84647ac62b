<?php
require_once 'includes/autoload.php';

// Get database instance
$db = Database::getInstance();

// Create course_locations table
$sql = "CREATE TABLE IF NOT EXISTS course_locations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    location_id INT NOT NULL,
    FOREIGN KEY (course_id) REFERENCES courses(course_id) ON DELETE CASCADE,
    FOREIGN KEY (location_id) REFERENCES locations(location_id) ON DELETE CASCADE,
    UNIQUE KEY (course_id, location_id)
)";

$result = $db->query($sql);

if ($result) {
    echo "course_locations table created successfully!";
} else {
    echo "Error creating course_locations table.";
}

// Update database_schema.sql file to include coaching_locations and course_locations tables
$schemaFile = file_get_contents('database_schema.sql');

// Add coaching_locations table if not already in schema
if (strpos($schemaFile, 'coaching_locations') === false) {
    $coachingLocationsSchema = "
-- Coaching center locations mapping
CREATE TABLE IF NOT EXISTS coaching_locations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    coaching_id INT NOT NULL,
    location_id INT NOT NULL,
    FOREIGN KEY (coaching_id) REFERENCES coaching_centers(coaching_id) ON DELETE CASCADE,
    FOREIGN KEY (location_id) REFERENCES locations(location_id) ON DELETE CASCADE
);
";
    
    // Find a good place to insert the new table definition (after coaching_facilities)
    $pos = strpos($schemaFile, 'coaching_facilities');
    if ($pos !== false) {
        $pos = strpos($schemaFile, ');', $pos);
        if ($pos !== false) {
            $schemaFile = substr_replace($schemaFile, ");\n" . $coachingLocationsSchema, $pos, 2);
        }
    }
}

// Add course_locations table if not already in schema
if (strpos($schemaFile, 'course_locations') === false) {
    $courseLocationsSchema = "
-- Course locations mapping
CREATE TABLE IF NOT EXISTS course_locations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    location_id INT NOT NULL,
    FOREIGN KEY (course_id) REFERENCES courses(course_id) ON DELETE CASCADE,
    FOREIGN KEY (location_id) REFERENCES locations(location_id) ON DELETE CASCADE,
    UNIQUE KEY (course_id, location_id)
);
";
    
    // Find a good place to insert the new table definition (after courses)
    $pos = strpos($schemaFile, 'courses');
    if ($pos !== false) {
        $pos = strpos($schemaFile, ');', $pos);
        if ($pos !== false) {
            $schemaFile = substr_replace($schemaFile, ");\n" . $courseLocationsSchema, $pos, 2);
        }
    }
}

// Write updated schema back to file
file_put_contents('database_schema.sql', $schemaFile);
echo "\nDatabase schema file updated successfully!";