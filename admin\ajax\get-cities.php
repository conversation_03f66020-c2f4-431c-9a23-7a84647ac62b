<?php
/**
 * AJAX Get Cities by State ID
 */
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set appropriate headers
header('Content-Type: application/json');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

try {
    require_once '../../includes/autoload.php';
    
    // Check user authentication
    $user = new User();
    if (!$user->isLoggedIn() || !$user->isAdmin()) {
        throw new Exception('Unauthorized access');
    }
    
    // Get state ID from POST
    $stateId = isset($_POST['state_id']) ? (int)$_POST['state_id'] : 0;
    
    if ($stateId <= 0) {
        throw new Exception('Invalid state ID');
    }
    
    // Get cities for the state
    $cityObj = new City();
    
    // Use the getByStateId method
    $cities = $cityObj->getByStateId($stateId, 'active');
    
    // Log the result for debugging
    error_log("Cities for state ID $stateId: " . json_encode($cities));
    
    // Ensure we have an array even if no cities are found
    if (!is_array($cities)) {
        $cities = [];
    }
    
    // Return cities as JSON
    echo json_encode($cities);
    
} catch (Exception $e) {
    error_log("Error in get-cities.php: " . $e->getMessage());
    echo json_encode([
        'error' => true,
        'message' => $e->getMessage()
    ]);
}