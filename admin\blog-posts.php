<?php
/**
 * Admin Blog Posts
 */
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    require_once '../includes/autoload.php';
    
    // Check user authentication
    $user = new User();
    if (!$user->isLoggedIn() || !$user->isAdmin()) {
        header('Location: login.php');
        exit;
    }
    
    // Get admin info
    $adminInfo = $user->getUserData();
    
    // Initialize settings
    $settings = Settings::getInstance();
    
    // Initialize blog object
    $blogObj = new Blog();
    
    // Handle actions
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    $postId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    $message = '';
    
    if ($action === 'delete' && $postId > 0) {
        if ($blogObj->delete($postId)) {
            $message = '<div class="alert alert-success">Blog post deleted successfully.</div>';
        } else {
            $message = '<div class="alert alert-danger">Failed to delete blog post.</div>';
        }
    } else if ($action === 'publish' && $postId > 0) {
        if ($blogObj->update($postId, ['status' => 'published'])) {
            $message = '<div class="alert alert-success">Blog post published successfully.</div>';
        } else {
            $message = '<div class="alert alert-danger">Failed to publish blog post.</div>';
        }
    } else if ($action === 'draft' && $postId > 0) {
        if ($blogObj->update($postId, ['status' => 'draft'])) {
            $message = '<div class="alert alert-success">Blog post moved to draft successfully.</div>';
        } else {
            $message = '<div class="alert alert-danger">Failed to update blog post status.</div>';
        }
    }
    
    // Get blog posts with pagination
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = 10;
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    $categoryId = isset($_GET['category_id']) ? (int)$_GET['category_id'] : 0;
    $search = isset($_GET['search']) ? $_GET['search'] : '';
    
    // Prepare filters
    $filters = [];
    if (!empty($status)) {
        $filters['status'] = $status;
    }
    if ($categoryId > 0) {
        $filters['category_id'] = $categoryId;
    }
    if (!empty($search)) {
        $filters['search'] = $search;
    }
    
    // Get blog posts
    $result = $blogObj->getPosts($filters, $page, $limit);
    $posts = $result['posts'] ?? [];
    $totalPages = $result['total_pages'] ?? 1;
    $totalCount = $result['total_count'] ?? 0;
    
    // Get categories for filter dropdown
    $categoryObj = new Category();
    $categories = $categoryObj->getAll(['type' => 'blog']);
    
} catch (Exception $e) {
    die('<div style="color:red;padding:20px;border:2px solid red;margin:20px;">'
        . '<h2>Error</h2>'
        . '<p><strong>Message:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>'
        . '<p><strong>File:</strong> ' . htmlspecialchars($e->getFile()) . '</p>'
        . '<p><strong>Line:</strong> ' . htmlspecialchars($e->getLine()) . '</p>'
        . '</div>');
}

// Page title
$pageTitle = 'Blog Posts';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Admin Panel | <?php echo $settings->getSiteName(); ?></title>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo getAssetUrl($settings->getSiteFavicon()); ?>" type="image/x-icon">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Admin CSS -->
    <link rel="stylesheet" href="assets/css/admin.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <?php include 'templates/sidebar.php'; ?>
        
        <!-- Main Content -->
        <div class="admin-main" id="adminMain">
            <!-- Header -->
            <?php include 'templates/header.php'; ?>
            
            <!-- Content -->
            <div class="admin-content">
                <div class="page-title">
                    <div>
                        <h1><?php echo $pageTitle; ?></h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="dashboard.php">Home</a></li>
                                <li class="breadcrumb-item">Blog</li>
                                <li class="breadcrumb-item active" aria-current="page"><?php echo $pageTitle; ?></li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <a href="blog-add.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add New Post
                        </a>
                    </div>
                </div>
                
                <?php echo $message; ?>
                
                <!-- Filters -->
                <div class="admin-card mb-4">
                    <div class="admin-card-body">
                        <form action="" method="get" class="row g-3">
                            <div class="col-md-3">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" class="form-control" id="search" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="Search by title...">
                            </div>
                            <div class="col-md-3">
                                <label for="category_id" class="form-label">Category</label>
                                <select class="form-select" id="category_id" name="category_id">
                                    <option value="">All Categories</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['category_id']; ?>" <?php echo $categoryId == $category['category_id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($category['category_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">All Status</option>
                                    <option value="published" <?php echo $status === 'published' ? 'selected' : ''; ?>>Published</option>
                                    <option value="draft" <?php echo $status === 'draft' ? 'selected' : ''; ?>>Draft</option>
                                </select>
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <a href="blog-posts.php" class="btn btn-secondary w-100">
                                    <i class="fas fa-sync-alt"></i> Reset
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Blog Posts Table -->
                <div class="admin-card">
                    <div class="admin-card-header">
                        <h2 class="admin-card-title">
                            <i class="fas fa-blog me-2"></i> All Blog Posts
                        </h2>
                        <span class="badge bg-primary"><?php echo $totalCount; ?> Total</span>
                    </div>
                    <div class="admin-card-body p-0">
                        <div class="table-responsive">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Title</th>
                                        <th>Category</th>
                                        <th>Author</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($posts)): ?>
                                        <?php foreach ($posts as $post): ?>
                                            <tr>
                                                <td><?php echo $post['post_id']; ?></td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <?php if (!empty($post['featured_image'])): ?>
                                                            <img src="<?php echo getAssetUrl($post['featured_image']); ?>" alt="<?php echo htmlspecialchars($post['title']); ?>" class="me-2" style="width: 50px; height: 40px; object-fit: cover; border-radius: 4px;">
                                                        <?php else: ?>
                                                            <div class="placeholder-image me-2" style="width: 50px; height: 40px; background-color: #e9ecef; border-radius: 4px; display: flex; align-items: center; justify-content: center;">
                                                                <i class="fas fa-image text-secondary"></i>
                                                            </div>
                                                        <?php endif; ?>
                                                        <div>
                                                            <div class="fw-bold"><?php echo htmlspecialchars($post['title']); ?></div>
                                                            <small class="text-muted"><?php echo substr(strip_tags($post['excerpt']), 0, 50) . '...'; ?></small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td><?php echo htmlspecialchars($post['category_name'] ?? 'Uncategorized'); ?></td>
                                                <td><?php echo htmlspecialchars($post['author_name']); ?></td>
                                                <td>
                                                    <?php if ($post['status'] === 'published'): ?>
                                                        <span class="badge bg-success">Published</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-warning">Draft</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo date('M d, Y', strtotime($post['created_at'])); ?></td>
                                                <td>
                                                    <div class="btn-group">
                                                        <a href="blog-edit.php?id=<?php echo $post['post_id']; ?>" class="btn btn-sm btn-primary" title="Edit">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <a href="../blog/<?php echo $post['slug']; ?>" target="_blank" class="btn btn-sm btn-info" title="View">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <?php if ($post['status'] === 'draft'): ?>
                                                            <a href="blog-posts.php?action=publish&id=<?php echo $post['post_id']; ?>" class="btn btn-sm btn-success" title="Publish" onclick="return confirm('Are you sure you want to publish this post?');">
                                                                <i class="fas fa-check"></i>
                                                            </a>
                                                        <?php else: ?>
                                                            <a href="blog-posts.php?action=draft&id=<?php echo $post['post_id']; ?>" class="btn btn-sm btn-warning" title="Move to Draft" onclick="return confirm('Are you sure you want to move this post to draft?');">
                                                                <i class="fas fa-file"></i>
                                                            </a>
                                                        <?php endif; ?>
                                                        <a href="blog-posts.php?action=delete&id=<?php echo $post['post_id']; ?>" class="btn btn-sm btn-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this post? This action cannot be undone.');">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="7" class="text-center">No blog posts found.</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <div class="admin-card-footer">
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center mb-0">
                                    <?php if ($page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $page - 1; ?>&status=<?php echo urlencode($status); ?>&category_id=<?php echo $categoryId; ?>&search=<?php echo urlencode($search); ?>" aria-label="Previous">
                                                <span aria-hidden="true">&laquo;</span>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                    
                                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo urlencode($status); ?>&category_id=<?php echo $categoryId; ?>&search=<?php echo urlencode($search); ?>">
                                                <?php echo $i; ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>
                                    
                                    <?php if ($page < $totalPages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $page + 1; ?>&status=<?php echo urlencode($status); ?>&category_id=<?php echo $categoryId; ?>&search=<?php echo urlencode($search); ?>" aria-label="Next">
                                                <span aria-hidden="true">&raquo;</span>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Footer -->
            <?php include 'templates/footer.php'; ?>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Admin JS -->
    <script src="assets/js/admin.js"></script>
</body>
</html>