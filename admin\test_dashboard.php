<?php
/**
 * Test Dashboard Script
 */
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Testing Dashboard Components</h1>";

try {
    require_once '../includes/autoload.php';
    
    echo "<h2>Testing CoachingCenter getRecent method</h2>";
    $coachingObj = new CoachingCenter();
    $recentCoachings = $coachingObj->getRecent(5);
    echo "✓ CoachingCenter getRecent works - Found: " . count($recentCoachings) . " coaching centers<br>";
    
    echo "<h2>Testing Review getRecent method</h2>";
    $reviewObj = new Review();
    $recentReviews = $reviewObj->getRecent(5);
    echo "✓ Review getRecent works - Found: " . count($recentReviews) . " reviews<br>";
    
    echo "<h2>Testing Enquiry getRecent method</h2>";
    $enquiryObj = new Enquiry();
    $recentEnquiries = $enquiryObj->getRecent(5);
    echo "✓ Enquiry getRecent works - Found: " . count($recentEnquiries) . " enquiries<br>";
    
    echo "<h2>All methods working!</h2>";
    echo "<p><a href='dashboard.php'>Try Dashboard Now</a></p>";
    
} catch (Exception $e) {
    echo '<div style="color:red;padding:20px;border:2px solid red;margin:20px;">'
        . '<h2>Error Found!</h2>'
        . '<p><strong>Error:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>'
        . '<p><strong>File:</strong> ' . htmlspecialchars($e->getFile()) . '</p>'
        . '<p><strong>Line:</strong> ' . htmlspecialchars($e->getLine()) . '</p>'
        . '<p><strong>Stack Trace:</strong></p>'
        . '<pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre>'
        . '</div>';
}
?>