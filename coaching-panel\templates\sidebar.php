<?php
// Get current page
$currentPage = basename($_SERVER['PHP_SELF']);

// Define menu items
$menuItems = [
    [
        'title' => 'Dashboard',
        'icon' => 'fas fa-tachometer-alt',
        'url' => 'index.php',
        'active' => $currentPage === 'index.php'
    ],
    [
        'title' => 'Profile',
        'icon' => 'fas fa-user-circle',
        'url' => 'profile.php',
        'active' => $currentPage === 'profile.php'
    ],
    [
        'title' => 'Courses',
        'icon' => 'fas fa-book',
        'url' => 'courses.php',
        'active' => $currentPage === 'courses.php' || $currentPage === 'add-course.php' || $currentPage === 'edit-course.php'
    ],
    [
        'title' => 'Success Stories',
        'icon' => 'fas fa-trophy',
        'url' => 'success-stories.php',
        'active' => $currentPage === 'success-stories.php' || $currentPage === 'add-success-story.php' || $currentPage === 'edit-success-story.php'
    ],
    [
        'title' => 'Gallery',
        'icon' => 'fas fa-images',
        'url' => 'gallery.php',
        'active' => $currentPage === 'gallery.php'
    ],
    [
        'title' => 'Locations',
        'icon' => 'fas fa-map-marker-alt',
        'url' => 'locations.php',
        'active' => $currentPage === 'locations.php'
    ],
    [
        'title' => 'Enquiries',
        'icon' => 'fas fa-envelope',
        'url' => 'enquiries.php',
        'active' => $currentPage === 'enquiries.php' || $currentPage === 'view-enquiry.php'
    ],
    [
        'title' => 'Reviews',
        'icon' => 'fas fa-star',
        'url' => 'reviews.php',
        'active' => $currentPage === 'reviews.php'
    ],
    [
        'title' => 'Subscription',
        'icon' => 'fas fa-credit-card',
        'url' => 'subscription.php',
        'active' => $currentPage === 'subscription.php'
    ],
    [
        'title' => 'Settings',
        'icon' => 'fas fa-cog',
        'url' => 'settings.php',
        'active' => $currentPage === 'settings.php'
    ]
];
?>

<div class="sidebar">
    <div class="sidebar-header">
        <div class="logo">
            <a href="index.php">
                <img src="../assets/images/logo.png" alt="Coaching Directory" class="img-fluid">
            </a>
        </div>
        <div class="sidebar-toggle d-md-none">
            <button class="btn btn-link text-white" id="sidebarToggleBtn">
                <i class="fas fa-bars"></i>
            </button>
        </div>
    </div>
    
    <div class="sidebar-menu">
        <ul class="nav flex-column">
            <?php foreach ($menuItems as $item): ?>
                <li class="nav-item">
                    <a class="nav-link <?php echo $item['active'] ? 'active' : ''; ?>" href="<?php echo $item['url']; ?>">
                        <i class="<?php echo $item['icon']; ?> me-2"></i>
                        <span><?php echo $item['title']; ?></span>
                    </a>
                </li>
            <?php endforeach; ?>
            
            <li class="nav-item mt-4">
                <a class="nav-link text-danger" href="logout.php">
                    <i class="fas fa-sign-out-alt me-2"></i>
                    <span>Logout</span>
                </a>
            </li>
        </ul>
    </div>
</div>