<?php
/**
 * Create Activity Logs Table
 * This script creates the activity_logs table if it doesn't exist
 */
require_once 'includes/autoload.php';

// Get database instance
$db = Database::getInstance();

echo "<h1>Create Activity Logs Table</h1>";

// Check if activity_logs table exists
$result = $db->fetchRow("SHOW TABLES LIKE 'activity_logs'");

if ($result) {
    echo "<p>Activity logs table already exists.</p>";
} else {
    echo "<p>Creating activity_logs table...</p>";
    
    $sql = "CREATE TABLE `activity_logs` (
        `activity_id` int(11) NOT NULL AUTO_INCREMENT,
        `activity_type` varchar(50) NOT NULL,
        `description` text NOT NULL,
        `user_id` int(11) NOT NULL,
        `user_type` enum('admin','coaching','user') NOT NULL,
        `ip_address` varchar(45) DEFAULT NULL,
        `user_agent` text,
        `created_at` datetime NOT NULL,
        PRIMARY KEY (`activity_id`),
        <PERSON><PERSON><PERSON> `user_id` (`user_id`),
        <PERSON>EY `user_type` (`user_type`),
        <PERSON><PERSON>Y `activity_type` (`activity_type`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    $result = $db->query($sql);
    
    if ($result) {
        echo "<p>Activity logs table created successfully.</p>";
        
        // Add some sample activity logs
        $activities = [
            [
                'activity_type' => 'login',
                'description' => 'Admin logged in',
                'user_id' => 1,
                'user_type' => 'admin',
                'ip_address' => '127.0.0.1',
                'user_agent' => 'Mozilla/5.0',
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 hour'))
            ],
            [
                'activity_type' => 'add_coaching',
                'description' => 'Added new coaching center: Test Coaching',
                'user_id' => 1,
                'user_type' => 'admin',
                'ip_address' => '127.0.0.1',
                'user_agent' => 'Mozilla/5.0',
                'created_at' => date('Y-m-d H:i:s', strtotime('-45 minutes'))
            ],
            [
                'activity_type' => 'update_settings',
                'description' => 'Updated site settings',
                'user_id' => 1,
                'user_type' => 'admin',
                'ip_address' => '127.0.0.1',
                'user_agent' => 'Mozilla/5.0',
                'created_at' => date('Y-m-d H:i:s', strtotime('-30 minutes'))
            ],
            [
                'activity_type' => 'add_user',
                'description' => 'Added new user: <EMAIL>',
                'user_id' => 1,
                'user_type' => 'admin',
                'ip_address' => '127.0.0.1',
                'user_agent' => 'Mozilla/5.0',
                'created_at' => date('Y-m-d H:i:s', strtotime('-15 minutes'))
            ],
            [
                'activity_type' => 'approve_review',
                'description' => 'Approved review for Test Coaching',
                'user_id' => 1,
                'user_type' => 'admin',
                'ip_address' => '127.0.0.1',
                'user_agent' => 'Mozilla/5.0',
                'created_at' => date('Y-m-d H:i:s')
            ]
        ];
        
        foreach ($activities as $activity) {
            $db->insert('activity_logs', $activity);
        }
        
        echo "<p>Sample activity logs added.</p>";
    } else {
        echo "<p>Failed to create activity logs table.</p>";
    }
}

// Check if admins table exists
$result = $db->fetchRow("SHOW TABLES LIKE 'admins'");

if ($result) {
    echo "<p>Admins table already exists.</p>";
} else {
    echo "<p>Creating admins table...</p>";
    
    $sql = "CREATE TABLE `admins` (
        `admin_id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(100) NOT NULL,
        `email` varchar(100) NOT NULL,
        `password` varchar(255) NOT NULL,
        `role` enum('super_admin','admin') NOT NULL DEFAULT 'admin',
        `status` enum('active','inactive') NOT NULL DEFAULT 'active',
        `remember_token` varchar(64) DEFAULT NULL,
        `remember_expires` datetime DEFAULT NULL,
        `reset_token` varchar(64) DEFAULT NULL,
        `reset_expires` datetime DEFAULT NULL,
        `created_at` datetime NOT NULL,
        `updated_at` datetime DEFAULT NULL,
        PRIMARY KEY (`admin_id`),
        UNIQUE KEY `email` (`email`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    $result = $db->query($sql);
    
    if ($result) {
        echo "<p>Admins table created successfully.</p>";
        
        // Create default admin
        $adminData = [
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => password_hash('admin123', PASSWORD_DEFAULT),
            'role' => 'super_admin',
            'status' => 'active',
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        $adminId = $db->insert('admins', $adminData);
        
        if ($adminId) {
            echo "<p>Default admin created with ID: $adminId</p>";
        } else {
            echo "<p>Failed to create default admin.</p>";
        }
    } else {
        echo "<p>Failed to create admins table.</p>";
    }
}

echo "<p><a href='admin/index.php'>Go to Admin Login</a></p>";