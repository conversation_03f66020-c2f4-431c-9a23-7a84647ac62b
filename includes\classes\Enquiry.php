<?php
/**
 * Enquiry Class
 * Handles enquiry functionality
 */
class Enquiry {
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Add a new enquiry
     * @param array $data Enquiry data
     * @return int|bool Enquiry ID or false on failure
     */
    public function add($data) {
        // Set created_at
        $data['created_at'] = date('Y-m-d H:i:s');
        
        // Set status to new if not provided
        if (!isset($data['status'])) {
            $data['status'] = 'new';
        }
        
        // Insert enquiry
        $enquiryId = $this->db->insert('enquiries', $data);
        
        if ($enquiryId) {
            // Send notification to coaching center
            if (isset($data['coaching_id']) && $data['coaching_id'] > 0) {
                $this->sendNotification($enquiryId, $data['coaching_id']);
            }
            
            // Send confirmation email to user
            $this->sendConfirmationEmail($data);
        }
        
        return $enquiryId;
    }
    
    /**
     * Update enquiry
     * @param int $enquiryId Enquiry ID
     * @param array $data Enquiry data
     * @return bool True if update successful
     */
    public function update($enquiryId, $data) {
        // Set updated_at
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        // Update enquiry
        return $this->db->update('inquiries', $data, ['inquiry_id' => $enquiryId]);
    }
    
    /**
     * Delete enquiry
     * @param int $enquiryId Enquiry ID
     * @return bool True if delete successful
     */
    public function delete($enquiryId) {
        return $this->db->delete('inquiries', ['inquiry_id' => $enquiryId]);
    }
    
    /**
     * Get enquiry by ID
     * @param int $enquiryId Enquiry ID
     * @return array|null Enquiry data
     */
    public function getById($enquiryId) {
        return $this->db->fetchRow(
            "SELECT i.*, c.coaching_name, c.slug as coaching_slug
             FROM inquiries i
             LEFT JOIN coaching_centers c ON i.coaching_id = c.coaching_id
             WHERE i.inquiry_id = ?",
            [$enquiryId]
        );
    }
    
    /**
     * Get enquiries by coaching center
     * @param int $coachingId Coaching center ID
     * @param string $status Enquiry status (all, new, in_progress, closed)
     * @param int $page Page number
     * @param int $limit Records per page
     * @return array Enquiries
     */
    public function getByCoaching($coachingId, $status = 'all', $page = 1, $limit = RECORDS_PER_PAGE) {
        $offset = ($page - 1) * $limit;
        $params = [$coachingId];
        
        $sql = "SELECT * FROM enquiries WHERE coaching_id = ?";
        
        if ($status !== 'all') {
            $sql .= " AND status = ?";
            $params[] = $status;
        }
        
        $sql .= " ORDER BY created_at DESC LIMIT ?, ?";
        $params[] = $offset;
        $params[] = $limit;
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Get total count of enquiries by coaching center
     * @param int $coachingId Coaching center ID
     * @param string $status Enquiry status (all, new, in_progress, closed)
     * @return int Total count
     */
    public function getTotalCountByCoaching($coachingId, $status = 'all') {
        $params = [$coachingId];
        
        $sql = "SELECT COUNT(*) as count FROM inquiries WHERE coaching_id = ?";
        
        if ($status !== 'all') {
            $sql .= " AND status = ?";
            $params[] = $status;
        }
        
        $result = $this->db->fetchRow($sql, $params);
        return $result['count'];
    }
    
    /**
     * Get all enquiries
     * @param string $status Enquiry status (all, new, in_progress, closed)
     * @param int $page Page number
     * @param int $limit Records per page
     * @return array Enquiries
     */
    public function getAll($status = 'all', $page = 1, $limit = RECORDS_PER_PAGE) {
        $offset = ($page - 1) * $limit;
        $params = [];
        
        $sql = "SELECT e.*, c.coaching_name, c.slug as coaching_slug
                FROM enquiries e
                LEFT JOIN coaching_centers c ON e.coaching_id = c.coaching_id";
        
        if ($status !== 'all') {
            $sql .= " WHERE e.status = ?";
            $params[] = $status;
        }

        $sql .= " ORDER BY e.created_at DESC LIMIT ?, ?";
        $params[] = $offset;
        $params[] = $limit;
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Get total count of enquiries
     * @param string $status Enquiry status (all, new, in_progress, closed)
     * @return int Total count
     */
    public function getTotalCount($status = 'all') {
        $sql = "SELECT COUNT(*) as count FROM enquiries";
        $params = [];
        
        if ($status !== 'all') {
            $sql .= " WHERE status = ?";
            $params[] = $status;
        }
        
        $result = $this->db->fetchRow($sql, $params);
        return $result['count'];
    }
    
    /**
     * Get recent enquiries
     * @param int $limit Number of enquiries to get
     * @return array Recent enquiries
     */
    public function getRecent($limit = 5) {
        return $this->db->fetchAll(
            "SELECT e.*, c.coaching_name, c.slug as coaching_slug
             FROM enquiries e
             LEFT JOIN coaching_centers c ON e.coaching_id = c.coaching_id
             ORDER BY e.created_at DESC
             LIMIT ?",
            [$limit]
        );
    }
    
    /**
     * Get new enquiries count
     * @param int $days Number of days to consider
     * @return int New enquiries count
     */
    public function getNewEnquiriesCount($days = 7) {
        $result = $this->db->fetchRow(
            "SELECT COUNT(*) as count FROM enquiries
             WHERE status = 'new' AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)",
            [$days]
        );
        
        return $result['count'];
    }
    
    /**
     * Get recent enquiries by coaching center
     * @param int $coachingId Coaching center ID
     * @param int $limit Number of enquiries to get
     * @return array Recent enquiries
     */
    public function getRecentByCoaching($coachingId, $limit = 5) {
        return $this->db->fetchAll(
            "SELECT * FROM enquiries
             WHERE coaching_id = ?
             ORDER BY created_at DESC
             LIMIT ?",
            [$coachingId, $limit]
        );
    }
    
    /**
     * Send notification to coaching center
     * @param int $enquiryId Enquiry ID
     * @param int $coachingId Coaching center ID
     * @return bool True if notification sent
     */
    private function sendNotification($enquiryId, $coachingId) {
        // Get coaching center email
        $coaching = $this->db->fetchRow(
            "SELECT coaching_name, email FROM coaching_centers WHERE coaching_id = ?",
            [$coachingId]
        );
        
        if (!$coaching) {
            return false;
        }
        
        // Get enquiry details
        $enquiry = $this->getById($enquiryId);
        
        if (!$enquiry) {
            return false;
        }
        
        // Send email notification
        $subject = 'New Enquiry Received';
        $message = "Hello {$coaching['coaching_name']},\n\n";
        $message .= "You have received a new enquiry from {$enquiry['name']}.\n\n";
        $message .= "Enquiry Details:\n";
        $message .= "Name: {$enquiry['name']}\n";
        $message .= "Email: {$enquiry['email']}\n";
        $message .= "Phone: {$enquiry['phone']}\n";
        $message .= "Message: {$enquiry['message']}\n\n";
        $message .= "Please log in to your coaching panel to respond to this enquiry.\n\n";
        $message .= "Regards,\n";
        $message .= Settings::getInstance()->getSiteName();
        
        return sendEmail($coaching['email'], $subject, $message);
    }
    
    /**
     * Send confirmation email to user
     * @param array $data Enquiry data
     * @return bool True if email sent
     */
    private function sendConfirmationEmail($data) {
        if (!isset($data['email']) || empty($data['email'])) {
            return false;
        }
        
        // Get coaching center name if applicable
        $coachingName = 'our team';
        
        if (isset($data['coaching_id']) && $data['coaching_id'] > 0) {
            $coaching = $this->db->fetchRow(
                "SELECT coaching_name FROM coaching_centers WHERE coaching_id = ?",
                [$data['coaching_id']]
            );
            
            if ($coaching) {
                $coachingName = $coaching['coaching_name'];
            }
        }
        
        // Send confirmation email
        $subject = 'Enquiry Received - ' . Settings::getInstance()->getSiteName();
        $message = "Hello {$data['name']},\n\n";
        $message .= "Thank you for your enquiry. We have received your message and {$coachingName} will get back to you shortly.\n\n";
        $message .= "Your Enquiry Details:\n";
        $message .= "Name: {$data['name']}\n";
        $message .= "Email: {$data['email']}\n";
        $message .= "Phone: {$data['phone']}\n";
        $message .= "Message: {$data['message']}\n\n";
        $message .= "Regards,\n";
        $message .= Settings::getInstance()->getSiteName();
        
        return sendEmail($data['email'], $subject, $message);
    }
    
    /**
     * Get count by status
     * @param string $status Status
     * @return int Count
     */
    public function getCountByStatus($status) {
        return $this->db->count('inquiries', 'status = ?', [$status]);
    }
}