/* Admin Dashboard Styles */

/* Variables */
:root {
    --primary-color: #003366;
    --secondary-color: #66B2FF;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --dark-color: #343a40;
    --light-color: #f8f9fa;
    --sidebar-width: 250px;
    --header-height: 60px;
    --sidebar-bg: #003366;
    --sidebar-color: #fff;
    --sidebar-hover: rgba(255, 255, 255, 0.1);
    --sidebar-active: rgba(255, 255, 255, 0.2);
}

/* Layout */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f7fa;
    color: #333;
}

.dashboard-container {
    display: flex;
    min-height: 100vh;
}

.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    transition: margin-left 0.3s;
}

.dashboard-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    transition: margin-left 0.3s;
    min-height: 100vh;
}

/* Sidebar */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: var(--sidebar-width);
    height: 100vh;
    background-color: var(--sidebar-bg);
    color: var(--sidebar-color);
    z-index: 1000;
    transition: all 0.3s;
    overflow-y: auto;
}

.sidebar-header {
    padding: 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header .logo img {
    max-height: 40px;
}

.sidebar-menu {
    padding: 15px 0;
}

.sidebar-menu .nav-item {
    margin-bottom: 5px;
}

.sidebar-menu .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 10px 15px;
    border-radius: 5px;
    margin: 0 10px;
    transition: all 0.3s;
}

.sidebar-menu .nav-link:hover {
    color: #fff;
    background-color: var(--sidebar-hover);
}

.sidebar-menu .nav-link.active {
    color: #fff;
    background-color: var(--sidebar-active);
}

.sidebar-menu .nav-link i {
    width: 20px;
    text-align: center;
}

/* Header */
.dashboard-header {
    height: var(--header-height);
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
    padding: 0 20px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.header-right {
    display: flex;
    align-items: center;
}

.header-right .nav-link {
    color: #6c757d;
    padding: 10px;
    position: relative;
}

.header-right .nav-link:hover {
    color: var(--primary-color);
}

.header-right .badge {
    position: absolute;
    top: 3px;
    right: 3px;
    font-size: 0.6rem;
}

.avatar-placeholder {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

/* Cards */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
    padding: 15px 20px;
}

.card-title {
    margin-bottom: 0;
    color: #333;
    font-weight: 600;
}

.card-body {
    padding: 20px;
}

/* Stats Cards */
.stats-card {
    transition: transform 0.3s;
}

.stats-card:hover {
    transform: translateY(-5px);
}

.stats-title {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.stats-value {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 0;
}

.stats-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 1.5rem;
}

/* Tables */
.table {
    margin-bottom: 0;
}

.table th {
    font-weight: 600;
    color: #495057;
}

.table td {
    vertical-align: middle;
}

/* Forms */
.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-control, .form-select {
    border-radius: 5px;
    padding: 0.5rem 0.75rem;
    border: 1px solid #ced4da;
}

.form-control:focus, .form-select:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.25rem rgba(102, 178, 255, 0.25);
}

/* Buttons */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #002855;
    border-color: #002855;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
}

/* Utilities */
.page-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

/* Reviews */
.review-item:last-child {
    border-bottom: none !important;
    padding-bottom: 0 !important;
}

/* Subscription */
.subscription-info {
    padding: 10px;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Alert Styles */
.alert {
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.375rem;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}