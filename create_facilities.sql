-- Create facilities table if it doesn't exist
CREATE TABLE IF NOT EXISTS `facilities` (
  `facility_id` int(11) NOT NULL AUTO_INCREMENT,
  `facility_name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `icon` varchar(50) DEFAULT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`facility_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Insert default facilities if table is empty
INSERT INTO `facilities` (`facility_name`, `description`, `icon`, `status`, `created_at`)
SELECT 'Wi-Fi', 'Free Wi-Fi access', 'wifi', 'active', NOW()
WHERE NOT EXISTS (SELECT 1 FROM `facilities` WHERE `facility_name` = 'Wi-Fi');

INSERT INTO `facilities` (`facility_name`, `description`, `icon`, `status`, `created_at`)
SELECT 'Air Conditioning', 'Air conditioned classrooms', 'snowflake', 'active', NOW()
WHERE NOT EXISTS (SELECT 1 FROM `facilities` WHERE `facility_name` = 'Air Conditioning');

INSERT INTO `facilities` (`facility_name`, `description`, `icon`, `status`, `created_at`)
SELECT 'Library', 'Access to library resources', 'book', 'active', NOW()
WHERE NOT EXISTS (SELECT 1 FROM `facilities` WHERE `facility_name` = 'Library');

INSERT INTO `facilities` (`facility_name`, `description`, `icon`, `status`, `created_at`)
SELECT 'Computer Lab', 'Modern computer facilities', 'desktop', 'active', NOW()
WHERE NOT EXISTS (SELECT 1 FROM `facilities` WHERE `facility_name` = 'Computer Lab');

INSERT INTO `facilities` (`facility_name`, `description`, `icon`, `status`, `created_at`)
SELECT 'Cafeteria', 'On-site food services', 'utensils', 'active', NOW()
WHERE NOT EXISTS (SELECT 1 FROM `facilities` WHERE `facility_name` = 'Cafeteria');

INSERT INTO `facilities` (`facility_name`, `description`, `icon`, `status`, `created_at`)
SELECT 'Parking', 'Parking facilities available', 'parking', 'active', NOW()
WHERE NOT EXISTS (SELECT 1 FROM `facilities` WHERE `facility_name` = 'Parking');

INSERT INTO `facilities` (`facility_name`, `description`, `icon`, `status`, `created_at`)
SELECT 'Study Rooms', 'Dedicated study spaces', 'door-open', 'active', NOW()
WHERE NOT EXISTS (SELECT 1 FROM `facilities` WHERE `facility_name` = 'Study Rooms');

INSERT INTO `facilities` (`facility_name`, `description`, `icon`, `status`, `created_at`)
SELECT 'Sports Facilities', 'Access to sports equipment and grounds', 'futbol', 'active', NOW()
WHERE NOT EXISTS (SELECT 1 FROM `facilities` WHERE `facility_name` = 'Sports Facilities');