<?php
require_once 'includes/autoload.php';

// Get database connection
$db = Database::getInstance();

// Get table structure
$tables = ['coaching_centers', 'testimonials', 'reviews', 'inquiries', 'pages'];

foreach ($tables as $table) {
    echo "<h2>Table: {$table}</h2>";
    
    $result = $db->fetchAll("SHOW COLUMNS FROM {$table}");
    
    if (!$result) {
        echo "Error getting columns for table {$table}<br>";
        continue;
    }
    
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    foreach ($result as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "<td>{$column['Extra']}</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    echo "<hr>";
}
?>