2025-06-26 14:53:14 - getTotalCount method called with status: all
2025-06-26 14:53:14 - SQL Query: SELECT COUNT(*) as count FROM coaching_centers
2025-06-26 14:53:14 - getTotalCount method called with status: pending
2025-06-26 14:53:14 - SQL Query: SELECT COUNT(*) as count FROM coaching_centers WHERE status = ?
2025-06-26 14:53:14 - User::getTotalCount method called with status: all
2025-06-26 14:53:14 - SQL Query: SELECT COUNT(*) as count FROM users
2025-06-26 14:53:14 - User::getNewUsersCount method called with days: 7
2025-06-26 14:53:15 - getRecent method called with limit: 5
2025-06-26 14:53:15 - SQL Query: SELECT c.*, 
                           ci.city_name, 
                           s.state_name
                    FROM coaching_centers c
                    LEFT JOIN cities ci ON c.city_id = ci.city_id
                    LEFT JOIN states s ON c.state_id = s.state_id
                    ORDER BY c.created_at DESC
                    LIMIT ?
2025-06-26 15:35:33 - getTotalCount method called with status: all
2025-06-26 15:35:33 - SQL Query: SELECT COUNT(*) as count FROM coaching_centers
2025-06-26 15:35:33 - getTotalCount method called with status: pending
2025-06-26 15:35:33 - SQL Query: SELECT COUNT(*) as count FROM coaching_centers WHERE status = ?
2025-06-26 15:35:33 - User::getTotalCount method called with status: all
2025-06-26 15:35:33 - SQL Query: SELECT COUNT(*) as count FROM users
2025-06-26 15:35:33 - User::getNewUsersCount method called with days: 7
2025-06-26 15:35:33 - getRecent method called with limit: 5
2025-06-26 15:35:33 - SQL Query: SELECT c.*, 
                           ci.city_name, 
                           s.state_name
                    FROM coaching_centers c
                    LEFT JOIN cities ci ON c.city_id = ci.city_id
                    LEFT JOIN states s ON c.state_id = s.state_id
                    ORDER BY c.created_at DESC
                    LIMIT ?
2025-06-26 15:37:21 - getTotalCount method called with status: all
2025-06-26 15:37:21 - SQL Query: SELECT COUNT(*) as count FROM coaching_centers
2025-06-26 15:37:21 - getTotalCount method called with status: pending
2025-06-26 15:37:21 - SQL Query: SELECT COUNT(*) as count FROM coaching_centers WHERE status = ?
2025-06-26 15:37:21 - User::getTotalCount method called with status: all
2025-06-26 15:37:21 - SQL Query: SELECT COUNT(*) as count FROM users
2025-06-26 15:37:21 - User::getNewUsersCount method called with days: 7
2025-06-26 15:37:21 - getRecent method called with limit: 5
2025-06-26 15:37:21 - SQL Query: SELECT c.*, 
                           ci.city_name, 
                           s.state_name
                    FROM coaching_centers c
                    LEFT JOIN cities ci ON c.city_id = ci.city_id
                    LEFT JOIN states s ON c.state_id = s.state_id
                    ORDER BY c.created_at DESC
                    LIMIT ?
2025-06-26 15:37:36 - getTotalCount method called with status: all
2025-06-26 15:37:36 - SQL Query: SELECT COUNT(*) as count FROM coaching_centers
2025-06-26 15:37:36 - getTotalCount method called with status: pending
2025-06-26 15:37:36 - SQL Query: SELECT COUNT(*) as count FROM coaching_centers WHERE status = ?
2025-06-26 15:37:36 - User::getTotalCount method called with status: all
2025-06-26 15:37:36 - SQL Query: SELECT COUNT(*) as count FROM users
2025-06-26 15:37:36 - User::getNewUsersCount method called with days: 7
2025-06-26 15:37:36 - getRecent method called with limit: 5
2025-06-26 15:37:36 - SQL Query: SELECT c.*, 
                           ci.city_name, 
                           s.state_name
                    FROM coaching_centers c
                    LEFT JOIN cities ci ON c.city_id = ci.city_id
                    LEFT JOIN states s ON c.state_id = s.state_id
                    ORDER BY c.created_at DESC
                    LIMIT ?
2025-06-26 15:38:54 - getTotalCount method called with status: all
2025-06-26 15:38:54 - SQL Query: SELECT COUNT(*) as count FROM coaching_centers
2025-06-26 15:38:54 - getTotalCount method called with status: pending
2025-06-26 15:38:54 - SQL Query: SELECT COUNT(*) as count FROM coaching_centers WHERE status = ?
2025-06-26 15:38:54 - User::getTotalCount method called with status: all
2025-06-26 15:38:54 - SQL Query: SELECT COUNT(*) as count FROM users
2025-06-26 15:38:54 - User::getNewUsersCount method called with days: 7
2025-06-26 15:38:54 - getRecent method called with limit: 5
2025-06-26 15:38:54 - SQL Query: SELECT c.*, 
                           ci.city_name, 
                           s.state_name
                    FROM coaching_centers c
                    LEFT JOIN cities ci ON c.city_id = ci.city_id
                    LEFT JOIN states s ON c.state_id = s.state_id
                    ORDER BY c.created_at DESC
                    LIMIT ?
2025-06-26 15:47:01 - getTotalCount method called with status: all
2025-06-26 15:47:01 - SQL Query: SELECT COUNT(*) as count FROM coaching_centers
2025-06-26 15:47:01 - getTotalCount method called with status: pending
2025-06-26 15:47:01 - SQL Query: SELECT COUNT(*) as count FROM coaching_centers WHERE status = ?
2025-06-26 15:47:01 - User::getTotalCount method called with status: all
2025-06-26 15:47:01 - SQL Query: SELECT COUNT(*) as count FROM users
2025-06-26 15:47:01 - User::getNewUsersCount method called with days: 7
2025-06-26 15:47:01 - getRecent method called with limit: 5
2025-06-26 15:47:01 - SQL Query: SELECT c.*, 
                           ci.city_name, 
                           s.state_name
                    FROM coaching_centers c
                    LEFT JOIN cities ci ON c.city_id = ci.city_id
                    LEFT JOIN states s ON c.state_id = s.state_id
                    ORDER BY c.created_at DESC
                    LIMIT ?
2025-06-26 15:47:05 - getTotalCount method called with status: all
2025-06-26 15:47:05 - SQL Query: SELECT COUNT(*) as count FROM coaching_centers
2025-06-26 15:47:05 - getTotalCount method called with status: pending
2025-06-26 15:47:05 - SQL Query: SELECT COUNT(*) as count FROM coaching_centers WHERE status = ?
2025-06-26 15:47:05 - User::getTotalCount method called with status: all
2025-06-26 15:47:05 - SQL Query: SELECT COUNT(*) as count FROM users
2025-06-26 15:47:05 - User::getNewUsersCount method called with days: 7
2025-06-26 15:47:05 - getRecent method called with limit: 5
2025-06-26 15:47:05 - SQL Query: SELECT c.*, 
                           ci.city_name, 
                           s.state_name
                    FROM coaching_centers c
                    LEFT JOIN cities ci ON c.city_id = ci.city_id
                    LEFT JOIN states s ON c.state_id = s.state_id
                    ORDER BY c.created_at DESC
                    LIMIT ?
2025-06-26 15:47:21 - getTotalCount method called with status: all
2025-06-26 15:47:21 - SQL Query: SELECT COUNT(*) as count FROM coaching_centers
2025-06-26 15:47:21 - getTotalCount method called with status: pending
2025-06-26 15:47:21 - SQL Query: SELECT COUNT(*) as count FROM coaching_centers WHERE status = ?
2025-06-26 15:47:21 - User::getTotalCount method called with status: all
2025-06-26 15:47:21 - SQL Query: SELECT COUNT(*) as count FROM users
2025-06-26 15:47:21 - User::getNewUsersCount method called with days: 7
2025-06-26 15:47:21 - getRecent method called with limit: 5
2025-06-26 15:47:21 - SQL Query: SELECT c.*, 
                           ci.city_name, 
                           s.state_name
                    FROM coaching_centers c
                    LEFT JOIN cities ci ON c.city_id = ci.city_id
                    LEFT JOIN states s ON c.state_id = s.state_id
                    ORDER BY c.created_at DESC
                    LIMIT ?
2025-06-26 16:57:56 - getTotalCount method called with status: all
2025-06-26 16:57:56 - SQL Query: SELECT COUNT(*) as count FROM coaching_centers
2025-06-26 16:57:56 - getTotalCount method called with status: pending
2025-06-26 16:57:56 - SQL Query: SELECT COUNT(*) as count FROM coaching_centers WHERE status = ?
2025-06-26 16:57:56 - User::getTotalCount method called with status: all
2025-06-26 16:57:56 - SQL Query: SELECT COUNT(*) as count FROM users
2025-06-26 16:57:56 - User::getNewUsersCount method called with days: 7
2025-06-26 16:57:56 - getRecent method called with limit: 5
2025-06-26 16:57:56 - SQL Query: SELECT c.*, 
                           ci.city_name, 
                           s.state_name
                    FROM coaching_centers c
                    LEFT JOIN cities ci ON c.city_id = ci.city_id
                    LEFT JOIN states s ON c.state_id = s.state_id
                    ORDER BY c.created_at DESC
                    LIMIT ?
2025-06-26 19:09:49 - getTotalCount method called with status: all
2025-06-26 19:09:49 - SQL Query: SELECT COUNT(*) as count FROM coaching_centers
2025-06-26 19:09:49 - getTotalCount method called with status: pending
2025-06-26 19:09:49 - SQL Query: SELECT COUNT(*) as count FROM coaching_centers WHERE status = ?
2025-06-26 19:09:49 - User::getTotalCount method called with status: all
2025-06-26 19:09:49 - SQL Query: SELECT COUNT(*) as count FROM users
2025-06-26 19:09:49 - User::getNewUsersCount method called with days: 7
2025-06-26 19:09:49 - getRecent method called with limit: 5
2025-06-26 19:09:49 - SQL Query: SELECT c.*, 
                           ci.city_name, 
                           s.state_name
                    FROM coaching_centers c
                    LEFT JOIN cities ci ON c.city_id = ci.city_id
                    LEFT JOIN states s ON c.state_id = s.state_id
                    ORDER BY c.created_at DESC
                    LIMIT ?
