<?php
/**
 * Helper Functions
 * Contains global helper functions used throughout the application
 */

/**
 * Redirect to a URL
 * @param string $url URL to redirect to
 * @param bool $permanent Is redirect permanent (301)
 */
function redirect($url, $permanent = false) {
    header('Location: ' . $url, true, $permanent ? 301 : 302);
    exit;
}

/**
 * Get current URL
 * @return string Current URL
 */
function getCurrentUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
    return $protocol . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
}

/**
 * Get base URL
 * @return string Base URL
 */
function getBaseUrl() {
    return BASE_URL;
}

/**
 * Get asset URL
 * @param string $path Asset path
 * @return string Asset URL
 */
function getAssetUrl($path) {
    return ASSETS_URL . $path;
}

/**
 * Get upload URL
 * @param string $path Upload path
 * @return string Upload URL
 */
function getUploadUrl($path) {
    if (empty($path)) {
        error_log("Warning: Empty path passed to getUploadUrl()");
        return UPLOAD_URL . 'default-image.png';
    }
    
    // Remove any leading slashes or 'uploads/' prefix to avoid duplication
    $path = ltrim($path, '/');
    if (strpos($path, 'uploads/') === 0) {
        $path = substr($path, 8); // Remove 'uploads/' prefix
    }
    
    $fullUrl = UPLOAD_URL . $path;
    error_log("getUploadUrl: Input path = '{$path}', Output URL = '{$fullUrl}'");
    
    return $fullUrl;
}

/**
 * Get admin URL
 * @param string $path Admin path
 * @return string Admin URL
 */
function getAdminUrl($path = '') {
    return ADMIN_URL . $path;
}

/**
 * Check if current page is admin
 * @return bool True if current page is admin
 */
function isAdminPage() {
    return strpos($_SERVER['REQUEST_URI'], '/admin/') !== false;
}

/**
 * Get page title
 * @param string $title Page title
 * @return string Full page title
 */
function getPageTitle($title = '') {
    $settings = Settings::getInstance();
    $siteName = $settings->getSiteName();
    
    if (empty($title)) {
        return $siteName;
    }
    
    return $title . ' - ' . $siteName;
}

/**
 * Format date
 * @param string $date Date string
 * @param string $format Format string (default: 'M d, Y')
 * @return string Formatted date
 */
function formatDate($date, $format = 'M d, Y') {
    return Utility::formatDate($date, $format);
}

/**
 * Format datetime
 * @param string $datetime Datetime string
 * @param string $format Format string (default: 'M d, Y h:i A')
 * @return string Formatted datetime
 */
function formatDateTime($datetime, $format = 'M d, Y h:i A') {
    return Utility::formatDateTime($datetime, $format);
}

/**
 * Get time ago string
 * @param string $datetime Datetime string
 * @return string Time ago string
 */
function timeAgo($datetime) {
    return Utility::timeAgo($datetime);
}

/**
 * Truncate a string to a specified length
 * @param string $string String to truncate
 * @param int $length Maximum length
 * @param string $append String to append if truncated (default: '...')
 * @return string Truncated string
 */
function truncate($string, $length = 100, $append = '...') {
    return Utility::truncate($string, $length, $append);
}

/**
 * Sanitize input
 * @param string $input Input to sanitize
 * @return string Sanitized input
 */
function sanitize($input) {
    return Utility::sanitize($input);
}

/**
 * Generate CSRF token
 * @return string CSRF token
 */
function generateCSRFToken() {
    return Utility::generateCSRFToken();
}

/**
 * Verify CSRF token
 * @param string $token Token to verify
 * @return bool True if token is valid
 */
function verifyCSRFToken($token) {
    return Utility::verifyCSRFToken($token);
}

/**
 * Get CSRF token field
 * @return string CSRF token field HTML
 */
function getCSRFTokenField() {
    $token = generateCSRFToken();
    return '<input type="hidden" name="csrf_token" value="' . $token . '">';
}

/**
 * Check if CSRF token is valid
 * @return bool True if CSRF token is valid
 */
function checkCSRFToken() {
    if (!isset($_POST['csrf_token'])) {
        return false;
    }
    
    return verifyCSRFToken($_POST['csrf_token']);
}

/**
 * Get star rating HTML
 * @param float $rating Rating value
 * @param int $maxRating Maximum rating (default: 5)
 * @return string Star rating HTML
 */
function getStarRating($rating, $maxRating = 5) {
    return Utility::getStarRating($rating, $maxRating);
}

/**
 * Get pagination HTML
 * @param int $totalRecords Total number of records
 * @param int $currentPage Current page number
 * @param int $recordsPerPage Records per page
 * @param string $url Base URL for pagination links
 * @return string Pagination HTML
 */
function getPagination($totalRecords, $currentPage, $recordsPerPage, $url) {
    return Utility::getPagination($totalRecords, $currentPage, $recordsPerPage, $url);
}

/**
 * Get meta tags HTML
 * @param string $title Meta title
 * @param string $description Meta description
 * @param string $keywords Meta keywords
 * @param string $ogImage Open Graph image URL
 * @return string Meta tags HTML
 */
function getMetaTags($title = '', $description = '', $keywords = '', $ogImage = '') {
    return Utility::getMetaTags($title, $description, $keywords, $ogImage);
}

/**
 * Get breadcrumb HTML
 * @param array $items Breadcrumb items (array of ['title' => string, 'url' => string])
 * @return string Breadcrumb HTML
 */
function getBreadcrumb($items) {
    return Utility::getBreadcrumb($items);
}

/**
 * Get setting value
 * @param string $key Setting key
 * @param mixed $default Default value if setting not found
 * @return mixed Setting value
 */
function getSetting($key, $default = null) {
    $settings = Settings::getInstance();
    return $settings->get($key, $default);
}

/**
 * Check if user is logged in
 * @return bool True if user is logged in
 */
function isLoggedIn() {
    global $user;
    return $user->isLoggedIn();
}

/**
 * Check if user is admin
 * @return bool True if user is admin
 */
function isAdmin() {
    global $user;
    return $user->isAdmin();
}

/**
 * Check if user is coaching owner
 * @return bool True if user is coaching owner
 */
function isCoachingOwner() {
    global $user;
    return $user->isCoachingOwner();
}

/**
 * Get current user data
 * @param string $field Field name (optional)
 * @return mixed User data or specific field value
 */
function getCurrentUser($field = null) {
    global $user;
    return $user->getUserData($field);
}

/**
 * Get user display name
 * @param array $user User data
 * @return string User display name
 */
function getUserDisplayName($user) {
    if (!empty($user['first_name']) && !empty($user['last_name'])) {
        return $user['first_name'] . ' ' . $user['last_name'];
    } elseif (!empty($user['first_name'])) {
        return $user['first_name'];
    } else {
        return $user['username'];
    }
}

/**
 * Get user profile image URL
 * @param string $profileImage Profile image path
 * @return string Profile image URL
 */
function getUserProfileImage($profileImage) {
    if (empty($profileImage)) {
        return getAssetUrl('images/default-user.png');
    }
    
    return getUploadUrl($profileImage);
}

/**
 * Get coaching logo URL
 * @param string $logo Logo path
 * @return string Logo URL
 */
function getCoachingLogo($logo) {
    if (empty($logo)) {
        return getAssetUrl('images/default-logo.png');
    }
    
    return getUploadUrl($logo);
}

/**
 * Get coaching banner URL
 * @param string $banner Banner path
 * @return string Banner URL
 */
function getCoachingBanner($banner) {
    if (empty($banner)) {
        return getAssetUrl('images/default-banner.jpg');
    }
    
    return getUploadUrl($banner);
}

/**
 * Get coaching URL
 * @param string $slug Coaching slug
 * @return string Coaching URL
 */
function getCoachingUrl($slug) {
    return getBaseUrl() . 'center/' . $slug;
}

/**
 * Get category URL
 * @param string $slug Category slug
 * @return string Category URL
 */
function getCategoryUrl($slug) {
    // Clean the slug to ensure it's valid
    $slug = trim($slug);
    
    // Make sure we're using the exact slug as defined in the data
    // This is critical for matching the correct category
    return getBaseUrl() . 'category/' . $slug;
}

/**
 * Get city URL
 * @param int $cityId City ID
 * @param string $cityName City name
 * @return string City URL
 */
function getCityUrl($slug) {
    return getBaseUrl() . 'city/' . $slug;
}

/**
 * Get state URL
 * @param int $stateId State ID
 * @param string $stateName State name
 * @return string State URL
 */
function getStateUrl($stateId, $stateName) {
    $slug = Utility::generateSlug($stateName);
    return getBaseUrl() . 'state/' . $stateId . '/' . $slug;
}

/**
 * Get blog post URL
 * @param string $slug Post slug
 * @return string Post URL
 */
function getBlogPostUrl($slug) {
    return getBaseUrl() . 'blog/' . $slug;
}

/**
 * Get blog category URL
 * @param string $slug Category slug
 * @return string Category URL
 */
function getBlogCategoryUrl($slug) {
    return getBaseUrl() . 'blog/category/' . $slug;
}

/**
 * Get page URL
 * @param string $slug Page slug
 * @return string Page URL
 */
function getPageUrl($slug) {
    return getBaseUrl() . 'page/' . $slug;
}

/**
 * Get search URL
 * @param array $params Search parameters
 * @return string Search URL
 */
function getSearchUrl($params = []) {
    $url = getBaseUrl() . 'search';
    
    if (!empty($params)) {
        $url .= '?' . http_build_query($params);
    }
    
    return $url;
}

/**
 * Get current page number
 * @return int Current page number
 */
function getCurrentPage() {
    return isset($_GET['page']) && is_numeric($_GET['page']) ? (int)$_GET['page'] : 1;
}

/**
 * Check if string contains a substring
 * @param string $haystack String to search in
 * @param string $needle String to search for
 * @return bool True if string contains substring
 */
function strContains($haystack, $needle) {
    return strpos($haystack, $needle) !== false;
}

/**
 * Check if string starts with a substring
 * @param string $haystack String to search in
 * @param string $needle String to search for
 * @return bool True if string starts with substring
 */
function strStartsWith($haystack, $needle) {
    return strpos($haystack, $needle) === 0;
}

/**
 * Check if string ends with a substring
 * @param string $haystack String to search in
 * @param string $needle String to search for
 * @return bool True if string ends with substring
 */
function strEndsWith($haystack, $needle) {
    return substr($haystack, -strlen($needle)) === $needle;
}

/**
 * Format currency
 * @param float $amount Amount
 * @param string $currency Currency code (default: 'INR')
 * @return string Formatted currency
 */
function formatCurrency($amount, $currency = 'INR') {
    if ($currency === 'INR') {
        return '₹' . number_format($amount, 2);
    }
    
    return $currency . ' ' . number_format($amount, 2);
}

/**
 * Format phone number
 * @param string $phone Phone number
 * @return string Formatted phone number
 */
function formatPhone($phone) {
    // Remove non-numeric characters
    $phone = preg_replace('/[^0-9]/', '', $phone);
    
    // Format Indian phone number
    if (strlen($phone) === 10) {
        return substr($phone, 0, 3) . '-' . substr($phone, 3, 3) . '-' . substr($phone, 6);
    } elseif (strlen($phone) === 11 && $phone[0] === '0') {
        return substr($phone, 0, 1) . '-' . substr($phone, 1, 3) . '-' . substr($phone, 4, 3) . '-' . substr($phone, 7);
    } elseif (strlen($phone) === 12 && substr($phone, 0, 2) === '91') {
        return '+' . substr($phone, 0, 2) . '-' . substr($phone, 2, 3) . '-' . substr($phone, 5, 3) . '-' . substr($phone, 8);
    }
    
    return $phone;
}

/**
 * Get file extension
 * @param string $filename Filename
 * @return string File extension
 */
function getFileExtension($filename) {
    return strtolower(pathinfo($filename, PATHINFO_EXTENSION));
}

/**
 * Check if file is an image
 * @param string $filename Filename
 * @return bool True if file is an image
 */
function isImage($filename) {
    $extension = getFileExtension($filename);
    return in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp']);
}

/**
 * Generate random password
 * @param int $length Password length
 * @return string Random password
 */
function generateRandomPassword($length = 10) {
    return Utility::generateRandomString($length);
}

/**
 * Debug variable
 * @param mixed $var Variable to debug
 * @param bool $die Die after debug (default: true)
 */
function debug($var, $die = true) {
    echo '<pre>';
    print_r($var);
    echo '</pre>';
    
    if ($die) {
        die();
    }
}

/**
 * Get client IP address
 * @return string IP address
 */
function getClientIP() {
    return Utility::getClientIP();
}

/**
 * Send email
 * @param string $to Recipient email
 * @param string $subject Email subject
 * @param string $message Email message
 * @param string $from Sender email (optional)
 * @param array $attachments Attachments (optional)
 * @return bool True if email sent
 */
function sendEmail($to, $subject, $message, $from = '', $attachments = []) {
    return Utility::sendEmail($to, $subject, $message, $from, $attachments);
}

/**
 * Check if request is AJAX
 * @return bool True if request is AJAX
 */
function isAjaxRequest() {
    return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

/**
 * Send JSON response
 * @param array $data Response data
 * @param int $statusCode HTTP status code (default: 200)
 */
function sendJsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

/**
 * Get request method
 * @return string Request method
 */
function getRequestMethod() {
    return $_SERVER['REQUEST_METHOD'];
}

/**
 * Check if request method is POST
 * @return bool True if request method is POST
 */
function isPostRequest() {
    return getRequestMethod() === 'POST';
}

/**
 * Check if request method is GET
 * @return bool True if request method is GET
 */
function isGetRequest() {
    return getRequestMethod() === 'GET';
}

/**
 * Get request data
 * @param string $method Request method (GET or POST)
 * @return array Request data
 */
function getRequestData($method = null) {
    if ($method === 'GET' || (is_null($method) && isGetRequest())) {
        return $_GET;
    } elseif ($method === 'POST' || (is_null($method) && isPostRequest())) {
        return $_POST;
    }
    
    return [];
}

/**
 * Get request parameter
 * @param string $key Parameter key
 * @param mixed $default Default value if parameter not found
 * @param string $method Request method (GET or POST)
 * @return mixed Parameter value
 */
function getRequestParam($key, $default = null, $method = null) {
    $data = getRequestData($method);
    return isset($data[$key]) ? $data[$key] : $default;
}

/**
 * Create directory if it doesn't exist
 * @param string $path Directory path
 * @param int $permissions Directory permissions (default: 0755)
 * @return bool True if directory exists or was created
 */
function createDirectory($path, $permissions = 0755) {
    if (!is_dir($path)) {
        return mkdir($path, $permissions, true);
    }
    
    return true;
}

/**
 * Get human-readable file size
 * @param int $bytes File size in bytes
 * @param int $precision Precision (default: 2)
 * @return string Human-readable file size
 */
function getHumanFileSize($bytes, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    
    $bytes /= pow(1024, $pow);
    
    return round($bytes, $precision) . ' ' . $units[$pow];
}

/**
 * Get file MIME type
 * @param string $filePath File path
 * @return string MIME type
 */
function getFileMimeType($filePath) {
    if (function_exists('mime_content_type')) {
        return mime_content_type($filePath);
    }
    
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($finfo, $filePath);
    finfo_close($finfo);
    
    return $mimeType;
}

/**
 * Generate SEO-friendly URL
 * @param string $string String to convert to URL
 * @return string SEO-friendly URL
 */
function generateSeoUrl($string) {
    return Utility::generateSlug($string);
}

/**
 * Get YouTube video ID from URL
 * @param string $url YouTube URL
 * @return string|null YouTube video ID
 */
function getYoutubeVideoId($url) {
    $pattern = '/(?:youtube\.com\/(?:[^\/\n\s]+\/\S+\/|(?:v|e(?:mbed)?)\/|\S*?[?&]v=)|youtu\.be\/)([a-zA-Z0-9_-]{11})/';
    
    if (preg_match($pattern, $url, $matches)) {
        return $matches[1];
    }
    
    return null;
}

/**
 * Get YouTube embed URL from video ID
 * @param string $videoId YouTube video ID
 * @return string YouTube embed URL
 */
function getYoutubeEmbedUrl($videoId) {
    return 'https://www.youtube.com/embed/' . $videoId;
}

/**
 * Get YouTube thumbnail URL from video ID
 * @param string $videoId YouTube video ID
 * @param string $size Thumbnail size (default: 'mqdefault')
 * @return string YouTube thumbnail URL
 */
function getYoutubeThumbnailUrl($videoId, $size = 'mqdefault') {
    return 'https://img.youtube.com/vi/' . $videoId . '/' . $size . '.jpg';
}

/**
 * Convert YouTube URL to embed code
 * @param string $url YouTube URL
 * @param int $width Width (default: 560)
 * @param int $height Height (default: 315)
 * @return string YouTube embed code
 */
function convertYoutubeUrlToEmbed($url, $width = 560, $height = 315) {
    $videoId = getYoutubeVideoId($url);
    
    if ($videoId) {
        return '<iframe width="' . $width . '" height="' . $height . '" src="' . getYoutubeEmbedUrl($videoId) . '" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>';
    }
    
    return '';
}

/**
 * Get Google Maps embed URL
 * @param string $address Address
 * @param int $zoom Zoom level (default: 15)
 * @return string Google Maps embed URL
 */
function getGoogleMapsEmbedUrl($address, $zoom = 15) {
    return 'https://www.google.com/maps/embed/v1/place?key=YOUR_API_KEY&q=' . urlencode($address) . '&zoom=' . $zoom;
}

/**
 * Convert Google Maps URL to embed code
 * @param string $url Google Maps URL
 * @param int $width Width (default: 600)
 * @param int $height Height (default: 450)
 * @return string Google Maps embed code
 */
function convertGoogleMapsUrlToEmbed($url, $width = 600, $height = 450) {
    if (strContains($url, 'google.com/maps')) {
        return '<iframe width="' . $width . '" height="' . $height . '" src="' . $url . '" frameborder="0" style="border:0;" allowfullscreen="" aria-hidden="false" tabindex="0"></iframe>';
    }
    
    return '';
}

/**
 * Get distance between two coordinates
 * @param float $lat1 Latitude 1
 * @param float $lon1 Longitude 1
 * @param float $lat2 Latitude 2
 * @param float $lon2 Longitude 2
 * @param string $unit Unit (K for kilometers, M for miles, N for nautical miles)
 * @return float Distance
 */
function getDistance($lat1, $lon1, $lat2, $lon2, $unit = 'K') {
    if (($lat1 == $lat2) && ($lon1 == $lon2)) {
        return 0;
    }
    
    $theta = $lon1 - $lon2;
    $dist = sin(deg2rad($lat1)) * sin(deg2rad($lat2)) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * cos(deg2rad($theta));
    $dist = acos($dist);
    $dist = rad2deg($dist);
    $miles = $dist * 60 * 1.1515;
    
    if ($unit == 'K') {
        return ($miles * 1.609344);
    } elseif ($unit == 'N') {
        return ($miles * 0.8684);
    } else {
        return $miles;
    }
}

/**
 * Get current URL with query parameters
 * @param array $params Parameters to add or update
 * @param array $removeParams Parameters to remove
 * @return string URL with query parameters
 */
function getCurrentUrlWithParams($params = [], $removeParams = []) {
    $queryParams = $_GET;
    
    // Remove parameters
    foreach ($removeParams as $param) {
        unset($queryParams[$param]);
    }
    
    // Add or update parameters
    foreach ($params as $key => $value) {
        $queryParams[$key] = $value;
    }
    
    // Build URL
    $url = strtok($_SERVER['REQUEST_URI'], '?');
    
    if (!empty($queryParams)) {
        $url .= '?' . http_build_query($queryParams);
    }
    
    return $url;
}

/**
 * Get active class if condition is true
 * @param bool $condition Condition
 * @param string $class Class name (default: 'active')
 * @return string Class name or empty string
 */
function getActiveClass($condition, $class = 'active') {
    return $condition ? $class : '';
}

/**
 * Check if current URL matches a pattern
 * @param string $pattern URL pattern
 * @return bool True if current URL matches pattern
 */
function isCurrentUrl($pattern) {
    return strContains($_SERVER['REQUEST_URI'], $pattern);
}

/**
 * Get selected attribute if condition is true
 * @param bool $condition Condition
 * @return string Selected attribute or empty string
 */
function getSelectedAttribute($condition) {
    return $condition ? 'selected' : '';
}

/**
 * Get checked attribute if condition is true
 * @param bool $condition Condition
 * @return string Checked attribute or empty string
 */
function getCheckedAttribute($condition) {
    return $condition ? 'checked' : '';
}

/**
 * Get disabled attribute if condition is true
 * @param bool $condition Condition
 * @return string Disabled attribute or empty string
 */
function getDisabledAttribute($condition) {
    return $condition ? 'disabled' : '';
}

/**
 * Get required attribute if condition is true
 * @param bool $condition Condition
 * @return string Required attribute or empty string
 */
function getRequiredAttribute($condition) {
    return $condition ? 'required' : '';
}

/**
 * Get readonly attribute if condition is true
 * @param bool $condition Condition
 * @return string Readonly attribute or empty string
 */
function getReadonlyAttribute($condition) {
    return $condition ? 'readonly' : '';
}

/**
 * Get hidden attribute if condition is true
 * @param bool $condition Condition
 * @return string Hidden attribute or empty string
 */
function getHiddenAttribute($condition) {
    return $condition ? 'hidden' : '';
}

/**
 * Get attribute if condition is true
 * @param string $attribute Attribute name
 * @param bool $condition Condition
 * @param string $value Attribute value (default: '')
 * @return string Attribute or empty string
 */
function getAttribute($attribute, $condition, $value = '') {
    return $condition ? $attribute . ($value !== '' ? '="' . $value . '"' : '') : '';
}

/**
 * Get CSS class based on status
 * @param string $status Status
 * @return string CSS class
 */
function getStatusClass($status) {
    switch (strtolower($status)) {
        case 'active':
        case 'approved':
        case 'completed':
        case 'published':
            return 'success';
        case 'pending':
        case 'draft':
            return 'warning';
        case 'inactive':
        case 'rejected':
        case 'failed':
        case 'archived':
        case 'banned':
        case 'closed':
            return 'danger';
        default:
            return 'secondary';
    }
}

/**
 * Get status badge HTML
 * @param string $status Status
 * @return string Status badge HTML
 */
function getStatusBadge($status) {
    $class = getStatusClass($status);
    return '<span class="badge bg-' . $class . '">' . ucfirst($status) . '</span>';
}

/**
 * Get alert HTML
 * @param string $message Alert message
 * @param string $type Alert type (success, danger, warning, info)
 * @param bool $dismissible Is alert dismissible
 * @return string Alert HTML
 */
function getAlert($message, $type = 'info', $dismissible = true) {
    $html = '<div class="alert alert-' . $type . ($dismissible ? ' alert-dismissible fade show' : '') . '" role="alert">';
    $html .= $message;
    
    if ($dismissible) {
        $html .= '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>';
    }
    
    $html .= '</div>';
    
    return $html;
}

/**
 * Set flash message
 * @param string $message Flash message
 * @param string $type Message type (success, danger, warning, info)
 */
function setFlashMessage($message, $type = 'info') {
    $_SESSION['flash_message'] = [
        'message' => $message,
        'type' => $type
    ];
}

/**
 * Get flash message
 * @return string|null Flash message HTML or null if no message
 */
function getFlashMessage() {
    if (isset($_SESSION['flash_message'])) {
        $message = $_SESSION['flash_message']['message'];
        $type = $_SESSION['flash_message']['type'];
        
        unset($_SESSION['flash_message']);
        
        return getAlert($message, $type);
    }
    
    return null;
}

/**
 * Has flash message
 * @return bool True if flash message exists
 */
function hasFlashMessage() {
    return isset($_SESSION['flash_message']);
}

/**
 * Get form validation errors
 * @return array Validation errors
 */
function getValidationErrors() {
    return $_SESSION['validation_errors'] ?? [];
}

/**
 * Set form validation errors
 * @param array $errors Validation errors
 */
function setValidationErrors($errors) {
    $_SESSION['validation_errors'] = $errors;
}

/**
 * Clear form validation errors
 */
function clearValidationErrors() {
    unset($_SESSION['validation_errors']);
}

/**
 * Has form validation errors
 * @return bool True if validation errors exist
 */
function hasValidationErrors() {
    return isset($_SESSION['validation_errors']) && !empty($_SESSION['validation_errors']);
}

/**
 * Get form validation error
 * @param string $field Field name
 * @return string|null Validation error or null if no error
 */
function getValidationError($field) {
    $errors = getValidationErrors();
    return $errors[$field] ?? null;
}

/**
 * Has form validation error
 * @param string $field Field name
 * @return bool True if validation error exists
 */
function hasValidationError($field) {
    return getValidationError($field) !== null;
}

/**
 * Get form validation error HTML
 * @param string $field Field name
 * @return string Validation error HTML
 */
function getValidationErrorHtml($field) {
    $error = getValidationError($field);
    
    if ($error) {
        return '<div class="invalid-feedback">' . $error . '</div>';
    }
    
    return '';
}

/**
 * Get form field class
 * @param string $field Field name
 * @return string Form field class
 */
function getFormFieldClass($field) {
    return hasValidationError($field) ? 'is-invalid' : '';
}

/**
 * Get old form value
 * @param string $field Field name
 * @param mixed $default Default value
 * @return mixed Old form value
 */
function getOldFormValue($field, $default = '') {
    return $_SESSION['old_form_values'][$field] ?? $default;
}

/**
 * Set old form values
 * @param array $values Form values
 */
function setOldFormValues($values) {
    $_SESSION['old_form_values'] = $values;
}

/**
 * Clear old form values
 */
function clearOldFormValues() {
    unset($_SESSION['old_form_values']);
}

/**
 * Validate form data
 * @param array $data Form data
 * @param array $rules Validation rules
 * @return array Validation errors
 */
function validateFormData($data, $rules) {
    $errors = [];
    
    foreach ($rules as $field => $rule) {
        $value = $data[$field] ?? null;
        $ruleArray = explode('|', $rule);
        
        foreach ($ruleArray as $singleRule) {
            $ruleParts = explode(':', $singleRule);
            $ruleName = $ruleParts[0];
            $ruleParam = $ruleParts[1] ?? null;
            
            switch ($ruleName) {
                case 'required':
                    if (empty($value)) {
                        $errors[$field] = 'This field is required.';
                    }
                    break;
                
                case 'email':
                    if (!empty($value) && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                        $errors[$field] = 'Please enter a valid email address.';
                    }
                    break;
                
                case 'min':
                    if (!empty($value) && strlen($value) < $ruleParam) {
                        $errors[$field] = 'This field must be at least ' . $ruleParam . ' characters.';
                    }
                    break;
                
                case 'max':
                    if (!empty($value) && strlen($value) > $ruleParam) {
                        $errors[$field] = 'This field must not exceed ' . $ruleParam . ' characters.';
                    }
                    break;
                
                case 'numeric':
                    if (!empty($value) && !is_numeric($value)) {
                        $errors[$field] = 'This field must be a number.';
                    }
                    break;
                
                case 'integer':
                    if (!empty($value) && !filter_var($value, FILTER_VALIDATE_INT)) {
                        $errors[$field] = 'This field must be an integer.';
                    }
                    break;
                
                case 'url':
                    if (!empty($value) && !filter_var($value, FILTER_VALIDATE_URL)) {
                        $errors[$field] = 'Please enter a valid URL.';
                    }
                    break;
                
                case 'date':
                    if (!empty($value) && !strtotime($value)) {
                        $errors[$field] = 'Please enter a valid date.';
                    }
                    break;
                
                case 'matches':
                    if ($value !== ($data[$ruleParam] ?? null)) {
                        $errors[$field] = 'This field must match the ' . $ruleParam . ' field.';
                    }
                    break;
                
                case 'in':
                    $allowedValues = explode(',', $ruleParam);
                    if (!empty($value) && !in_array($value, $allowedValues)) {
                        $errors[$field] = 'This field must be one of: ' . $ruleParam . '.';
                    }
                    break;
            }
            
            // Stop validation for this field if there's already an error
            if (isset($errors[$field])) {
                break;
            }
        }
    }
    
    return $errors;
}

/**
 * Validate and process form
 * @param array $rules Validation rules
 * @param callable $callback Callback function to process form
 * @return bool True if form was processed successfully
 */
function validateAndProcessForm($rules, $callback) {
    if (isPostRequest()) {
        $data = $_POST;
        
        // Check CSRF token
        if (!checkCSRFToken()) {
            setFlashMessage('Invalid form submission. Please try again.', 'danger');
            setOldFormValues($data);
            return false;
        }
        
        // Validate form data
        $errors = validateFormData($data, $rules);
        
        if (empty($errors)) {
            // Process form
            $result = $callback($data);
            
            if ($result) {
                clearOldFormValues();
                clearValidationErrors();
                return true;
            } else {
                setOldFormValues($data);
                return false;
            }
        } else {
            setValidationErrors($errors);
            setOldFormValues($data);
            return false;
        }
    }
    
    return false;
}