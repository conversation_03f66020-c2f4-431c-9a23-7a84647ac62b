<?php
// <PERSON><PERSON><PERSON> to create facilities table
require_once 'includes/autoload.php';

// Override HTTP_HOST for CLI
$_SERVER['HTTP_HOST'] = 'localhost';

// Get database connection
$db = Database::getInstance();

// Check if facilities table already exists
$result = $db->query("SHOW TABLES LIKE 'facilities'");
if ($result->num_rows > 0) {
    echo "Facilities table already exists.\n";
    exit;
}

// Create facilities table
$sql = "CREATE TABLE `facilities` (
  `facility_id` int(11) NOT NULL AUTO_INCREMENT,
  `facility_name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `icon` varchar(50) DEFAULT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`facility_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;";

if ($db->query($sql)) {
    echo "Facilities table created successfully.\n";
    
    // Insert some default facilities
    $facilities = [
        ['facility_name' => 'Wi-Fi', 'description' => 'Free Wi-Fi access', 'icon' => 'wifi', 'status' => 'active', 'created_at' => date('Y-m-d H:i:s')],
        ['facility_name' => 'Air Conditioning', 'description' => 'Air conditioned classrooms', 'icon' => 'snowflake', 'status' => 'active', 'created_at' => date('Y-m-d H:i:s')],
        ['facility_name' => 'Library', 'description' => 'Access to library resources', 'icon' => 'book', 'status' => 'active', 'created_at' => date('Y-m-d H:i:s')],
        ['facility_name' => 'Computer Lab', 'description' => 'Modern computer facilities', 'icon' => 'desktop', 'status' => 'active', 'created_at' => date('Y-m-d H:i:s')],
        ['facility_name' => 'Cafeteria', 'description' => 'On-site food services', 'icon' => 'utensils', 'status' => 'active', 'created_at' => date('Y-m-d H:i:s')],
        ['facility_name' => 'Parking', 'description' => 'Parking facilities available', 'icon' => 'parking', 'status' => 'active', 'created_at' => date('Y-m-d H:i:s')],
        ['facility_name' => 'Study Rooms', 'description' => 'Dedicated study spaces', 'icon' => 'door-open', 'status' => 'active', 'created_at' => date('Y-m-d H:i:s')],
        ['facility_name' => 'Sports Facilities', 'description' => 'Access to sports equipment and grounds', 'icon' => 'futbol', 'status' => 'active', 'created_at' => date('Y-m-d H:i:s')]
    ];
    
    foreach ($facilities as $facility) {
        $db->insert('facilities', $facility);
    }
    
    echo "Default facilities added.\n";
} else {
    echo "Error creating facilities table.\n";
}
?>