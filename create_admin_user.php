<?php
/**
 * Create Admin User
 * This script creates an admin user with username 'admin' and password 'admin123'
 */

// Include database configuration
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'coaching_directory');

// Connect to database
$conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "<h1>Create Admin User</h1>";

// Check if users table exists
$result = $conn->query("SHOW TABLES LIKE 'users'");
if ($result->num_rows == 0) {
    echo "<p>Creating users table...</p>";
    
    $sql = "CREATE TABLE `users` (
        `user_id` int(11) NOT NULL AUTO_INCREMENT,
        `username` varchar(50) NOT NULL,
        `email` varchar(100) NOT NULL,
        `password` varchar(255) NOT NULL,
        `first_name` varchar(50) DEFAULT NULL,
        `last_name` varchar(50) DEFAULT NULL,
        `phone` varchar(20) DEFAULT NULL,
        `profile_image` varchar(255) DEFAULT NULL,
        `user_type` enum('admin','coaching_owner','student','parent') NOT NULL DEFAULT 'student',
        `is_verified` tinyint(1) NOT NULL DEFAULT '0',
        `verification_token` varchar(64) DEFAULT NULL,
        `reset_token` varchar(64) DEFAULT NULL,
        `reset_token_expiry` datetime DEFAULT NULL,
        `status` enum('active','inactive','banned') NOT NULL DEFAULT 'active',
        `last_login` datetime DEFAULT NULL,
        `created_at` datetime NOT NULL,
        `updated_at` datetime DEFAULT NULL,
        PRIMARY KEY (`user_id`),
        UNIQUE KEY `username` (`username`),
        UNIQUE KEY `email` (`email`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    if ($conn->query($sql) === TRUE) {
        echo "<p>Users table created successfully.</p>";
    } else {
        echo "<p>Error creating users table: " . $conn->error . "</p>";
    }
}

// Check if admin user exists
$stmt = $conn->prepare("SELECT user_id FROM users WHERE username = ?");
$stmt->bind_param("s", $username);
$username = "admin";
$stmt->execute();
$stmt->store_result();

if ($stmt->num_rows > 0) {
    echo "<p>Admin user already exists. Updating password...</p>";
    
    // Update admin user
    $stmt = $conn->prepare("UPDATE users SET password = ?, is_verified = 1, status = 'active', user_type = 'admin' WHERE username = ?");
    $stmt->bind_param("ss", $password, $username);
    $password = password_hash("admin123", PASSWORD_DEFAULT);
    $username = "admin";
    
    if ($stmt->execute()) {
        echo "<p>Admin user updated successfully.</p>";
    } else {
        echo "<p>Error updating admin user: " . $stmt->error . "</p>";
    }
} else {
    echo "<p>Creating admin user...</p>";
    
    // Create admin user
    $stmt = $conn->prepare("INSERT INTO users (username, email, password, first_name, last_name, user_type, is_verified, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
    $stmt->bind_param("sssssssss", $username, $email, $password, $firstName, $lastName, $userType, $isVerified, $status, $createdAt);
    
    $username = "admin";
    $email = "<EMAIL>";
    $password = password_hash("admin123", PASSWORD_DEFAULT);
    $firstName = "Admin";
    $lastName = "User";
    $userType = "admin";
    $isVerified = 1;
    $status = "active";
    $createdAt = date("Y-m-d H:i:s");
    
    if ($stmt->execute()) {
        echo "<p>Admin user created successfully.</p>";
    } else {
        echo "<p>Error creating admin user: " . $stmt->error . "</p>";
    }
}

// Display admin user details
$result = $conn->query("SELECT user_id, username, email, user_type, is_verified, status FROM users WHERE username = 'admin'");
if ($result->num_rows > 0) {
    $row = $result->fetch_assoc();
    echo "<h2>Admin User Details</h2>";
    echo "<p>User ID: " . $row["user_id"] . "</p>";
    echo "<p>Username: " . $row["username"] . "</p>";
    echo "<p>Email: " . $row["email"] . "</p>";
    echo "<p>User Type: " . $row["user_type"] . "</p>";
    echo "<p>Verified: " . ($row["is_verified"] ? "Yes" : "No") . "</p>";
    echo "<p>Status: " . $row["status"] . "</p>";
}

// Close connection
$conn->close();

echo "<p>Done! You can now <a href='admin/index.php'>login to the admin panel</a> with username 'admin' and password 'admin123'.</p>";