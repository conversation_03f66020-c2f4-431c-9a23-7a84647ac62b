<?php
// <PERSON><PERSON><PERSON> to add created_at column to states table
require_once 'includes/autoload.php';

// Override HTTP_HOST for CLI
$_SERVER['HTTP_HOST'] = 'localhost';

// Get database connection
$db = Database::getInstance();
$conn = $db->getConnection();

// Check if created_at column already exists
$result = $conn->query("SHOW COLUMNS FROM states LIKE 'created_at'");
if ($result->num_rows > 0) {
    echo "created_at column already exists in states table.\n";
    exit;
}

// Add created_at column
$sql = "ALTER TABLE states ADD COLUMN created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP";
if ($conn->query($sql)) {
    echo "created_at column added to states table.\n";
} else {
    echo "Error adding created_at column: " . $conn->error . "\n";
}

// Add updated_at column for consistency
$result = $conn->query("SHOW COLUMNS FROM states LIKE 'updated_at'");
if ($result->num_rows == 0) {
    $sql = "ALTER TABLE states ADD COLUMN updated_at timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP";
    if ($conn->query($sql)) {
        echo "updated_at column added to states table.\n";
    } else {
        echo "Error adding updated_at column: " . $conn->error . "\n";
    }
}
?>