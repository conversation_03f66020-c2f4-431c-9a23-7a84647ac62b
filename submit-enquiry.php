<?php
/**
 * Submit Enquiry Handler
 * This file handles the submission of enquiry forms from coaching center pages
 */
require_once 'includes/autoload.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Initialize database
$db = Database::getInstance();

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $coachingId = isset($_POST['coaching_id']) ? (int)$_POST['coaching_id'] : 0;
    $name = isset($_POST['name']) ? trim($_POST['name']) : '';
    $email = isset($_POST['email']) ? trim($_POST['email']) : '';
    $phone = isset($_POST['phone']) ? trim($_POST['phone']) : '';
    $courseId = isset($_POST['course']) ? (int)$_POST['course'] : 0;
    $locationId = isset($_POST['location_id']) ? (int)$_POST['location_id'] : 0;
    $message = isset($_POST['message']) ? trim($_POST['message']) : '';


    
    // Validate required fields
    $errors = [];
    
    if (empty($coachingId)) {
        $errors[] = 'Coaching center information is missing.';
    }
    
    if (empty($name)) {
        $errors[] = 'Name is required.';
    }
    
    if (empty($email)) {
        $errors[] = 'Email is required.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Please enter a valid email address.';
    }
    
    if (empty($phone)) {
        $errors[] = 'Phone number is required.';
    }
    
    // If no errors, save the enquiry
    if (empty($errors)) {
        try {
            // Get coaching center info for notification
            $coachingObj = new CoachingCenter();
            $coaching = $coachingObj->getById($coachingId);




            
            // Get course name if course ID is provided
            $courseName = '';
            if ($courseId) {
                $courseData = $db->fetchOne(
                    "SELECT course_name FROM courses WHERE course_id = ?",
                    [$courseId]
                );
                $courseName = ($courseData && is_array($courseData)) ? $courseData['course_name'] : '';
            }
            
            // Get location name if location ID is provided
            $locationName = '';
            if ($locationId) {
                $locationData = $db->fetchOne(
                    "SELECT l.location_name, c.city_name 
                     FROM locations l 
                     LEFT JOIN cities c ON l.city_id = c.city_id 
                     WHERE l.location_id = ?",
                    [$locationId]
                );
                if ($locationData && is_array($locationData)) {
                    $locationName = $locationData['location_name'];
                    if (!empty($locationData['city_name'])) {
                        $locationName .= ', ' . $locationData['city_name'];
                    }
                }
            }
            
            // Insert enquiry into database
            $result = $db->insert('enquiries', [
                'coaching_id' => $coachingId,
                'course_id' => $courseId ?: null,
                'location_id' => $locationId ?: null,
                'name' => $name,
                'email' => $email,
                'phone' => $phone,
                'message' => $message,
                'status' => 'new',
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            if ($result) {
                // Set success message in session
                $_SESSION['enquiry_success'] = true;
                
                // Send notification email to coaching center if email is available
                if ($coaching && is_array($coaching) && !empty($coaching['email'])) {
                    $subject = "New Enquiry from " . $name;
                    
                    $emailBody = "Dear Admin,\n\n";
                    $emailBody .= "You have received a new enquiry from your coaching center listing.\n\n";
                    $emailBody .= "Enquiry Details:\n";
                    $emailBody .= "Name: " . $name . "\n";
                    $emailBody .= "Email: " . $email . "\n";
                    $emailBody .= "Phone: " . $phone . "\n";
                    
                    if (!empty($courseName)) {
                        $emailBody .= "Course: " . $courseName . "\n";
                    }
                    
                    if (!empty($locationName)) {
                        $emailBody .= "Preferred Location: " . $locationName . "\n";
                    }
                    
                    if (!empty($message)) {
                        $emailBody .= "Message: " . $message . "\n";
                    }
                    
                    $emailBody .= "\nPlease log in to your coaching panel to respond to this enquiry.\n\n";
                    $emailBody .= "Regards,\nCoaching Directory Team";
                    
                    // Send email (using mail function for simplicity)
                    @mail($coaching['email'], $subject, $emailBody);
                }
                
                // Always redirect to homepage after successful submission
                header("Location: http://localhost/coaching/?enquiry=success");
                exit;
            } else {
                // Set error message in session
                $_SESSION['enquiry_error'] = 'Failed to submit enquiry. Please try again.';
                
                // Redirect back to the coaching center page
                $slug = ($coaching && is_array($coaching) && isset($coaching['slug'])) ? $coaching['slug'] : '';
                if (!empty($slug)) {
                    header("Location: http://localhost/coaching/center/{$slug}?enquiry=error");
                    exit;
                } else {
                    header("Location: http://localhost/coaching/index.php?enquiry=error");
                    exit;
                }
            }
        } catch (Exception $e) {
            // Log the error
            error_log("Error submitting enquiry: " . $e->getMessage());
            
            // Set error message in session
            $_SESSION['enquiry_error'] = 'An error occurred while submitting your enquiry. Please try again.';
            
            // Redirect back to the coaching center page
            $slug = ($coaching && is_array($coaching) && isset($coaching['slug'])) ? $coaching['slug'] : '';
            if (!empty($slug)) {
                header("Location: http://localhost/coaching/center/{$slug}?enquiry=error");
                exit;
            } else {
                header("Location: http://localhost/coaching/index.php?enquiry=error");
                exit;
            }
        }
    } else {
        // Set error messages in session
        $_SESSION['enquiry_errors'] = $errors;
        
        // Redirect back to the coaching center page
        $coachingObj = new CoachingCenter();
        $coaching = $coachingObj->getById($coachingId);
        $slug = ($coaching && is_array($coaching) && isset($coaching['slug'])) ? $coaching['slug'] : '';

        if (!empty($slug)) {
            header("Location: http://localhost/coaching/center/{$slug}?enquiry=error");
            exit;
        } else {
            header("Location: http://localhost/coaching/index.php?enquiry=error");
            exit;
        }
    }
} else {
    // If not a POST request, redirect to home page
    header("Location: http://localhost/coaching/index.php");
    exit;
}