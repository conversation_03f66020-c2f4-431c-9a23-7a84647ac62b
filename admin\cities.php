<?php
/**
 * Admin Cities
 */
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    require_once '../includes/autoload.php';
    
    // Check user authentication
    $user = new User();
    if (!$user->isLoggedIn() || !$user->isAdmin()) {
        header('Location: login.php');
        exit;
    }
    
    // Get admin info
    $adminInfo = $user->getUserData();
    
    // Initialize settings
    $settings = Settings::getInstance();
    
    // Initialize city object
    $cityObj = new City();
    
    // Initialize state object for dropdown
    $stateObj = new State();
    $statesData = $stateObj->getAll(['status' => 'active']);
    $states = $statesData['states'] ?? [];
    
    // Handle actions
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    $cityId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    $stateFilter = isset($_GET['state_id']) ? (int)$_GET['state_id'] : 0;
    $message = '';
    
    if ($action === 'delete' && $cityId > 0) {
        if ($cityObj->delete($cityId)) {
            $message = '<div class="alert alert-success">City deleted successfully.</div>';
        } else {
            $message = '<div class="alert alert-danger">Failed to delete city. It may be in use.</div>';
        }
    }
    
    // Handle form submission for adding/editing city
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $cityName = trim($_POST['city_name']);
        $stateId = (int)$_POST['state_id'];
        $status = $_POST['status'];
        
        if (empty($cityName) || $stateId <= 0) {
            $message = '<div class="alert alert-danger">City name and state are required.</div>';
        } else {
            $cityData = [
                'city_name' => $cityName,
                'state_id' => $stateId,
                'status' => $status
            ];
            
            if (isset($_POST['edit_id']) && $_POST['edit_id'] > 0) {
                // Update existing city
                $editId = (int)$_POST['edit_id'];
                if ($cityObj->update($editId, $cityData)) {
                    $message = '<div class="alert alert-success">City updated successfully.</div>';
                } else {
                    $message = '<div class="alert alert-danger">Failed to update city.</div>';
                }
            } else {
                // Add new city
                $cityData['slug'] = Utility::generateSlug($cityName);
                $cityData['created_at'] = date('Y-m-d H:i:s');
                
                if ($cityObj->create($cityData)) {
                    $message = '<div class="alert alert-success">City added successfully.</div>';
                } else {
                    $message = '<div class="alert alert-danger">Failed to add city.</div>';
                }
            }
        }
    }
    
    // Get city to edit
    $editCity = null;
    if ($action === 'edit' && $cityId > 0) {
        $editCity = $cityObj->getById($cityId);
    }
    
    // Get all cities with optional state filter
    $filters = [];
    if ($stateFilter > 0) {
        $filters['state_id'] = $stateFilter;
    }
    
    $citiesData = $cityObj->getAll($filters);
    $cities = $citiesData['cities'] ?? [];
    
} catch (Exception $e) {
    die('<div style="color:red;padding:20px;border:2px solid red;margin:20px;">'
        . '<h2>Error</h2>'
        . '<p><strong>Message:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>'
        . '<p><strong>File:</strong> ' . htmlspecialchars($e->getFile()) . '</p>'
        . '<p><strong>Line:</strong> ' . htmlspecialchars($e->getLine()) . '</p>'
        . '</div>');
}

// Page title
$pageTitle = 'Cities';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Admin Panel | <?php echo $settings->getSiteName(); ?></title>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo getAssetUrl($settings->getSiteFavicon()); ?>" type="image/x-icon">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Admin CSS -->
    <link rel="stylesheet" href="assets/css/admin.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <?php include 'templates/sidebar.php'; ?>
        
        <!-- Main Content -->
        <div class="admin-main" id="adminMain">
            <!-- Header -->
            <?php include 'templates/header.php'; ?>
            
            <!-- Content -->
            <div class="admin-content">
                <div class="page-title">
                    <div>
                        <h1><?php echo $pageTitle; ?></h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="dashboard.php">Home</a></li>
                                <li class="breadcrumb-item">Locations</li>
                                <li class="breadcrumb-item active" aria-current="page"><?php echo $pageTitle; ?></li>
                            </ol>
                        </nav>
                    </div>
                </div>
                
                <?php echo $message; ?>
                
                <!-- Filter by State -->
                <?php if (!$editCity): ?>
                    <div class="admin-card mb-4">
                        <div class="admin-card-body">
                            <form action="" method="get" class="row g-3">
                                <div class="col-md-6">
                                    <label for="state_filter" class="form-label">Filter by State</label>
                                    <select class="form-select" id="state_filter" name="state_id">
                                        <option value="">All States</option>
                                        <?php foreach ($states as $state): ?>
                                            <option value="<?php echo $state['state_id']; ?>" <?php echo $stateFilter == $state['state_id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($state['state_name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-3 d-flex align-items-end">
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-filter"></i> Filter
                                    </button>
                                </div>
                                <div class="col-md-3 d-flex align-items-end">
                                    <a href="cities.php" class="btn btn-secondary w-100">
                                        <i class="fas fa-sync-alt"></i> Reset
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                <?php endif; ?>
                
                <div class="row">
                    <!-- Add/Edit City Form -->
                    <div class="col-md-4">
                        <div class="admin-card">
                            <div class="admin-card-header">
                                <h2 class="admin-card-title">
                                    <i class="fas fa-city me-2"></i> <?php echo $editCity ? 'Edit City' : 'Add New City'; ?>
                                </h2>
                            </div>
                            <div class="admin-card-body">
                                <form action="" method="post">
                                    <?php if ($editCity): ?>
                                        <input type="hidden" name="edit_id" value="<?php echo $editCity['city_id']; ?>">
                                    <?php endif; ?>
                                    
                                    <div class="mb-3">
                                        <label for="city_name" class="form-label">City Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="city_name" name="city_name" value="<?php echo $editCity ? htmlspecialchars($editCity['city_name']) : ''; ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="state_id" class="form-label">State <span class="text-danger">*</span></label>
                                        <select class="form-select" id="state_id" name="state_id" required>
                                            <option value="">Select State</option>
                                            <?php foreach ($states as $state): ?>
                                                <option value="<?php echo $state['state_id']; ?>" <?php echo $editCity && $editCity['state_id'] == $state['state_id'] ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($state['state_name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="status" class="form-label">Status</label>
                                        <select class="form-select" id="status" name="status">
                                            <option value="active" <?php echo $editCity && $editCity['status'] === 'active' ? 'selected' : ''; ?>>Active</option>
                                            <option value="inactive" <?php echo $editCity && $editCity['status'] === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                                        </select>
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <?php if ($editCity): ?>
                                            <button type="submit" class="btn btn-primary">Update City</button>
                                            <a href="cities.php<?php echo $stateFilter ? '?state_id=' . $stateFilter : ''; ?>" class="btn btn-secondary">Cancel</a>
                                        <?php else: ?>
                                            <button type="submit" class="btn btn-primary">Add City</button>
                                        <?php endif; ?>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Cities List -->
                    <div class="col-md-8">
                        <div class="admin-card">
                            <div class="admin-card-header">
                                <h2 class="admin-card-title">
                                    <i class="fas fa-list me-2"></i> All Cities
                                    <?php if ($stateFilter && !empty($states)): ?>
                                        <?php foreach ($states as $state): ?>
                                            <?php if ($state['state_id'] == $stateFilter): ?>
                                                <span class="text-muted fs-6 ms-2">in <?php echo htmlspecialchars($state['state_name']); ?></span>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </h2>
                                <span class="badge bg-primary"><?php echo count($cities); ?> Total</span>
                            </div>
                            <div class="admin-card-body p-0">
                                <div class="table-responsive">
                                    <table class="admin-table">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Name</th>
                                                <th>State</th>
                                                <th>Slug</th>
                                                <th>Status</th>
                                                <th>Locations</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (!empty($cities)): ?>
                                                <?php foreach ($cities as $city): ?>
                                                    <tr>
                                                        <td><?php echo $city['city_id']; ?></td>
                                                        <td><?php echo htmlspecialchars($city['city_name']); ?></td>
                                                        <td><?php echo htmlspecialchars($city['state_name']); ?></td>
                                                        <td><?php echo htmlspecialchars($city['slug']); ?></td>
                                                        <td>
                                                            <?php if ($city['status'] === 'active'): ?>
                                                                <span class="badge bg-success">Active</span>
                                                            <?php else: ?>
                                                                <span class="badge bg-danger">Inactive</span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <?php echo isset($city['location_count']) ? $city['location_count'] : 0; ?>
                                                            <a href="locations.php?city_id=<?php echo $city['city_id']; ?>" class="ms-1 text-primary">
                                                                <i class="fas fa-external-link-alt"></i>
                                                            </a>
                                                        </td>
                                                        <td>
                                                            <div class="btn-group">
                                                                <a href="cities.php?action=edit&id=<?php echo $city['city_id']; ?><?php echo $stateFilter ? '&state_id=' . $stateFilter : ''; ?>" class="btn btn-sm btn-primary" title="Edit">
                                                                    <i class="fas fa-edit"></i>
                                                                </a>
                                                                <a href="cities.php?action=delete&id=<?php echo $city['city_id']; ?><?php echo $stateFilter ? '&state_id=' . $stateFilter : ''; ?>" class="btn btn-sm btn-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this city? This will also delete all locations in this city.');">
                                                                    <i class="fas fa-trash"></i>
                                                                </a>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="7" class="text-center">No cities found.</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Footer -->
            <?php include 'templates/footer.php'; ?>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Admin JS -->
    <script src="assets/js/admin.js"></script>
</body>
</html>