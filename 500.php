<?php
/**
 * 500 Error Page
 */
require_once 'includes/autoload.php';

// Get settings
$settings = Settings::getInstance();

// Set HTTP status code
http_response_code(500);

// Page title and meta
$pageTitle = 'Server Error';
$pageDescription = 'An unexpected error occurred. Please try again later.';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?php echo getMetaTags($pageTitle . ' - ' . $settings->getSiteName(), $pageDescription); ?>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo getAssetUrl($settings->getSiteFavicon()); ?>" type="image/x-icon">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo getAssetUrl('css/style.css'); ?>">
    
    <style>
        .error-page {
            padding: 100px 0;
            text-align: center;
        }
        
        .error-code {
            font-size: 150px;
            font-weight: 700;
            color: var(--danger);
            margin-bottom: 20px;
            text-shadow: 0 0 20px rgba(220, 53, 69, 0.5);
            position: relative;
        }
        
        .error-code:after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 200px;
            height: 200px;
            background: radial-gradient(circle, rgba(220, 53, 69, 0.2) 0%, rgba(220, 53, 69, 0) 70%);
            border-radius: 50%;
            z-index: -1;
            animation: pulse 2s infinite;
        }
        
        .error-title {
            font-size: 36px;
            font-weight: 700;
            color: var(--dark-blue);
            margin-bottom: 20px;
        }
        
        .error-message {
            font-size: 18px;
            color: var(--dark-gray);
            margin-bottom: 30px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .error-actions {
            margin-top: 30px;
        }
        
        .error-actions .btn {
            margin: 0 10px;
            padding: 12px 30px;
        }
        
        @keyframes pulse {
            0% {
                transform: translate(-50%, -50%) scale(1);
                opacity: 0.5;
            }
            50% {
                transform: translate(-50%, -50%) scale(1.2);
                opacity: 0.2;
            }
            100% {
                transform: translate(-50%, -50%) scale(1);
                opacity: 0.5;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <?php include 'templates/header.php'; ?>
    
    <!-- Error Section -->
    <section class="error-page">
        <div class="container">
            <div class="error-content">
                <div class="error-code">500</div>
                <h1 class="error-title">Server Error</h1>
                <p class="error-message">Oops! Something went wrong on our end. We're working to fix the issue. Please try again later or contact our support team if the problem persists.</p>
                
                <div class="error-actions">
                    <a href="<?php echo getBaseUrl(); ?>" class="btn btn-primary hover-glow"><i class="fas fa-home"></i> Go to Homepage</a>
                    <a href="<?php echo getBaseUrl(); ?>contact.php" class="btn btn-outline-primary"><i class="fas fa-envelope"></i> Contact Support</a>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Footer -->
    <?php include 'templates/footer.php'; ?>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="<?php echo getAssetUrl('js/main.js'); ?>"></script>
</body>
</html>