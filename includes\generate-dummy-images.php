<?php
/**
 * Generate Dummy Images
 * This script provides URLs for placeholder images from external services
 */

/**
 * Get a placeholder image URL
 * 
 * @param int $width Width of the image
 * @param int $height Height of the image
 * @param string $text Text to display on the image
 * @param string $bgColor Background color in hex format (without #)
 * @param string $textColor Text color in hex format (without #)
 * @return string Placeholder image URL
 */
function getPlaceholderImage($width, $height, $text = '', $bgColor = '003366', $textColor = 'FFFFFF') {
    // Use placeholder.com service
    $url = "https://via.placeholder.com/{$width}x{$height}/{$bgColor}/{$textColor}";
    
    if (!empty($text)) {
        $url .= "?text=" . urlencode($text);
    }
    
    return $url;
}

/**
 * Get coaching logo URL
 * 
 * @param int $id Coaching ID
 * @param string $name Coaching name
 * @return string Placeholder image URL
 */
function getCoachingLogoUrl($id, $name = '') {
    $colors = [
        '003366',    // Dark Blue
        '66B2FF',    // Light Blue
        '009900',    // Green
        'CC0000',    // Red
        '993399',    // Purple
        'FF9900',    // Orange
        '009999',    // Teal
        '333333',    // Dark Gray
        '990099',    // Magenta
        '006633'     // Dark Green
    ];
    
    $color = $colors[($id - 1) % count($colors)];
    $text = !empty($name) ? $name : "Coaching {$id}";
    
    return getPlaceholderImage(200, 200, $text, $color);
}

/**
 * Get category icon URL
 * 
 * @param string $key Category key
 * @param string $name Category name
 * @return string Placeholder image URL
 */
function getCategoryIconUrl($key, $name) {
    return getPlaceholderImage(100, 100, $name, '66B2FF');
}

/**
 * Get city image URL
 * 
 * @param string $key City key
 * @param string $name City name
 * @return string Placeholder image URL
 */
function getCityImageUrl($key, $name) {
    return getPlaceholderImage(400, 250, $name, '333333');
}

/**
 * Get blog image URL
 * 
 * @param string $key Blog key
 * @param string $name Blog title
 * @return string Placeholder image URL
 */
function getBlogImageUrl($key, $name) {
    return getPlaceholderImage(800, 500, $name, '006699');
}

/**
 * Get testimonial image URL
 * 
 * @param int $id Testimonial ID
 * @return string Placeholder image URL
 */
function getTestimonialImageUrl($id) {
    return getPlaceholderImage(150, 150, "User {$id}", '666666');
}

/**
 * Get hero image URL
 * 
 * @return string Placeholder image URL
 */
function getHeroImageUrl() {
    return getPlaceholderImage(800, 600, "Find Coaching Centers", '003366');
}

// Example usage:
// echo getCoachingLogoUrl(1, 'Brilliant Academy');
// echo getCategoryIconUrl('engineering', 'Engineering');
// echo getCityImageUrl('delhi', 'Delhi');
// echo getBlogImageUrl('jee', 'JEE Preparation');
// echo getTestimonialImageUrl(1);
// echo getHeroImageUrl();