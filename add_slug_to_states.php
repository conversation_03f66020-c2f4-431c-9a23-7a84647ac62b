<?php
// <PERSON><PERSON><PERSON> to add slug column to states table
require_once 'includes/autoload.php';

// Override HTTP_HOST for CLI
$_SERVER['HTTP_HOST'] = 'localhost';

// Get database connection
$db = Database::getInstance();
$conn = $db->getConnection();

// Check if slug column already exists
$result = $conn->query("SHOW COLUMNS FROM states LIKE 'slug'");
if ($result->num_rows > 0) {
    echo "Slug column already exists in states table.\n";
    exit;
}

// Add slug column
$sql = "ALTER TABLE states ADD COLUMN slug VARCHAR(100) AFTER state_name";
if ($conn->query($sql)) {
    echo "Slug column added to states table.\n";
    
    // Update existing states with slugs
    $sql = "UPDATE states SET slug = LOWER(REPLACE(state_name, ' ', '-'))";
    if ($conn->query($sql)) {
        echo "Slugs updated for existing states.\n";
    } else {
        echo "Error updating slugs: " . $conn->error . "\n";
    }
} else {
    echo "Error adding slug column: " . $conn->error . "\n";
}
?>