/**
 * Main Stylesheet
 */

/* ===== Variables ===== */
:root {
    /* Primary Colors */
    --dark-blue: #003366;
    --light-blue: #66B2FF;
    --white: #FFFFFF;
    
    /* Secondary Colors */
    --light-gray: #F5F5F5;
    --dark-gray: #333333;
    
    /* Glowing/Accent Colors */
    --electric-cyan: #00FFFF;
    --neon-green: #00FF00;
    --magenta: #FF00FF;
    
    /* Text Colors */
    --dark-text: #1A1A1A;
    --light-text: #F5F5F5;
    
    /* Other Colors */
    --success: #28a745;
    --danger: #dc3545;
    --warning: #ffc107;
    --info: #17a2b8;
    
    /* Spacing */
    --spacing-xs: 5px;
    --spacing-sm: 10px;
    --spacing-md: 15px;
    --spacing-lg: 20px;
    --spacing-xl: 30px;
    --spacing-xxl: 50px;
    
    /* Border Radius */
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;
    
    /* Box Shadow */
    --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --box-shadow-hover: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* ===== Base Styles ===== */
body {
    font-family: 'Roboto', sans-serif;
    color: var(--dark-text);
    line-height: 1.5;
    background-color: var(--white);
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    color: var(--dark-blue);
}

h1 {
    font-size: 40px;
}

h2 {
    font-size: 30px;
}

h3 {
    font-size: 24px;
}

h4 {
    font-size: 20px;
}

h5 {
    font-size: 18px;
}

h6 {
    font-size: 16px;
}

p {
    margin-bottom: var(--spacing-md);
}

a {
    color: var(--light-blue);
    text-decoration: none;
    transition: all 0.3s ease;
}

a:hover {
    color: var(--dark-blue);
}

img {
    max-width: 100%;
    height: auto;
}

.section-padding {
    padding: var(--spacing-xxl) 0;
}

.bg-light {
    background-color: var(--light-gray);
}

.bg-dark {
    background-color: var(--dark-blue);
}

.bg-dark-light {
    background-color: #004080;
}

.bg-darker {
    background-color: #002040;
}

.text-light {
    color: var(--light-text);
}

/* ===== Buttons ===== */
.btn {
    padding: 12px 30px;
    border-radius: var(--border-radius-md);
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-sm {
    padding: 8px 20px;
    font-size: 14px;
}

.btn-lg {
    padding: 15px 40px;
    font-size: 18px;
}

.btn-primary {
    background-color: var(--light-blue);
    border-color: var(--light-blue);
    color: var(--white);
}

.btn-primary:hover {
    background-color: var(--dark-blue);
    border-color: var(--dark-blue);
    color: var(--white);
}

.btn-outline-primary {
    border-color: var(--light-blue);
    color: var(--light-blue);
}

.btn-outline-primary:hover {
    background-color: var(--light-blue);
    color: var(--white);
}

/* ===== Forms ===== */
.form-control {
    padding: 12px 15px;
    border-radius: var(--border-radius-md);
    border: 1px solid #E0E0E0;
}

.form-control:focus {
    border-color: var(--light-blue);
    box-shadow: 0 0 0 0.2rem rgba(102, 178, 255, 0.25);
}

.form-select {
    padding: 12px 15px;
    border-radius: var(--border-radius-md);
    border: 1px solid #E0E0E0;
}

.form-select:focus {
    border-color: var(--light-blue);
    box-shadow: 0 0 0 0.2rem rgba(102, 178, 255, 0.25);
}

.search-form {
    background-color: var(--white);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    box-shadow: var(--box-shadow);
    margin-top: var(--spacing-xl);
}

/* ===== Header ===== */
.site-header {
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: var(--box-shadow);
}

.top-bar {
    padding: 10px 0;
    font-size: 14px;
}

.contact-info span {
    margin-right: var(--spacing-md);
}

.social-links a {
    color: var(--light-text);
    margin-left: var(--spacing-sm);
    font-size: 16px;
}

.social-links a:hover {
    color: var(--electric-cyan);
}

.main-header {
    background-color: var(--white);
    padding: 15px 0;
}

.logo {
    height: 50px;
}

.navbar-nav .nav-link {
    padding: 10px 15px;
    color: var(--dark-text);
    font-weight: 500;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--light-blue);
}

.navbar-nav .nav-link.btn {
    padding: 8px 20px;
    margin-left: 10px;
}

.dropdown-menu {
    border: none;
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
}

.dropdown-item {
    padding: 8px 15px;
    font-weight: 500;
}

.dropdown-item:hover {
    background-color: var(--light-gray);
    color: var(--light-blue);
}

/* ===== Hero Section ===== */
.hero-section {
    padding: var(--spacing-xxl) 0;
    background-color: var(--dark-blue);
    position: relative;
    overflow: hidden;
    color: var(--white);
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
}

.hero-background .overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 51, 102, 0.95) 0%, rgba(0, 0, 51, 0.85) 100%);
    z-index: 1;
}

.glowing-circles {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 0;
}

.circle {
    position: absolute;
    border-radius: 50%;
    filter: blur(60px);
    opacity: 0.5;
    animation: pulse 8s infinite alternate;
}

.circle-1 {
    top: -100px;
    right: -100px;
    width: 300px;
    height: 300px;
    background-color: var(--electric-cyan);
    animation-delay: 0s;
}

.circle-2 {
    bottom: -150px;
    left: -50px;
    width: 400px;
    height: 400px;
    background-color: var(--magenta);
    animation-delay: 2s;
}

.circle-3 {
    top: 40%;
    left: 30%;
    width: 200px;
    height: 200px;
    background-color: var(--neon-green);
    animation-delay: 4s;
}

.hero-content {
    position: relative;
    z-index: 2;
    margin-bottom: var(--spacing-xl);
}

.hero-content h1 {
    margin-bottom: var(--spacing-md);
    color: var(--white);
    font-size: 48px;
    font-weight: 700;
    text-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

.hero-content p {
    font-size: 18px;
    margin-bottom: var(--spacing-xl);
    color: var(--light-text);
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 15px;
    margin-bottom: var(--spacing-xl);
}

.hero-buttons .btn {
    padding: 12px 30px;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.hero-buttons .btn-outline-primary {
    background-color: transparent;
    border-color: var(--light-blue);
    color: var(--white);
}

.hero-buttons .btn-outline-primary:hover {
    background-color: var(--light-blue);
    color: var(--white);
}

.hero-image {
    position: relative;
    z-index: 2;
    text-align: center;
}

.hero-image img {
    border-radius: var(--border-radius-lg);
    box-shadow: 0 0 30px rgba(0, 255, 255, 0.3);
    transform: perspective(1000px) rotateY(-5deg);
    transition: all 0.5s ease;
}

.hero-image img:hover {
    transform: perspective(1000px) rotateY(0deg);
    box-shadow: 0 0 40px rgba(0, 255, 255, 0.5);
}

.search-form-container {
    position: relative;
    z-index: 3;
    margin-top: -30px;
}

.search-form {
    background-color: var(--white);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.search-form label {
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--dark-blue);
}

.search-form .input-group-text {
    background-color: var(--light-gray);
    border-color: #E0E0E0;
    color: var(--dark-blue);
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.5;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 0.5;
    }
}

/* ===== Categories Section ===== */
.section-header {
    margin-bottom: var(--spacing-xl);
}

.section-header h2 {
    margin-bottom: var(--spacing-sm);
    position: relative;
    display: inline-block;
    padding-bottom: 15px;
}

.section-header h2.with-line:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, var(--light-blue), var(--electric-cyan));
    border-radius: 3px;
}

.section-header p {
    font-size: 18px;
    color: var(--dark-gray);
    max-width: 700px;
    margin: 0 auto;
}

.category-card {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--box-shadow);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    text-decoration: none;
    position: relative;
    overflow: hidden;
    height: 100%;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-hover);
}

.category-card:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, var(--light-blue), var(--electric-cyan));
    opacity: 0;
    transition: all 0.3s ease;
}

.category-card:hover:before {
    opacity: 1;
}

.category-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: var(--spacing-lg);
    color: var(--white);
    font-size: 28px;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

.category-icon:after {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--light-blue), transparent);
    opacity: 0;
    z-index: -1;
    transition: all 0.3s ease;
}

.category-card:hover .category-icon:after {
    opacity: 1;
    animation: spin 10s linear infinite;
}

.bg-primary {
    background-color: var(--light-blue);
}

.bg-success {
    background-color: var(--success);
}

.bg-danger {
    background-color: var(--danger);
}

.bg-warning {
    background-color: var(--warning);
}

.bg-info {
    background-color: var(--info);
}

.bg-dark {
    background-color: var(--dark-blue);
}

.bg-secondary {
    background-color: var(--dark-gray);
}

.category-icon img {
    width: 35px;
    height: 35px;
    filter: brightness(0) invert(1);
}

.category-content {
    flex: 1;
}

.category-content h3 {
    margin-bottom: 5px;
    font-size: 18px;
    color: var(--dark-blue);
    transition: all 0.3s ease;
}

.category-card:hover .category-content h3 {
    color: var(--light-blue);
}

.category-content p {
    margin-bottom: 0;
    color: var(--dark-gray);
    font-size: 14px;
}

.count-badge {
    display: inline-block;
    background-color: var(--light-gray);
    color: var(--dark-blue);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.category-arrow {
    position: absolute;
    right: 20px;
    color: var(--light-blue);
    opacity: 0;
    transform: translateX(-10px);
    transition: all 0.3s ease;
}

.category-card:hover .category-arrow {
    opacity: 1;
    transform: translateX(0);
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* ===== Category Description ===== */
.category-description {
    background: linear-gradient(135deg, rgba(102, 178, 255, 0.1) 0%, rgba(0, 0, 255, 0.05) 100%);
    padding: var(--spacing-xxl) 0;
    margin-bottom: var(--spacing-xl);
    position: relative;
    overflow: hidden;
}

.category-info {
    display: flex;
    align-items: center;
    background-color: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    position: relative;
    z-index: 2;
}

.category-icon {
    width: 100px;
    height: 100px;
    background-color: var(--light-blue);
    color: var(--white);
    border-radius: var(--border-radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: var(--spacing-xl);
    font-size: 40px;
    transition: all 0.3s ease;
}

.category-content h2 {
    color: var(--dark-blue);
    margin-bottom: var(--spacing-md);
    position: relative;
}

.category-content p {
    color: var(--dark-gray);
    font-size: 16px;
    line-height: 1.6;
}

/* ===== Coaching List ===== */
.coaching-list {
    margin: var(--spacing-xl) 0;
}

.coaching-list-item {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-lg);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.coaching-list-item:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, var(--light-blue), var(--electric-cyan));
    opacity: 0;
    transition: all 0.3s ease;
}

.coaching-list-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-hover);
}

.coaching-list-item:hover:before {
    opacity: 1;
}

.coaching-list-item .coaching-image {
    border-radius: var(--border-radius-md);
    overflow: hidden;
    border: 1px solid var(--light-gray);
    padding: 5px;
    background-color: var(--white);
    position: relative;
    height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.coaching-list-item .coaching-image img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.coaching-list-item:hover .coaching-image img {
    transform: scale(1.05);
}

.coaching-list-item .coaching-content h3 {
    color: var(--dark-blue);
    transition: all 0.3s ease;
    font-size: 22px;
    margin-bottom: 10px;
}

.coaching-list-item:hover .coaching-content h3 {
    color: var(--light-blue);
}

.coaching-list-item .coaching-meta {
    margin-bottom: 12px;
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.coaching-list-item .coaching-meta span {
    color: var(--dark-gray);
    display: flex;
    align-items: center;
    font-size: 14px;
}

.coaching-list-item .coaching-meta i {
    color: var(--light-blue);
    margin-right: 5px;
}

.coaching-list-item .coaching-categories {
    margin-bottom: 12px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.coaching-list-item .coaching-description {
    color: var(--dark-gray);
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 0;
}

.coaching-list-item .coaching-actions {
    display: flex;
    flex-direction: column;
    height: 100%;
    justify-content: space-between;
}

.coaching-list-item .coaching-contact {
    margin-bottom: 15px;
}

.coaching-list-item .coaching-contact p {
    margin-bottom: 8px;
    font-size: 14px;
    display: flex;
    align-items: center;
}

.coaching-list-item .coaching-contact i {
    color: var(--light-blue);
    margin-right: 8px;
    width: 20px;
    text-align: center;
}

.coaching-list-item .action-buttons {
    display: flex;
    flex-direction: column;
}

.coaching-list-item .action-buttons .btn {
    transition: all 0.3s ease;
}

.coaching-list-item .action-buttons .btn-primary {
    background-color: var(--light-blue);
    border-color: var(--light-blue);
}

.coaching-list-item .action-buttons .btn-primary:hover {
    background-color: var(--dark-blue);
    border-color: var(--dark-blue);
}

.coaching-list-item .action-buttons .btn-outline-primary {
    color: var(--light-blue);
    border-color: var(--light-blue);
}

.coaching-list-item .action-buttons .btn-outline-primary:hover {
    background-color: var(--light-blue);
    color: var(--white);
}

.featured-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: rgba(255, 215, 0, 0.9);
    color: var(--dark-blue);
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 5px;
}

.featured-badge i {
    color: var(--dark-blue);
}

/* Filter Options */
.filter-options {
    background-color: var(--white);
    padding: 15px;
    border-radius: var(--border-radius-md);
    box-shadow: var(--box-shadow);
    margin-bottom: 20px;
}

/* Pagination */
.pagination-container {
    margin-top: 30px;
}

.pagination .page-link {
    color: var(--light-blue);
    border-color: var(--light-gray);
    padding: 8px 16px;
}

.pagination .page-item.active .page-link {
    background-color: var(--light-blue);
    border-color: var(--light-blue);
    color: var(--white);
}

.pagination .page-link:hover {
    background-color: var(--light-gray);
    color: var(--dark-blue);
}

/* Animation */
.fade-in {
    opacity: 0;
    animation: fadeIn 0.5s ease forwards;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* City Stats Section */
.city-stats-section {
    padding: var(--spacing-xxl) 0;
    background-color: var(--light-gray);
    position: relative;
    overflow: hidden;
}

.stat-card {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    box-shadow: var(--box-shadow);
    height: 100%;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-hover);
}

.stat-card:before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(0, 255, 255, 0.1) 0%, rgba(0, 0, 0, 0) 70%);
    z-index: -1;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.stat-card:hover:before {
    opacity: 1;
}

.stat-icon {
    font-size: 36px;
    color: var(--light-blue);
    margin-bottom: 15px;
}

.stat-number {
    font-size: 30px;
    font-weight: 700;
    color: var(--dark-blue);
    margin-bottom: 5px;
}

.stat-title {
    color: var(--dark-gray);
    font-size: 16px;
    margin-bottom: 0;
}

/* ===== Coaching Card ===== */ 
.coaching-card {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--box-shadow);
    margin-bottom: var(--spacing-xl);
    transition: all 0.3s ease;
    overflow: hidden;
}

.coaching-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-hover);
}

.coaching-image {
    height: 200px;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--light-gray);
}

.coaching-image img {
    max-height: 100%;
    max-width: 100%;
    object-fit: contain;
}

.featured-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: var(--neon-green);
    color: var(--dark-text);
    padding: 5px 10px;
    border-radius: var(--border-radius-sm);
    font-size: 12px;
    font-weight: 500;
}

.rating-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: var(--dark-blue);
    color: var(--white);
    padding: 5px 10px;
    border-radius: var(--border-radius-sm);
    font-size: 12px;
    font-weight: 500;
}

.coaching-content {
    padding: var(--spacing-lg);
}

.coaching-content h3 {
    margin-bottom: 10px;
    font-size: 20px;
}

.coaching-meta {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    font-size: 14px;
}

.location {
    color: var(--dark-gray);
}

.rating {
    display: flex;
    align-items: center;
}

.star-rating {
    color: #FFD700;
    margin-right: 5px;
}

.reviews {
    color: var(--dark-gray);
    font-size: 12px;
}

.coaching-categories {
    margin-bottom: 15px;
}

.category-badge {
    display: inline-block;
    background-color: var(--light-gray);
    color: var(--dark-gray);
    padding: 3px 8px;
    border-radius: var(--border-radius-sm);
    font-size: 12px;
    margin-right: 5px;
    margin-bottom: 5px;
}

.category-badge:hover {
    background-color: var(--light-blue);
    color: var(--white);
}

/* ===== City Card ===== */
.city-card {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--box-shadow);
    margin-bottom: var(--spacing-xl);
    transition: all 0.3s ease;
    overflow: hidden;
}

.city-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-hover);
}

.city-image {
    height: 200px;
    position: relative;
    overflow: hidden;
}

.city-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.city-content {
    padding: var(--spacing-lg);
    text-align: center;
}

.city-content h3 {
    margin-bottom: 10px;
    font-size: 20px;
}

.city-content p {
    margin-bottom: 15px;
    color: var(--dark-gray);
}

/* ===== City Description Page ===== */
.city-description {
    padding: var(--spacing-xxl) 0;
    background: linear-gradient(135deg, rgba(0, 51, 102, 0.05) 0%, rgba(102, 178, 255, 0.1) 100%);
    margin-bottom: var(--spacing-xl);
    position: relative;
}

.city-image-container {
    position: relative;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.city-image-container img {
    width: 100%;
    height: 350px;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.city-image-container:hover img {
    transform: scale(1.05);
}

.city-image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.1), rgba(0, 51, 102, 0.7));
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.city-image-container:hover .city-image-overlay {
    opacity: 0.5;
}

.city-stats {
    position: absolute;
    bottom: 20px;
    left: 20px;
    right: 20px;
    display: flex;
    justify-content: space-between;
    color: var(--white);
    z-index: 2;
}

.stat-item {
    display: flex;
    align-items: center;
    background-color: rgba(0, 51, 102, 0.8);
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.stat-item i {
    margin-right: 8px;
    color: var(--electric-cyan);
}

.city-title {
    color: var(--dark-blue);
    font-size: 36px;
    margin-bottom: 15px;
    position: relative;
    padding-bottom: 15px;
}

.city-title:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, var(--light-blue), var(--electric-cyan));
    border-radius: 3px;
}

.city-rating {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.city-rating .stars {
    color: #FFD700;
    margin-right: 10px;
}

.city-rating span {
    color: var(--dark-gray);
    font-weight: 500;
}

.city-description-text {
    margin-bottom: 25px;
    color: var(--dark-gray);
    font-size: 16px;
    line-height: 1.6;
}

.city-highlights {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 20px;
}

.highlight-item {
    display: flex;
    align-items: center;
    background-color: var(--white);
    padding: 10px 15px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.highlight-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.highlight-item i {
    color: var(--light-blue);
    margin-right: 10px;
    font-size: 18px;
}

.highlight-item span {
    font-weight: 500;
    color: var(--dark-blue);
}

/* ===== How It Works Section ===== */
.step-card {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--box-shadow);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    transition: all 0.3s ease;
    text-align: center;
}

.step-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-hover);
}

.step-icon {
    width: 80px;
    height: 80px;
    background-color: var(--light-blue);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
    color: var(--white);
    font-size: 30px;
    position: relative;
}

.step-number {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 30px;
    height: 30px;
    background-color: var(--dark-blue);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 16px;
    font-weight: 700;
}

.step-content h3 {
    margin-bottom: 10px;
    font-size: 20px;
}

.step-content p {
    color: var(--dark-gray);
}

/* ===== Blog Card ===== */
.blog-card {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--box-shadow);
    margin-bottom: var(--spacing-xl);
    transition: all 0.3s ease;
    overflow: hidden;
}

.blog-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-hover);
}

.blog-image {
    height: 200px;
    position: relative;
    overflow: hidden;
}

.blog-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.blog-image .category {
    position: absolute;
    top: 10px;
    left: 10px;
    background-color: var(--light-blue);
    color: var(--white);
    padding: 5px 10px;
    border-radius: var(--border-radius-sm);
    font-size: 12px;
    font-weight: 500;
}

.blog-content {
    padding: var(--spacing-lg);
}

.blog-meta {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    font-size: 12px;
    color: var(--dark-gray);
}

.blog-content h3 {
    margin-bottom: 10px;
    font-size: 20px;
}

.blog-content p {
    margin-bottom: 15px;
    color: var(--dark-gray);
}

.read-more {
    font-weight: 500;
    color: var(--light-blue);
}

.read-more i {
    margin-left: 5px;
    transition: all 0.3s ease;
}

.read-more:hover i {
    transform: translateX(5px);
}

/* ===== CTA Section ===== */
.cta-section {
    background-color: var(--dark-blue);
    color: var(--white);
}

.cta-content {
    padding: var(--spacing-xl) 0;
}

.cta-content h2 {
    color: var(--white);
    margin-bottom: var(--spacing-md);
}

.cta-content p {
    font-size: 18px;
    margin-bottom: var(--spacing-xl);
}

.cta-buttons .btn {
    margin: 0 10px;
}

/* ===== Footer ===== */
.footer-top {
    padding: var(--spacing-xxl) 0;
}

.footer-widget {
    margin-bottom: var(--spacing-xl);
}

.footer-widget h3 {
    color: var(--white);
    margin-bottom: var(--spacing-lg);
    font-size: 20px;
    position: relative;
    padding-bottom: 10px;
}

.footer-widget h3:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 2px;
    background-color: var(--light-blue);
}

.footer-about .footer-logo {
    height: 50px;
    margin-bottom: var(--spacing-md);
}

.footer-about p {
    margin-bottom: var(--spacing-md);
    color: var(--light-text);
}

.footer-about .social-links {
    margin-top: var(--spacing-md);
}

.footer-about .social-links a {
    display: inline-block;
    width: 36px;
    height: 36px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    text-align: center;
    line-height: 36px;
    margin-right: 10px;
    color: var(--white);
    transition: all 0.3s ease;
}

.footer-about .social-links a:hover {
    background-color: var(--light-blue);
    color: var(--white);
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 10px;
}

.footer-links li a {
    color: var(--light-text);
    transition: all 0.3s ease;
    display: block;
}

.footer-links li a:hover {
    color: var(--light-blue);
    padding-left: 5px;
}

.footer-posts {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-posts li {
    margin-bottom: 15px;
}

.footer-posts li a {
    display: flex;
    align-items: center;
    color: var(--light-text);
}

.footer-posts li a:hover {
    color: var(--light-blue);
}

.footer-posts .post-image {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    margin-right: 10px;
}

.footer-posts .post-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.footer-posts .post-content h4 {
    font-size: 14px;
    margin-bottom: 5px;
    color: var(--light-text);
}

.footer-posts .post-content span {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
}

.contact-info ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.contact-info ul li {
    margin-bottom: 10px;
    color: var(--light-text);
}

.contact-info ul li i {
    margin-right: 10px;
    color: var(--light-blue);
}

.newsletter p {
    margin-bottom: var(--spacing-md);
    color: var(--light-text);
}

.newsletter-form .input-group {
    margin-top: var(--spacing-md);
}

.footer-bottom {
    padding: 20px 0;
    font-size: 14px;
}

.copyright p {
    margin-bottom: 0;
}

.footer-bottom-links a {
    color: var(--light-text);
    margin-left: 20px;
}

.footer-bottom-links a:hover {
    color: var(--light-blue);
}

/* ===== Back to Top Button ===== */
.back-to-top {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    background-color: var(--light-blue);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.back-to-top.active {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background-color: var(--dark-blue);
    color: var(--white);
}

/* ===== Breadcrumb ===== */
.breadcrumb-section {
    background-color: var(--light-gray);
    padding: var(--spacing-lg) 0;
}

.breadcrumb {
    margin-bottom: 0;
}

.breadcrumb-item a {
    color: var(--dark-blue);
}

.breadcrumb-item.active {
    color: var(--dark-gray);
}

/* ===== Pagination ===== */
.pagination {
    margin-top: var(--spacing-xl);
}

.pagination .page-link {
    color: var(--dark-blue);
    border-color: var(--light-gray);
    padding: 10px 15px;
}

.pagination .page-item.active .page-link {
    background-color: var(--light-blue);
    border-color: var(--light-blue);
}

.pagination .page-link:hover {
    background-color: var(--light-gray);
    color: var(--dark-blue);
}

/* ===== Alert ===== */
.alert {
    border-radius: var(--border-radius-md);
    padding: 15px 20px;
}

/* ===== Responsive Styles ===== */
@media (max-width: 991px) {
    h1 {
        font-size: 32px;
    }
    
    h2 {
        font-size: 26px;
    }
    
    h3 {
        font-size: 20px;
    }
    
    .section-padding {
        padding: 40px 0;
    }
    
    .navbar-nav .nav-link.btn {
        margin-left: 0;
        margin-top: 10px;
    }
    
    .hero-content {
        text-align: center;
        margin-bottom: 30px;
    }
    
    .search-form {
        margin-bottom: 30px;
    }
}

@media (max-width: 767px) {
    h1 {
        font-size: 28px;
    }
    
    h2 {
        font-size: 24px;
    }
    
    .top-bar .social-links {
        text-align: center !important;
        margin-top: 10px;
    }
    
    .contact-info {
        text-align: center;
    }
    
    .footer-widget {
        margin-bottom: 30px;
    }
    
    .footer-bottom-links {
        text-align: center !important;
        margin-top: 10px;
    }
    
    .footer-bottom-links a {
        margin: 0 10px;
    }
    
    .copyright {
        text-align: center;
    }
}

/* ===== Animations ===== */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes slideInUp {
    from {
        transform: translateY(50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.slide-in-up {
    animation: slideInUp 0.5s ease-in-out;
}

/* ===== Hover Effects ===== */
.hover-glow:hover {
    box-shadow: 0 0 15px var(--electric-cyan);
    border-color: var(--electric-cyan);
    color: var(--white);
    background-color: var(--dark-blue);
    transform: translateY(-3px);
}

.hover-scale:hover {
    transform: scale(1.05);
    box-shadow: var(--box-shadow-hover);
    z-index: 10;
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

.hover-neon:hover {
    text-shadow: 0 0 10px var(--neon-green), 0 0 20px var(--neon-green), 0 0 30px var(--neon-green);
    color: var(--white);
}

.hover-magenta:hover {
    color: var(--magenta);
    border-color: var(--magenta);
    box-shadow: 0 0 15px var(--magenta);
}

.btn-primary.hover-glow:hover {
    background-color: var(--electric-cyan);
    border-color: var(--electric-cyan);
    color: var(--dark-blue);
    font-weight: bold;
    box-shadow: 0 0 20px var(--electric-cyan);
}

.category-card:hover .category-icon {
    background-color: var(--light-blue);
    color: var(--white);
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 0 15px rgba(102, 178, 255, 0.5);
}

.coaching-card:hover .coaching-image img {
    transform: scale(1.05);
}

.city-card:hover .city-image img {
    transform: scale(1.1);
    filter: brightness(1.1);
}

.blog-card:hover .blog-image img {
    transform: scale(1.1);
    filter: brightness(1.1);
}

/* ===== Custom Scrollbar ===== */
::-webkit-scrollbar {
    width: 10px;
}

::-webkit-scrollbar-track {
    background: var(--light-gray);
}

::-webkit-scrollbar-thumb {
    background: var(--light-blue);
    border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--dark-blue);
}

/* ===== Coaching Center Detail Page ===== */
.page-header {
    background: linear-gradient(135deg, var(--dark-blue) 0%, var(--bg-darker) 100%);
    padding: var(--spacing-xxl) 0;
    color: var(--white);
    position: relative;
    overflow: hidden;
    text-align: center;
}

.page-header h1 {
    font-size: 42px;
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    animation: fadeIn 1s ease-in-out;
}

.breadcrumb {
    justify-content: center;
    background-color: transparent;
    padding: 0;
}

.breadcrumb-item a {
    color: var(--light-blue);
    transition: all 0.3s ease;
}

.breadcrumb-item a:hover {
    color: var(--white);
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: var(--light-text);
}

.breadcrumb-item + .breadcrumb-item::before {
    color: var(--light-text);
}

.page-header:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 51, 102, 0.9) 0%, rgba(0, 0, 51, 0.8) 100%);
    z-index: 1;
}

.page-header .container {
    position: relative;
    z-index: 2;
}

.page-header h1 {
    color: var(--white);
    margin-bottom: var(--spacing-sm);
    font-size: 36px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.breadcrumb {
    background-color: transparent;
    padding: 0;
    margin: 0;
}

.breadcrumb-item a {
    color: var(--light-blue);
}

.breadcrumb-item.active {
    color: var(--light-text);
}

.breadcrumb-item + .breadcrumb-item::before {
    color: var(--light-text);
}

.coaching-overview {
    padding: var(--spacing-xxl) 0;
}

.coaching-detail-card {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    position: relative;
    overflow: hidden;
}

.coaching-detail-card:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, var(--light-blue), var(--electric-cyan));
}

.coaching-header {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--light-gray);
}

.coaching-logo {
    width: 120px;
    height: 120px;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    margin-right: var(--spacing-xl);
    border: 1px solid var(--light-gray);
    padding: 5px;
    background-color: var(--white);
    box-shadow: var(--box-shadow);
}

.coaching-logo img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.coaching-title {
    flex: 1;
}

.coaching-title h2 {
    margin-bottom: var(--spacing-sm);
    color: var(--dark-blue);
    font-size: 28px;
}

.coaching-meta {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: var(--spacing-sm);
}

.coaching-meta span {
    margin-right: var(--spacing-lg);
    color: var(--dark-gray);
    font-size: 14px;
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.coaching-meta span i {
    margin-right: 5px;
    color: var(--light-blue);
}

.coaching-categories {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.category-badge {
    display: inline-block;
    padding: 5px 12px;
    background-color: var(--light-gray);
    color: var(--dark-blue);
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
}

.category-badge:hover {
    background-color: var(--light-blue);
    color: var(--white);
    transform: translateY(-2px);
}

.coaching-description {
    margin-bottom: var(--spacing-xl);
}

.coaching-description h3 {
    margin-bottom: var(--spacing-md);
    color: var(--dark-blue);
    font-size: 22px;
    position: relative;
    padding-bottom: 10px;
}

.coaching-description h3:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, var(--light-blue), var(--electric-cyan));
    border-radius: 3px;
}

.coaching-facilities {
    margin-bottom: var(--spacing-lg);
}

.coaching-gallery {
    margin-top: var(--spacing-lg);
    border-top: 1px solid var(--light-gray);
    padding-top: var(--spacing-lg);
}

.coaching-gallery h3 {
    margin-bottom: var(--spacing-md);
    color: var(--dark-blue);
    font-size: 22px;
    position: relative;
    padding-bottom: 10px;
}

.coaching-gallery h3:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, var(--light-blue), var(--electric-cyan));
    border-radius: 3px;
}

.coaching-facilities h3 {
    margin-bottom: var(--spacing-md);
    color: var(--dark-blue);
    font-size: 22px;
    position: relative;
    padding-bottom: 10px;
}

.coaching-facilities h3:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, var(--light-blue), var(--electric-cyan));
    border-radius: 3px;
}

.facilities-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}

.facility-item {
    display: flex;
    align-items: center;
    padding: 10px;
    background-color: var(--light-gray);
    border-radius: var(--border-radius-md);
    transition: all 0.3s ease;
}

.facility-item:hover {
    background-color: rgba(102, 178, 255, 0.1);
    transform: translateY(-2px);
}

.facility-item i {
    margin-right: 10px;
    color: var(--light-blue);
    font-size: 16px;
}

.courses-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.course-item {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    padding: var(--spacing-lg);
    border: 1px solid var(--light-gray);
    transition: all 0.3s ease;
}

.course-item:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-3px);
    border-color: var(--light-blue);
}

.course-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);
    flex-wrap: wrap;
}

.course-header h4 {
    margin-bottom: 5px;
    color: var(--dark-blue);
    font-size: 18px;
}

.course-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 10px;
}

.course-meta span {
    font-size: 14px;
    color: var(--dark-gray);
    display: flex;
    align-items: center;
}

.course-meta span i {
    margin-right: 5px;
    color: var(--light-blue);
}

.course-description {
    margin-bottom: var(--spacing-md);
}

.course-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--light-gray);
}

.start-date {
    font-size: 14px;
    color: var(--dark-gray);
    display: flex;
    align-items: center;
}

.start-date i {
    margin-right: 5px;
    color: var(--light-blue);
}

.success-stories-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.success-story-item {
    display: flex;
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    padding: var(--spacing-lg);
    border: 1px solid var(--light-gray);
    transition: all 0.3s ease;
}

.success-story-item:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-3px);
    border-color: var(--light-blue);
}

.student-image {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: var(--spacing-lg);
    border: 3px solid var(--light-blue);
    box-shadow: 0 0 10px rgba(102, 178, 255, 0.3);
}

.student-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.student-details {
    flex: 1;
}

.student-details h4 {
    margin-bottom: 5px;
    color: var(--dark-blue);
    font-size: 18px;
}

.achievement {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.achievement .badge {
    margin-right: 10px;
    padding: 5px 10px;
    font-size: 12px;
}

.achievement .year {
    font-size: 14px;
    color: var(--dark-gray);
}

.testimonial {
    font-style: italic;
    color: var(--dark-gray);
    position: relative;
    padding-left: 20px;
    border-left: 3px solid var(--light-blue);
}

.reviews-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.review-item {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    padding: var(--spacing-lg);
    border: 1px solid var(--light-gray);
    transition: all 0.3s ease;
}

.review-item:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-3px);
    border-color: var(--light-blue);
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    flex-wrap: wrap;
}

.reviewer-info {
    display: flex;
    align-items: center;
}

.reviewer-info img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    margin-right: 15px;
    object-fit: cover;
    border: 2px solid var(--light-blue);
}

.reviewer-info h4 {
    margin-bottom: 0;
    font-size: 16px;
    color: var(--dark-blue);
}

.review-rating {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.review-rating .stars {
    display: flex;
    flex-direction: row;
}

.review-rating .fa-star {
    color: #ddd;
    margin-right: 2px;
}

.review-rating .fa-star.filled {
    color: #FFD700;
}

.review-date {
    font-size: 12px;
    color: var(--dark-gray);
    margin-top: 5px;
}

.review-content {
    color: var(--dark-gray);
}

.write-review {
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--light-gray);
}

.write-review h4 {
    margin-bottom: var(--spacing-md);
    color: var(--dark-blue);
    font-size: 18px;
}

.rating-input {
    display: flex;
    flex-direction: row-reverse;
    justify-content: flex-end;
}

.rating-input input {
    display: none;
}

.rating-input label {
    cursor: pointer;
    font-size: 25px;
    color: #ddd;
    margin-right: 5px;
    transition: all 0.2s ease;
}

.rating-input label:hover,
.rating-input label:hover ~ label,
.rating-input input:checked ~ label {
    color: #FFD700;
}

.coaching-sidebar-card {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    position: relative;
    overflow: hidden;
}

.coaching-sidebar-card:before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, var(--light-blue), var(--electric-cyan));
}

.coaching-sidebar-card h3 {
    margin-bottom: var(--spacing-lg);
    color: var(--dark-blue);
    font-size: 20px;
    position: relative;
    padding-bottom: 10px;
}

.coaching-sidebar-card h3:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, var(--light-blue), var(--electric-cyan));
    border-radius: 3px;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.contact-item {
    display: flex;
    align-items: flex-start;
}

.contact-item i {
    width: 40px;
    height: 40px;
    background-color: rgba(102, 178, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--light-blue);
    margin-right: 15px;
    font-size: 16px;
}

.contact-item div {
    flex: 1;
}

.contact-item h4 {
    margin-bottom: 5px;
    font-size: 16px;
    color: var(--dark-blue);
}

.contact-item p {
    margin-bottom: 0;
    color: var(--dark-gray);
}

.contact-item a {
    color: var(--dark-gray);
    transition: all 0.3s ease;
}

.contact-item a:hover {
    color: var(--light-blue);
}

.contact-social {
    display: flex;
    gap: 10px;
    margin-top: var(--spacing-md);
}

.contact-social a {
    width: 40px;
    height: 40px;
    background-color: var(--light-gray);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--dark-blue);
    transition: all 0.3s ease;
}

.contact-social a:hover {
    background-color: var(--light-blue);
    color: var(--white);
    transform: translateY(-3px);
}

.coaching-map {
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.coaching-map iframe {
    display: block;
}

.similar-coaching-section {
    padding: var(--spacing-xxl) 0;
    background-color: var(--light-gray);
}

.similar-coaching-section h2 {
    margin-bottom: var(--spacing-xl);
    text-align: center;
    position: relative;
    padding-bottom: 15px;
}

.similar-coaching-section h2:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, var(--light-blue), var(--electric-cyan));
    border-radius: 3px;
}

@media (max-width: 991px) {
    .facilities-list {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .coaching-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .coaching-logo {
        margin-bottom: var(--spacing-md);
    }
    
    .success-story-item {
        flex-direction: column;
    }
    
    .student-image {
        margin-bottom: var(--spacing-md);
    }
}

/* Gallery Carousel Styles */
.small-gallery-carousel {
    margin: 20px 0;
    max-width: 250px;
    margin-left: 0;
}

.gallery-frame {
    border: 2px solid var(--light-blue);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    position: relative;
    width: 250px;
    height: 200px;
    margin: 0 auto;
    max-width: none; /* Prevent responsive scaling */
}

.gallery-frame img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.carousel-caption {
    background-color: rgba(0, 51, 102, 0.7);
    border-radius: var(--border-radius-sm);
    padding: 10px;
    bottom: 20px;
}

.carousel-control-prev,
.carousel-control-next {
    width: 10%;
    opacity: 0.8;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
    background-color: var(--dark-blue);
    border-radius: 50%;
    padding: 10px;
}

.carousel-indicators {
    margin-bottom: -30px;
}

.carousel-indicators button {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: var(--light-blue);
    opacity: 0.5;
}

.carousel-indicators button.active {
    background-color: var(--dark-blue);
    opacity: 1;
}

@media (max-width: 767px) {
    .facilities-list {
        grid-template-columns: 1fr;
    }
    
    .review-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .review-rating {
        margin-top: var(--spacing-sm);
        align-items: flex-start;
    }
    
    .review-rating .stars {
        display: flex;
        flex-direction: row;
    }
    
    .small-gallery-carousel {
        max-width: 250px;
        margin: 0 auto;
    }
    
    .gallery-frame {
        width: 250px;
        height: 200px;
        max-width: none;
    }
}

/* ===== Category Page Styles ===== */
.category-description {
    background: linear-gradient(135deg, var(--light-gray) 0%, var(--white) 100%);
}

.category-info {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xxl);
    box-shadow: var(--box-shadow);
    border: 1px solid rgba(102, 178, 255, 0.1);
}

.category-icon-large {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--light-blue), var(--electric-cyan));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: var(--white);
    box-shadow: 0 0 20px rgba(102, 178, 255, 0.3);
    animation: pulse-glow 2s infinite;
}

.category-icon-large img {
    width: 50px;
    height: 50px;
    object-fit: contain;
}

.category-content h2 {
    color: var(--dark-blue);
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: var(--spacing-md);
}

.category-description-text {
    font-size: 1.1rem;
    color: var(--dark-gray);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

.category-stats {
    display: flex;
    gap: var(--spacing-xl);
}

.stat-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--light-blue);
    font-weight: 600;
}

.stat-item i {
    font-size: 1.2rem;
}

/* Coaching List Styles */
.coaching-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.coaching-list-item {
    opacity: 1;
    transform: none;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.coaching-card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--box-shadow);
    transition: all 0.3s ease;
    border: 1px solid rgba(102, 178, 255, 0.1);
}

.coaching-card:hover {
    box-shadow: var(--box-shadow-hover);
    transform: translateY(-2px);
    border-color: var(--light-blue);
}

.coaching-image {
    position: relative;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    height: 120px;
    background: var(--light-gray);
    display: flex;
    align-items: center;
    justify-content: center;
}

.coaching-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.coaching-card:hover .coaching-image img {
    transform: scale(1.05);
}

.coaching-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: var(--spacing-md);
}

.coaching-title a {
    color: var(--dark-blue);
    text-decoration: none;
    transition: color 0.3s ease;
}

.coaching-title a:hover {
    color: var(--light-blue);
}

.coaching-meta {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.meta-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--dark-gray);
    font-size: 0.9rem;
}

.meta-item i {
    color: var(--light-blue);
    width: 16px;
}

.coaching-categories {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.coaching-description {
    color: var(--dark-gray);
    line-height: 1.6;
    margin-bottom: 0;
}

.coaching-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    height: 100%;
    justify-content: space-between;
}

.coaching-contact {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.contact-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin: 0;
    font-size: 0.9rem;
}

.contact-item i {
    color: var(--light-blue);
    width: 16px;
}

.contact-item a {
    color: var(--dark-gray);
    text-decoration: none;
    transition: color 0.3s ease;
}

.contact-item a:hover {
    color: var(--light-blue);
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.action-buttons .btn {
    font-size: 0.9rem;
    padding: 8px 16px;
    border-radius: var(--border-radius-sm);
}

/* No Results Styles */
.no-results {
    text-align: center;
    padding: var(--spacing-xxl) 0;
}

.no-results i {
    color: var(--light-gray);
}

.no-results h3 {
    color: var(--dark-blue);
    margin-bottom: var(--spacing-md);
}

.no-results p {
    color: var(--dark-gray);
    margin-bottom: var(--spacing-xl);
}

/* Related Categories */
.related-categories {
    background: var(--light-gray);
}

/* Pagination Styles */
.pagination-wrapper {
    margin-top: var(--spacing-xxl);
    text-align: center;
}

.pagination .page-link {
    color: var(--dark-blue);
    border-color: var(--light-blue);
    padding: 10px 15px;
    margin: 0 2px;
    border-radius: var(--border-radius-sm);
}

.pagination .page-link:hover {
    background-color: var(--light-blue);
    border-color: var(--light-blue);
    color: var(--white);
}

.pagination .page-item.active .page-link {
    background-color: var(--dark-blue);
    border-color: var(--dark-blue);
    color: var(--white);
}

/* Responsive Design for Category Page */
@media (max-width: 991px) {
    .category-content h2 {
        font-size: 2rem;
    }
    
    .category-icon-large {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
    
    .category-stats {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .coaching-actions {
        margin-top: var(--spacing-lg);
    }
    
    .action-buttons {
        flex-direction: row;
        justify-content: space-between;
    }
}

@media (max-width: 767px) {
    .category-info {
        padding: var(--spacing-lg);
    }
    
    .category-content h2 {
        font-size: 1.8rem;
    }
    
    .coaching-card {
        padding: var(--spacing-lg);
    }
    
    .coaching-image {
        height: 100px;
        margin-bottom: var(--spacing-md);
    }
    
    .coaching-meta {
        margin-bottom: var(--spacing-lg);
    }
    
    .action-buttons {
        flex-direction: column;
    }
}

@keyframes pulse-glow {
    0% {
        box-shadow: 0 0 20px rgba(102, 178, 255, 0.3);
    }
    50% {
        box-shadow: 0 0 30px rgba(102, 178, 255, 0.5);
    }
    100% {
        box-shadow: 0 0 20px rgba(102, 178, 255, 0.3);
    }
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Coaching Locations */
.coaching-locations {
    margin-bottom: 15px;
    padding: 10px;
    background-color: var(--light-gray);
    border-radius: 6px;
}

.coaching-locations h6 {
    margin-bottom: 8px;
    color: var(--dark-blue);
}

.location-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.location-item {
    margin-bottom: 5px;
}

.location-link {
    display: inline-block;
    padding: 4px 10px;
    background-color: var(--white);
    border: 1px solid var(--light-blue);
    border-radius: 4px;
    color: var(--dark-blue);
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.location-link:hover {
    background-color: var(--light-blue);
    color: var(--white);
}

.location-link.active {
    background-color: var(--dark-blue);
    color: var(--white);
    border-color: var(--dark-blue);
}

/* Course Locations */
.course-locations {
    margin-top: 8px;
    color: var(--dark-gray);
}

.course-locations i {
    color: var(--light-blue);
    margin-right: 5px;
}

/* Center Locations in Sidebar */
.center-locations {
    margin-top: 15px;
}

.location-item-sidebar {
    background-color: var(--light-gray);
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
    border-left: 3px solid var(--light-blue);
}

.location-item-sidebar:last-child {
    margin-bottom: 0;
}

.location-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.location-header i {
    color: var(--light-blue);
    font-size: 18px;
    margin-right: 10px;
}

.location-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--dark-blue);
}

.location-details p {
    margin-bottom: 10px;
    font-size: 14px;
    color: var(--dark-gray);
}

.location-details .btn {
    width: 100%;
    margin-top: 5px;
}
