<?php
// Include database configuration
require_once 'includes/config/config.php';
require_once 'includes/classes/Database.php';

// Connect to database
$db = Database::getInstance();

// Get all categories
$categories = $db->fetchAll("SELECT * FROM course_categories");

// Display categories
echo "<h1>Categories</h1>";
echo "<pre>";
print_r($categories);
echo "</pre>";

// Get category by slug
$slug = 'upsc-civil-services';
$category = $db->fetchRow("SELECT * FROM course_categories WHERE slug = ?", [$slug]);

// Display category
echo "<h1>Category with slug '{$slug}'</h1>";
echo "<pre>";
print_r($category);
echo "</pre>";