<?php
/**
 * Create Admin User
 * This script creates an admin user in the database
 */
require_once 'includes/autoload.php';

// Get database instance
$db = Database::getInstance();

// Check if admin user exists
$adminUser = $db->fetchRow(
    "SELECT user_id FROM users WHERE username = ? OR email = ?",
    ['admin', '<EMAIL>']
);

if ($adminUser) {
    echo "Admin user already exists with ID: " . $adminUser['user_id'];
    
    // Update admin password
    $updated = $db->update('users', [
        'password' => password_hash('admin123', PASSWORD_DEFAULT),
        'is_verified' => 1,
        'status' => 'active'
    ], 'user_id = ?', [$adminUser['user_id']]);
    
    if ($updated) {
        echo "<br>Admin password updated successfully.";
    } else {
        echo "<br>Failed to update admin password.";
    }
} else {
    // Create admin user
    $adminData = [
        'username' => 'admin',
        'email' => '<EMAIL>',
        'password' => password_hash('admin123', PASSWORD_DEFAULT),
        'first_name' => 'Admin',
        'last_name' => 'User',
        'user_type' => 'admin',
        'is_verified' => 1,
        'status' => 'active',
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    $userId = $db->insert('users', $adminData);
    
    if ($userId) {
        echo "Admin user created successfully with ID: $userId";
    } else {
        echo "Failed to create admin user.";
    }
}

// Display all users
$users = $db->fetchAll("SELECT user_id, username, email, user_type, is_verified, status FROM users");

echo "<h2>All Users</h2>";
echo "<table border='1'>";
echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Type</th><th>Verified</th><th>Status</th></tr>";

foreach ($users as $user) {
    echo "<tr>";
    echo "<td>" . $user['user_id'] . "</td>";
    echo "<td>" . $user['username'] . "</td>";
    echo "<td>" . $user['email'] . "</td>";
    echo "<td>" . $user['user_type'] . "</td>";
    echo "<td>" . ($user['is_verified'] ? 'Yes' : 'No') . "</td>";
    echo "<td>" . $user['status'] . "</td>";
    echo "</tr>";
}

echo "</table>";