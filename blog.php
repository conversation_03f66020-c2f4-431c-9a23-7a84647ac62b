<?php
/**
 * Blog Page
 * Shows all blog posts
 */
require_once 'includes/autoload.php';

// Get settings
$settings = Settings::getInstance();

// Include dummy data for development
require_once 'includes/dummy-data.php';

// Get blog posts
$blogObj = new Blog();
$posts = $blogObj->getPosts();

// If no posts found, use dummy data
if (empty($posts)) {
    $posts = getDummyBlogPosts(10);
}

// Page title and meta
$pageTitle = 'Blog';
$pageDescription = 'Read the latest articles and news about education and coaching on ' . $settings->getSiteName();
$pageKeywords = 'blog, education articles, coaching news, ' . $settings->getMetaKeywords();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?php echo getMetaTags($pageTitle . ' - ' . $settings->getSiteName(), $pageDescription, $pageKeywords); ?>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo getAssetUrl($settings->getSiteFavicon()); ?>" type="image/x-icon">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo getAssetUrl('css/style.css'); ?>">
</head>
<body>
    <!-- Header -->
    <?php include 'templates/header.php'; ?>
    
    <!-- Main Content -->
    <main>
        <!-- Page Header -->
        <section class="page-header">
            <div class="container">
                <h1><?php echo $pageTitle; ?></h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="index.php">Home</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Blog</li>
                    </ol>
                </nav>
            </div>
        </section>
        
        <!-- Blog Posts Section -->
        <section class="blog-posts-section">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8">
                        <?php foreach ($posts as $post): ?>
                            <div class="blog-post">
                                <div class="row">
                                    <div class="col-md-5">
                                        <div class="post-image">
                                            <?php if (!empty($post['featured_image'])): ?>
                                                <img src="<?php echo getUploadUrl($post['featured_image']); ?>" alt="<?php echo $post['title']; ?>">
                                            <?php else: ?>
                                                <img src="<?php echo getAssetUrl('images/dummy/blog.jpg'); ?>" alt="<?php echo $post['title']; ?>">
                                            <?php endif; ?>
                                            <a href="<?php echo getBlogCategoryUrl($post['category_slug']); ?>" class="category"><?php echo $post['category_name']; ?></a>
                                        </div>
                                    </div>
                                    <div class="col-md-7">
                                        <div class="post-content">
                                            <div class="post-meta">
                                                <span><i class="fas fa-calendar"></i> <?php echo date('F j, Y', strtotime($post['published_at'])); ?></span>
                                                <span><i class="fas fa-user"></i> <?php echo $post['author_name']; ?></span>
                                            </div>
                                            <h3><a href="<?php echo getBlogPostUrl($post['slug']); ?>"><?php echo $post['title']; ?></a></h3>
                                            <p><?php echo substr(strip_tags($post['content']), 0, 200); ?>...</p>
                                            <a href="<?php echo getBlogPostUrl($post['slug']); ?>" class="btn btn-outline-primary">Read More</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <div class="col-lg-4">
                        <div class="sidebar">
                            <!-- Search Widget -->
                            <div class="widget search-widget">
                                <h3 class="widget-title">Search</h3>
                                <form action="blog.php" method="get">
                                    <div class="input-group">
                                        <input type="text" name="search" class="form-control" placeholder="Search...">
                                        <button type="submit" class="btn btn-primary"><i class="fas fa-search"></i></button>
                                    </div>
                                </form>
                            </div>
                            
                            <!-- Categories Widget -->
                            <div class="widget categories-widget">
                                <h3 class="widget-title">Categories</h3>
                                <ul>
                                    <?php 
                                    $blogCategories = $blogObj->getCategories();
                                    if (empty($blogCategories)) {
                                        $blogCategories = [
                                            ['category_name' => 'Education News', 'category_slug' => 'education-news', 'post_count' => 5],
                                            ['category_name' => 'Exam Tips', 'category_slug' => 'exam-tips', 'post_count' => 3],
                                            ['category_name' => 'Study Guides', 'category_slug' => 'study-guides', 'post_count' => 7],
                                            ['category_name' => 'Career Advice', 'category_slug' => 'career-advice', 'post_count' => 4],
                                            ['category_name' => 'Success Stories', 'category_slug' => 'success-stories', 'post_count' => 2]
                                        ];
                                    }
                                    ?>
                                    <?php foreach ($blogCategories as $category): ?>
                                        <li>
                                            <a href="<?php echo getBlogCategoryUrl($category['category_slug']); ?>">
                                                <?php echo $category['category_name']; ?>
                                                <span>(<?php echo $category['post_count']; ?>)</span>
                                            </a>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                            
                            <!-- Recent Posts Widget -->
                            <div class="widget recent-posts-widget">
                                <h3 class="widget-title">Recent Posts</h3>
                                <ul>
                                    <?php 
                                    $recentPosts = $blogObj->getRecentPosts(5);
                                    if (empty($recentPosts)) {
                                        $recentPosts = getDummyBlogPosts(5);
                                    }
                                    ?>
                                    <?php foreach ($recentPosts as $recentPost): ?>
                                        <li>
                                            <div class="post-image">
                                                <?php if (!empty($recentPost['featured_image'])): ?>
                                                    <img src="<?php echo getUploadUrl($recentPost['featured_image']); ?>" alt="<?php echo $recentPost['title']; ?>">
                                                <?php else: ?>
                                                    <img src="<?php echo getAssetUrl('images/dummy/blog-small.jpg'); ?>" alt="<?php echo $recentPost['title']; ?>">
                                                <?php endif; ?>
                                            </div>
                                            <div class="post-info">
                                                <h4><a href="<?php echo getBlogPostUrl($recentPost['slug']); ?>"><?php echo $recentPost['title']; ?></a></h4>
                                                <span><i class="fas fa-calendar"></i> <?php echo date('F j, Y', strtotime($recentPost['published_at'])); ?></span>
                                            </div>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
    
    <!-- Footer -->
    <?php include 'templates/footer.php'; ?>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="<?php echo getAssetUrl('js/main.js'); ?>"></script>
</body>
</html>