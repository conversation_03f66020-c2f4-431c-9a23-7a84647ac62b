<?php
/**
 * Contact Page
 */
require_once 'includes/autoload.php';

// Get settings
$settings = Settings::getInstance();

// Process contact form
$formSubmitted = false;
if (isPostRequest()) {
    if (checkCSRFToken()) {
        // Get form data
        $name = $_POST['name'];
        $email = $_POST['email'];
        $phone = $_POST['phone'];
        $subject = $_POST['subject'];
        $message = $_POST['message'];
        
        // Validate form data
        $errors = [];
        
        if (empty($name)) {
            $errors['name'] = 'Name is required';
        }
        
        if (empty($email)) {
            $errors['email'] = 'Email is required';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = 'Please enter a valid email address';
        }
        
        if (empty($subject)) {
            $errors['subject'] = 'Subject is required';
        }
        
        if (empty($message)) {
            $errors['message'] = 'Message is required';
        }
        
        if (empty($errors)) {
            // Save message to database
            $db = Database::getInstance();
            $messageData = [
                'name' => $name,
                'email' => $email,
                'phone' => $phone,
                'subject' => $subject,
                'message' => $message,
                'is_read' => 0,
                'status' => 'pending'
            ];
            
            $messageId = $db->insert('contact_messages', $messageData);
            
            if ($messageId) {
                $formSubmitted = true;
                setFlashMessage('Your message has been sent successfully. We will get back to you soon!', 'success');
                
                // Send email notification to admin
                $adminEmail = $settings->getContactEmail();
                $emailSubject = 'New Contact Message: ' . $subject;
                
                $emailMessage = "
                    <html>
                    <head>
                        <title>New Contact Message</title>
                    </head>
                    <body>
                        <h2>New Contact Message</h2>
                        <p><strong>Name:</strong> {$name}</p>
                        <p><strong>Email:</strong> {$email}</p>
                        <p><strong>Phone:</strong> {$phone}</p>
                        <p><strong>Subject:</strong> {$subject}</p>
                        <p><strong>Message:</strong></p>
                        <p>{$message}</p>
                    </body>
                    </html>
                ";
                
                $headers = "MIME-Version: 1.0" . "\r\n";
                $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
                $headers .= "From: " . $settings->getSiteName() . " <" . $email . ">" . "\r\n";
                
                mail($adminEmail, $emailSubject, $emailMessage, $headers);
                
                // Clear form data
                $_POST = [];
            } else {
                setFlashMessage('Failed to send message. Please try again.', 'danger');
            }
        } else {
            setValidationErrors($errors);
        }
    } else {
        setFlashMessage('Invalid form submission. Please try again.', 'danger');
    }
}

// Page title and meta
$pageTitle = 'Contact Us';
$pageDescription = 'Get in touch with us for any questions, feedback, or support. We are here to help you find the best coaching centers for your educational needs.';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?php echo getMetaTags($pageTitle . ' - ' . $settings->getSiteName(), $pageDescription); ?>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo getAssetUrl($settings->getSiteFavicon()); ?>" type="image/x-icon">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo getAssetUrl('css/style.css'); ?>">
</head>
<body>
    <!-- Header -->
    <?php include 'templates/header.php'; ?>
    
    <!-- Breadcrumb -->
    <section class="breadcrumb-section">
        <div class="container">
            <?php
            $breadcrumbItems = [
                ['title' => 'Home', 'url' => getBaseUrl()],
                ['title' => 'Contact Us', 'url' => '']
            ];
            echo getBreadcrumb($breadcrumbItems);
            ?>
        </div>
    </section>
    
    <!-- Contact Section -->
    <section class="contact-section section-padding">
        <div class="container">
            <div class="section-header text-center">
                <h2>Contact Us</h2>
                <p>Get in touch with us for any questions or feedback</p>
            </div>
            
            <div class="row">
                <!-- Contact Information -->
                <div class="col-lg-4">
                    <div class="contact-info">
                        <div class="contact-info-item">
                            <div class="icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="content">
                                <h4>Our Location</h4>
                                <p><?php echo $settings->getContactAddress(); ?></p>
                            </div>
                        </div>
                        
                        <div class="contact-info-item">
                            <div class="icon">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div class="content">
                                <h4>Phone Number</h4>
                                <p><a href="tel:<?php echo $settings->getContactPhone(); ?>"><?php echo $settings->getContactPhone(); ?></a></p>
                            </div>
                        </div>
                        
                        <div class="contact-info-item">
                            <div class="icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="content">
                                <h4>Email Address</h4>
                                <p><a href="mailto:<?php echo $settings->getContactEmail(); ?>"><?php echo $settings->getContactEmail(); ?></a></p>
                            </div>
                        </div>
                        
                        <div class="contact-info-item">
                            <div class="icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="content">
                                <h4>Working Hours</h4>
                                <p>Monday - Friday: 9:00 AM - 6:00 PM</p>
                                <p>Saturday: 9:00 AM - 1:00 PM</p>
                                <p>Sunday: Closed</p>
                            </div>
                        </div>
                        
                        <div class="contact-social">
                            <h4>Follow Us</h4>
                            <div class="social-links">
                                <?php $socialLinks = $settings->getSocialLinks(); ?>
                                <?php if (!empty($socialLinks['facebook'])): ?>
                                    <a href="<?php echo $socialLinks['facebook']; ?>" target="_blank"><i class="fab fa-facebook-f"></i></a>
                                <?php endif; ?>
                                <?php if (!empty($socialLinks['twitter'])): ?>
                                    <a href="<?php echo $socialLinks['twitter']; ?>" target="_blank"><i class="fab fa-twitter"></i></a>
                                <?php endif; ?>
                                <?php if (!empty($socialLinks['instagram'])): ?>
                                    <a href="<?php echo $socialLinks['instagram']; ?>" target="_blank"><i class="fab fa-instagram"></i></a>
                                <?php endif; ?>
                                <?php if (!empty($socialLinks['linkedin'])): ?>
                                    <a href="<?php echo $socialLinks['linkedin']; ?>" target="_blank"><i class="fab fa-linkedin-in"></i></a>
                                <?php endif; ?>
                                <?php if (!empty($socialLinks['youtube'])): ?>
                                    <a href="<?php echo $socialLinks['youtube']; ?>" target="_blank"><i class="fab fa-youtube"></i></a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Contact Form -->
                <div class="col-lg-8">
                    <div class="contact-form-container">
                        <h3>Send Us a Message</h3>
                        
                        <?php if ($formSubmitted): ?>
                            <div class="form-success">
                                <div class="text-center">
                                    <i class="fas fa-check-circle fa-3x mb-3 text-success"></i>
                                    <h4>Message Sent Successfully!</h4>
                                    <p>Thank you for contacting us. We will get back to you soon.</p>
                                    <a href="<?php echo getBaseUrl(); ?>" class="btn btn-primary mt-3">Back to Home</a>
                                </div>
                            </div>
                        <?php else: ?>
                            <form action="contact.php" method="POST" class="contact-form">
                                <?php echo getCSRFTokenField(); ?>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="name">Your Name</label>
                                            <input type="text" id="name" name="name" class="form-control <?php echo hasValidationError('name') ? 'is-invalid' : ''; ?>" value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : (isLoggedIn() ? getCurrentUser('first_name') . ' ' . getCurrentUser('last_name') : ''); ?>" required>
                                            <?php if (hasValidationError('name')): ?>
                                                <div class="invalid-feedback"><?php echo getValidationError('name'); ?></div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="email">Email Address</label>
                                            <input type="email" id="email" name="email" class="form-control <?php echo hasValidationError('email') ? 'is-invalid' : ''; ?>" value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : (isLoggedIn() ? getCurrentUser('email') : ''); ?>" required>
                                            <?php if (hasValidationError('email')): ?>
                                                <div class="invalid-feedback"><?php echo getValidationError('email'); ?></div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="phone">Phone Number (Optional)</label>
                                            <input type="tel" id="phone" name="phone" class="form-control" value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : (isLoggedIn() ? getCurrentUser('phone') : ''); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="subject">Subject</label>
                                            <input type="text" id="subject" name="subject" class="form-control <?php echo hasValidationError('subject') ? 'is-invalid' : ''; ?>" value="<?php echo isset($_POST['subject']) ? htmlspecialchars($_POST['subject']) : ''; ?>" required>
                                            <?php if (hasValidationError('subject')): ?>
                                                <div class="invalid-feedback"><?php echo getValidationError('subject'); ?></div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-group mb-3">
                                    <label for="message">Message</label>
                                    <textarea id="message" name="message" class="form-control <?php echo hasValidationError('message') ? 'is-invalid' : ''; ?>" rows="6" required><?php echo isset($_POST['message']) ? htmlspecialchars($_POST['message']) : ''; ?></textarea>
                                    <?php if (hasValidationError('message')): ?>
                                        <div class="invalid-feedback"><?php echo getValidationError('message'); ?></div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="form-group">
                                    <button type="submit" class="btn btn-primary">Send Message</button>
                                </div>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Map Section -->
    <section class="map-section">
        <div class="container-fluid p-0">
            <div class="map-container">
                <iframe 
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3504.2536055556375!2d77.20659841508096!3d28.56325198244263!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x390ce26f903969d7%3A0x8f66310952fafd25!2sIndia%20Gate%2C%20New%20Delhi%2C%20Delhi!5e0!3m2!1sen!2sin!4v1625123456789!5m2!1sen!2sin" 
                    width="100%" 
                    height="450" 
                    style="border:0;" 
                    allowfullscreen="" 
                    loading="lazy">
                </iframe>
            </div>
        </div>
    </section>
    
    <!-- Footer -->
    <?php include 'templates/footer.php'; ?>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="<?php echo getAssetUrl('js/main.js'); ?>"></script>
</body>
</html>