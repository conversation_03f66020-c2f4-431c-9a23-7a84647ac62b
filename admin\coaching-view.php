<?php
/**
 * Admin View Coaching Center
 */
require_once '../includes/autoload.php';

$user = new User();
if (!$user->isLoggedIn() || !$user->isAdmin()) {
    header('Location: login.php');
    exit;
}

$coachingId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$coachingObj = new CoachingCenter();
$coaching = $coachingObj->getById($coachingId);

$pageTitle = 'View Coaching Center';
$settings = Settings::getInstance();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Admin Panel | <?php echo $settings->getSiteName(); ?></title>
    <link rel="shortcut icon" href="<?php echo getAssetUrl($settings->getSiteFavicon()); ?>" type="image/x-icon">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/admin.css">
</head>
<body>
    <div class="admin-layout">
        <?php include 'templates/sidebar.php'; ?>
        <div class="admin-main" id="adminMain">
            <?php include 'templates/header.php'; ?>
            <div class="admin-content">
                <div class="page-title">
                    <h1><?php echo $pageTitle; ?></h1>
                </div>
                <?php if ($coaching): ?>
                    <div class="alert alert-info">Details for <strong><?php echo htmlspecialchars($coaching['coaching_name']); ?></strong> (ID: <?php echo $coachingId; ?>) go here.</div>
                <?php else: ?>
                    <div class="alert alert-danger">Coaching center not found.</div>
                <?php endif; ?>
            </div>
            <?php include 'templates/footer.php'; ?>
        </div>
    </div>
</body>
</html>
