<?php
require_once '../includes/autoload.php';
Auth::requireCoachingOwner();

$coachingId = $_SESSION['coaching_id'];
$inquiryObj = new Inquiry();

// Handle mark as read/responded/closed/delete
$message = '';
if (isset($_GET['action'], $_GET['inquiry_id'])) {
    $inquiryId = (int)$_GET['inquiry_id'];
    if ($_GET['action'] === 'delete') {
        $deleted = Database::getInstance()->delete('inquiries', 'inquiry_id = ? AND coaching_id = ?', [$inquiryId, $coachingId]);
        $message = $deleted ? '<div class="alert alert-success">Inquiry deleted.</div>' : '<div class="alert alert-danger">Failed to delete inquiry.</div>';
    } elseif ($_GET['action'] === 'mark_read') {
        $inquiryObj->update($inquiryId, ['is_read' => 1]);
    } elseif ($_GET['action'] === 'mark_responded') {
        $inquiryObj->update($inquiryId, ['status' => 'responded']);
    } elseif ($_GET['action'] === 'mark_closed') {
        $inquiryObj->update($inquiryId, ['status' => 'closed']);
    }
}

// Fetch all inquiries for this coaching center
$inquiries = Database::getInstance()->fetchAll('SELECT * FROM inquiries WHERE coaching_id = ? ORDER BY created_at DESC', [$coachingId]);
$pageTitle = 'Inquiries';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Coaching Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="<?php echo getAssetUrl('css/admin.css'); ?>">
</head>
<body>
    <div class="dashboard-container">
        <?php include 'templates/sidebar.php'; ?>
        <div class="main-content">
            <?php include 'templates/header.php'; ?>
            <div class="container-fluid">
                <div class="row mb-4">
                    <div class="col-12">
                        <h1 class="page-title">Inquiries</h1>
                        <p class="text-muted">View and manage all inquiries for your coaching center.</p>
                    </div>
                </div>
                <?php echo $message; ?>
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">All Inquiries</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-striped mb-0">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Phone</th>
                                        <th>Subject</th>
                                        <th>Date</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($inquiries)): ?>
                                        <?php foreach ($inquiries as $inquiry): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($inquiry['name']); ?></td>
                                                <td><?php echo htmlspecialchars($inquiry['email']); ?></td>
                                                <td><?php echo htmlspecialchars($inquiry['phone']); ?></td>
                                                <td><?php echo htmlspecialchars($inquiry['subject']); ?></td>
                                                <td><?php echo date('M d, Y H:i', strtotime($inquiry['created_at'])); ?></td>
                                                <td>
                                                    <span class="badge bg-<?php
                                                        if ($inquiry['status'] === 'pending') echo 'warning';
                                                        elseif ($inquiry['status'] === 'responded') echo 'success';
                                                        elseif ($inquiry['status'] === 'closed') echo 'secondary';
                                                        else echo 'info';
                                                    ?>"><?php echo ucfirst($inquiry['status']); ?></span>
                                                    <?php if (!$inquiry['is_read']): ?>
                                                        <span class="badge bg-primary">Unread</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <a href="#" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#inquiryModal<?php echo $inquiry['inquiry_id']; ?>">View</a>
                                                    <a href="inquiries.php?action=mark_read&inquiry_id=<?php echo $inquiry['inquiry_id']; ?>" class="btn btn-sm btn-secondary">Mark Read</a>
                                                    <a href="inquiries.php?action=mark_responded&inquiry_id=<?php echo $inquiry['inquiry_id']; ?>" class="btn btn-sm btn-success">Mark Responded</a>
                                                    <a href="inquiries.php?action=mark_closed&inquiry_id=<?php echo $inquiry['inquiry_id']; ?>" class="btn btn-sm btn-warning">Mark Closed</a>
                                                    <a href="inquiries.php?action=delete&inquiry_id=<?php echo $inquiry['inquiry_id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Delete this inquiry?');">Delete</a>
                                                </td>
                                            </tr>
                                            <!-- Modal for viewing inquiry details -->
                                            <div class="modal fade" id="inquiryModal<?php echo $inquiry['inquiry_id']; ?>" tabindex="-1" aria-labelledby="inquiryModalLabel<?php echo $inquiry['inquiry_id']; ?>" aria-hidden="true">
                                                <div class="modal-dialog">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title" id="inquiryModalLabel<?php echo $inquiry['inquiry_id']; ?>">Inquiry Details</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <p><strong>Name:</strong> <?php echo htmlspecialchars($inquiry['name']); ?></p>
                                                            <p><strong>Email:</strong> <?php echo htmlspecialchars($inquiry['email']); ?></p>
                                                            <p><strong>Phone:</strong> <?php echo htmlspecialchars($inquiry['phone']); ?></p>
                                                            <p><strong>Subject:</strong> <?php echo htmlspecialchars($inquiry['subject']); ?></p>
                                                            <p><strong>Message:</strong><br><?php echo nl2br(htmlspecialchars($inquiry['message'])); ?></p>
                                                            <p><strong>Date:</strong> <?php echo date('M d, Y H:i', strtotime($inquiry['created_at'])); ?></p>
                                                            <p><strong>Status:</strong> <?php echo ucfirst($inquiry['status']); ?></p>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr><td colspan="7" class="text-center">No inquiries found.</td></tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
