<?php
/**
 * AJAX Get Locations by City ID
 */
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');

try {
    require_once '../../includes/autoload.php';
    
    // Check user authentication
    $user = new User();
    if (!$user->isLoggedIn() || !$user->isAdmin()) {
        throw new Exception('Unauthorized access');
    }
    
    // Get city ID from POST
    $cityId = isset($_POST['city_id']) ? (int)$_POST['city_id'] : 0;
    
    if ($cityId <= 0) {
        throw new Exception('Invalid city ID');
    }
    
    // Get locations for the city
    $locationObj = new Location();
    $locations = $locationObj->getLocationsByCity($cityId, true);
    
    // Return locations as JSON
    echo json_encode($locations);
    
} catch (Exception $e) {
    echo json_encode([
        'error' => true,
        'message' => $e->getMessage()
    ]);
}