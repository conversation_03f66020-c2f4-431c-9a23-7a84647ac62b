<?php
/**
 * Settings Class
 * Handles website settings
 */
class Settings {
    private $db;
    private $settings = [];
    private static $instance;
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->db = Database::getInstance();
        $this->loadSettings();
    }
    
    /**
     * Get singleton instance
     * @return Settings
     */
    public static function getInstance() {
        if (!self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Load settings from database
     */
    private function loadSettings() {
        $rows = $this->db->fetchAll("SELECT * FROM settings");
        
        foreach ($rows as $row) {
            $this->settings[$row['setting_key']] = $row['setting_value'];
        }
    }
    
    /**
     * Get a setting
     * @param string $key Setting key
     * @param mixed $default Default value if setting not found
     * @return mixed Setting value
     */
    public function get($key, $default = null) {
        return isset($this->settings[$key]) ? $this->settings[$key] : $default;
    }
    
    /**
     * Set a setting
     * @param string $key Setting key
     * @param mixed $value Setting value
     * @param string $group Setting group (optional)
     * @param bool $isPublic Is setting public (optional)
     * @return bool True if setting was set
     */
    public function set($key, $value, $group = 'general', $isPublic = true) {
        // Check if setting exists
        $existing = $this->db->fetchRow(
            "SELECT setting_id FROM settings WHERE setting_key = ?",
            [$key]
        );
        
        if ($existing) {
            // Update existing setting
            $updated = $this->db->update(
                'settings',
                [
                    'setting_value' => $value,
                    'setting_group' => $group,
                    'is_public' => $isPublic ? 1 : 0
                ],
                'setting_id = ?',
                [$existing['setting_id']]
            );
            
            if ($updated) {
                $this->settings[$key] = $value;
                return true;
            }
            
            return false;
        } else {
            // Insert new setting
            $inserted = $this->db->insert('settings', [
                'setting_key' => $key,
                'setting_value' => $value,
                'setting_group' => $group,
                'is_public' => $isPublic ? 1 : 0
            ]);
            
            if ($inserted) {
                $this->settings[$key] = $value;
                return true;
            }
            
            return false;
        }
    }
    
    /**
     * Delete a setting
     * @param string $key Setting key
     * @return bool True if setting was deleted
     */
    public function delete($key) {
        $deleted = $this->db->delete('settings', 'setting_key = ?', [$key]);
        
        if ($deleted) {
            unset($this->settings[$key]);
            return true;
        }
        
        return false;
    }
    
    /**
     * Get all settings
     * @param string $group Setting group (optional)
     * @return array Settings
     */
    public function getAll($group = null) {
        if ($group === null) {
            return $this->settings;
        }
        
        $groupSettings = [];
        
        $rows = $this->db->fetchAll(
            "SELECT * FROM settings WHERE setting_group = ?",
            [$group]
        );
        
        foreach ($rows as $row) {
            $groupSettings[$row['setting_key']] = $row['setting_value'];
        }
        
        return $groupSettings;
    }
    
    /**
     * Get all setting groups
     * @return array Setting groups
     */
    public function getGroups() {
        $rows = $this->db->fetchAll(
            "SELECT DISTINCT setting_group FROM settings ORDER BY setting_group ASC"
        );
        
        $groups = [];
        
        foreach ($rows as $row) {
            $groups[] = $row['setting_group'];
        }
        
        return $groups;
    }
    
    /**
     * Save multiple settings
     * @param array $settings Settings to save
     * @param string $group Setting group
     * @return bool True if all settings were saved
     */
    public function saveMultiple($settings, $group = 'general') {
        $success = true;
        
        foreach ($settings as $key => $value) {
            if (!$this->set($key, $value, $group)) {
                $success = false;
            }
        }
        
        return $success;
    }
    
    /**
     * Get site name
     * @return string Site name
     */
    public function getSiteName() {
        return $this->get('site_name', 'Coaching Directory');
    }
    
    /**
     * Get site description
     * @return string Site description
     */
    public function getSiteDescription() {
        return $this->get('site_description', 'India\'s largest directory of coaching centers');
    }
    
    /**
     * Get site logo
     * @return string Site logo URL
     */
    public function getSiteLogo() {
        return $this->get('site_logo', 'assets/images/logo.png');
    }
    
    /**
     * Get site favicon
     * @return string Site favicon URL
     */
    public function getSiteFavicon() {
        return $this->get('site_favicon', 'assets/images/favicon.ico');
    }
    
    /**
     * Get contact email
     * @return string Contact email
     */
    public function getContactEmail() {
        return $this->get('contact_email', '<EMAIL>');
    }
    
    /**
     * Get contact phone
     * @return string Contact phone
     */
    public function getContactPhone() {
        return $this->get('contact_phone', '+91 9876543210');
    }
    
    /**
     * Get contact address
     * @return string Contact address
     */
    public function getContactAddress() {
        return $this->get('contact_address', 'New Delhi, India');
    }
    
    /**
     * Get social media links
     * @return array Social media links
     */
    public function getSocialLinks() {
        return [
            'facebook' => $this->get('social_facebook', ''),
            'twitter' => $this->get('social_twitter', ''),
            'instagram' => $this->get('social_instagram', ''),
            'linkedin' => $this->get('social_linkedin', ''),
            'youtube' => $this->get('social_youtube', '')
        ];
    }
    
    /**
     * Get footer text
     * @return string Footer text
     */
    public function getFooterText() {
        return $this->get('footer_text', '© ' . date('Y') . ' Coaching Directory. All rights reserved.');
    }
    
    /**
     * Get Google Analytics code
     * @return string Google Analytics code
     */
    public function getGoogleAnalytics() {
        return $this->get('google_analytics', '');
    }
    
    /**
     * Get meta title
     * @return string Meta title
     */
    public function getMetaTitle() {
        return $this->get('meta_title', 'Coaching Directory - Find the Best Coaching Centers in India');
    }
    
    /**
     * Get meta description
     * @return string Meta description
     */
    public function getMetaDescription() {
        return $this->get('meta_description', 'India\'s largest directory of coaching centers for IIT JEE, NEET, UPSC and more. Find the best coaching centers in your city.');
    }
    
    /**
     * Get meta keywords
     * @return string Meta keywords
     */
    public function getMetaKeywords() {
        return $this->get('meta_keywords', 'coaching centers, IIT JEE coaching, NEET coaching, UPSC coaching, best coaching in India');
    }
}