<?php
require_once '../includes/autoload.php';

$user = new User();
if (!$user->isLoggedIn() || !$user->isAdmin()) {
    header('Location: login.php');
    exit;
}

$coachingId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$coachingObj = new CoachingCenter();
$categoryObj = new Category();
$locationObj = new Location();
$facilityObj = new Facility();

$coaching = $coachingObj->getById($coachingId);
$message = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $coaching) {
    $data = [
        'coaching_name' => trim($_POST['coaching_name']),
        'email' => trim($_POST['email']),
        'phone' => trim($_POST['phone']),
        'address' => trim($_POST['address']),
        'city_id' => (int)$_POST['city_id'],
        'state_id' => (int)$_POST['state_id'],
        'status' => $_POST['status'],
        'description' => trim($_POST['description'])
    ];
    $data['categories'] = isset($_POST['categories']) ? (array)$_POST['categories'] : [];
    $data['facilities'] = isset($_POST['facilities']) ? (array)$_POST['facilities'] : [];

    if ($coachingObj->update($coachingId, $data)) {
        $message = '<div class="alert alert-success">Coaching center updated successfully.</div>';
        $coaching = $coachingObj->getById($coachingId); // Refresh data
    } else {
        $message = '<div class="alert alert-danger">Failed to update coaching center.</div>';
    }
}

$pageTitle = 'Edit Coaching Center';
$settings = Settings::getInstance();
$allCategories = $categoryObj->getAll();
$allStates = $locationObj->getAllStates();
$allCities = $locationObj->getAllCities();
$allFacilities = $facilityObj->getAllForDropdown();

function isSelected($needle, $haystack) {
    return in_array($needle, $haystack) ? 'selected' : '';
}
function isChecked($needle, $haystack) {
    return in_array($needle, $haystack) ? 'checked' : '';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Admin Panel | <?php echo $settings->getSiteName(); ?></title>
    <link rel="shortcut icon" href="<?php echo getAssetUrl($settings->getSiteFavicon()); ?>" type="image/x-icon">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/admin.css">
    <style>
        .form-section-title {
            font-size: 1.15rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
            margin-top: 1.5rem;
            letter-spacing: 0.5px;
        }
        .form-card {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(44,62,80,0.07);
            padding: 2rem 2.5rem;
            margin-bottom: 2rem;
        }
        .form-label {
            font-weight: 500;
        }
        .form-check-label {
            font-weight: 400;
        }
        .multi-col-checkboxes {
            column-count: 2;
            column-gap: 2rem;
        }
        @media (max-width: 767px) {
            .form-card { padding: 1rem; }
            .multi-col-checkboxes { column-count: 1; }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <?php include 'templates/sidebar.php'; ?>
        <div class="admin-main" id="adminMain">
            <?php include 'templates/header.php'; ?>
            <div class="admin-content">
                <div class="page-title mb-4">
                    <h1><i class="fas fa-edit me-2"></i><?php echo $pageTitle; ?></h1>
                </div>
                <?php echo $message; ?>
                <?php if ($coaching): ?>
                <form method="post" class="form-card" autocomplete="off">
                    <div class="row g-4">
                        <div class="col-md-6">
                            <label class="form-label">Name <span class="text-danger">*</span></label>
                            <input type="text" name="coaching_name" class="form-control" value="<?php echo htmlspecialchars($coaching['coaching_name']); ?>" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Email</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                <input type="email" name="email" class="form-control" value="<?php echo htmlspecialchars($coaching['email']); ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Phone</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                <input type="text" name="phone" class="form-control" value="<?php echo htmlspecialchars($coaching['phone']); ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Status</label>
                            <select name="status" class="form-select">
                                <option value="active" <?php if ($coaching['status'] === 'active') echo 'selected'; ?>>Active</option>
                                <option value="pending" <?php if ($coaching['status'] === 'pending') echo 'selected'; ?>>Pending</option>
                                <option value="inactive" <?php if ($coaching['status'] === 'inactive') echo 'selected'; ?>>Inactive</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">State</label>
                            <select name="state_id" class="form-select">
                                <?php foreach ($allStates as $state): ?>
                                    <option value="<?php echo $state['state_id']; ?>" <?php if ($coaching['state_id'] == $state['state_id']) echo 'selected'; ?>><?php echo htmlspecialchars($state['state_name']); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">City</label>
                            <select name="city_id" class="form-select">
                                <?php foreach ($allCities as $city): ?>
                                    <option value="<?php echo $city['city_id']; ?>" <?php if ($coaching['city_id'] == $city['city_id']) echo 'selected'; ?>><?php echo htmlspecialchars($city['city_name']); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-12">
                            <label class="form-label">Address</label>
                            <input type="text" name="address" class="form-control" value="<?php echo htmlspecialchars($coaching['address']); ?>">
                        </div>
                        <div class="col-md-12">
                            <label class="form-label">Description</label>
                            <textarea name="description" class="form-control" rows="4"><?php echo htmlspecialchars($coaching['description']); ?></textarea>
                        </div>
                        <div class="col-md-12">
                            <div class="form-section-title"><i class="fas fa-layer-group me-2"></i>Categories</div>
                            <select name="categories[]" class="form-select" multiple>
                                <?php $selectedCategories = array_column($coaching['categories'], 'category_id'); ?>
                                <?php foreach ($allCategories as $cat): ?>
                                    <option value="<?php echo $cat['category_id']; ?>" <?php echo isSelected($cat['category_id'], $selectedCategories); ?>><?php echo htmlspecialchars($cat['category_name']); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-12">
                            <div class="form-section-title"><i class="fas fa-cogs me-2"></i>Facilities</div>
                            <div class="multi-col-checkboxes">
                            <?php $selectedFacilities = array_column($coaching['facilities'], 'facility_id'); ?>
                            <?php foreach ($allFacilities as $facility): ?>
                                <label class="form-check-label"><input type="checkbox" class="form-check-input me-1" name="facilities[]" value="<?php echo $facility['facility_id']; ?>" <?php echo isChecked($facility['facility_id'], $selectedFacilities); ?>> <?php echo htmlspecialchars($facility['facility_name']); ?></label>
                            <?php endforeach; ?>
                            </div>
                        </div>
                        <div class="col-md-12 mt-4">
                            <button type="submit" class="btn btn-primary px-4">Save Changes</button>
                            <a href="coaching-centers.php" class="btn btn-secondary ms-2">Back</a>
                        </div>
                    </div>
                </form>
                <?php else: ?>
                    <div class="alert alert-danger">Coaching center not found.</div>
                <?php endif; ?>
            </div>
            <?php include 'templates/footer.php'; ?>
        </div>
    </div>
</body>
</html>
