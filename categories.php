<?php
/**
 * Categories Page
 * Shows all categories
 */
require_once 'includes/autoload.php';

// Get settings
$settings = Settings::getInstance();

// Get all categories from database
$categoryObj = new Category();
$categories = $categoryObj->getCategoriesWithCount();

// Page title and meta
$pageTitle = 'All Categories';
$pageDescription = 'Browse all coaching categories available on ' . $settings->getSiteName();
$pageKeywords = 'coaching categories, education categories, ' . $settings->getMetaKeywords();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?php echo getMetaTags($pageTitle . ' - ' . $settings->getSiteName(), $pageDescription, $pageKeywords); ?>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo getAssetUrl($settings->getSiteFavicon()); ?>" type="image/x-icon">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo getAssetUrl('css/style.css'); ?>">
</head>
<body>
    <!-- Header -->
    <?php include 'templates/header.php'; ?>
    
    <!-- Main Content -->
    <main>
        <!-- Page Header -->
        <section class="page-header">
            <div class="container">
                <h1><?php echo $pageTitle; ?></h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="index.php">Home</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Categories</li>
                    </ol>
                </nav>
            </div>
        </section>
        
        <!-- Categories Section -->
        <section class="categories-section">
            <div class="container">
                <div class="row">
                    <?php foreach ($categories as $category): ?>
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                            <a href="<?php echo getCategoryUrl($category['slug']); ?>" class="category-card hover-scale">
                                <div class="category-icon">
                                    <i class="<?php echo !empty($category['icon']) ? $category['icon'] : 'fas fa-book'; ?>"></i>
                                </div>
                                <h3><?php echo $category['category_name']; ?></h3>
                                <p><?php echo isset($category['coaching_count']) ? $category['coaching_count'] : 0; ?> Coaching Centers</p>
                            </a>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
    </main>
    
    <!-- Footer -->
    <?php include 'templates/footer.php'; ?>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="<?php echo getAssetUrl('js/main.js'); ?>"></script>
</body>
</html>