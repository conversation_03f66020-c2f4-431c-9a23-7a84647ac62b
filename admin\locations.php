<?php
/**
 * Admin Locations
 */
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    require_once '../includes/autoload.php';
    
    // Check user authentication
    $user = new User();
    if (!$user->isLoggedIn() || !$user->isAdmin()) {
        header('Location: login.php');
        exit;
    }
    
    // Get admin info
    $adminInfo = $user->getUserData();
    
    // Initialize settings
    $settings = Settings::getInstance();
    
    // Initialize location object
    $locationObj = new Location();
    
    // Initialize city and state objects for dropdowns
    $stateObj = new State();
    $statesData = $stateObj->getAll(['status' => 'active']);
    $states = $statesData['states'] ?? [];
    
    $cityObj = new City();
    $cityFilter = isset($_GET['city_id']) ? (int)$_GET['city_id'] : 0;
    
    if ($cityFilter > 0) {
        $citiesData = $cityObj->getAll(['city_id' => $cityFilter]);
        $cities = $citiesData['cities'] ?? [];
    } else {
        $cities = [];
    }
    
    // Handle actions
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    $locationId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    $message = '';
    
    if ($action === 'delete' && $locationId > 0) {
        if ($locationObj->delete($locationId)) {
            $message = '<div class="alert alert-success">Location deleted successfully.</div>';
        } else {
            $message = '<div class="alert alert-danger">Failed to delete location. It may be in use.</div>';
        }
    }
    
    // Handle form submission for adding/editing location
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $locationName = trim($_POST['location_name']);
        $cityId = (int)$_POST['city_id'];
        $status = $_POST['status'];
        
        if (empty($locationName) || $cityId <= 0) {
            $message = '<div class="alert alert-danger">Location name and city are required.</div>';
        } else {
            $locationData = [
                'location_name' => $locationName,
                'city_id' => $cityId,
                'status' => $status
            ];
            
            if (isset($_POST['edit_id']) && $_POST['edit_id'] > 0) {
                // Update existing location
                $editId = (int)$_POST['edit_id'];
                if ($locationObj->update($editId, $locationData)) {
                    $message = '<div class="alert alert-success">Location updated successfully.</div>';
                } else {
                    $message = '<div class="alert alert-danger">Failed to update location.</div>';
                }
            } else {
                // Add new location
                $locationData['slug'] = Utility::generateSlug($locationName);
                $locationData['created_at'] = date('Y-m-d H:i:s');
                
                if ($locationObj->create($locationData)) {
                    $message = '<div class="alert alert-success">Location added successfully.</div>';
                } else {
                    $message = '<div class="alert alert-danger">Failed to add location.</div>';
                }
            }
        }
    }
    
    // Get location to edit
    $editLocation = null;
    if ($action === 'edit' && $locationId > 0) {
        $editLocation = $locationObj->getById($locationId);
        
        // Get cities for the state of this location
        if ($editLocation) {
            $cityState = $cityObj->getById($editLocation['city_id']);
            if ($cityState) {
                $citiesData = $cityObj->getAll(['state_id' => $cityState['state_id']]);
                $cities = $citiesData['cities'] ?? [];
            }
        }
    }
    
    // Get all locations with optional city filter
    $filters = [];
    if ($cityFilter > 0) {
        $filters['city_id'] = $cityFilter;
    }
    
    $locationsData = $locationObj->getAll($filters);
    $locations = $locationsData['locations'] ?? [];
    
} catch (Exception $e) {
    die('<div style="color:red;padding:20px;border:2px solid red;margin:20px;">'
        . '<h2>Error</h2>'
        . '<p><strong>Message:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>'
        . '<p><strong>File:</strong> ' . htmlspecialchars($e->getFile()) . '</p>'
        . '<p><strong>Line:</strong> ' . htmlspecialchars($e->getLine()) . '</p>'
        . '</div>');
}

// Page title
$pageTitle = 'Locations';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Admin Panel | <?php echo $settings->getSiteName(); ?></title>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo getAssetUrl($settings->getSiteFavicon()); ?>" type="image/x-icon">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Admin CSS -->
    <link rel="stylesheet" href="assets/css/admin.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <?php include 'templates/sidebar.php'; ?>
        
        <!-- Main Content -->
        <div class="admin-main" id="adminMain">
            <!-- Header -->
            <?php include 'templates/header.php'; ?>
            
            <!-- Content -->
            <div class="admin-content">
                <div class="page-title">
                    <div>
                        <h1><?php echo $pageTitle; ?></h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="dashboard.php">Home</a></li>
                                <li class="breadcrumb-item">Locations</li>
                                <li class="breadcrumb-item active" aria-current="page"><?php echo $pageTitle; ?></li>
                            </ol>
                        </nav>
                    </div>
                </div>
                
                <?php echo $message; ?>
                
                <!-- Filter by City -->
                <?php if (!$editLocation): ?>
                    <div class="admin-card mb-4">
                        <div class="admin-card-body">
                            <form action="" method="get" class="row g-3">
                                <div class="col-md-4">
                                    <label for="state_id" class="form-label">State</label>
                                    <select class="form-select" id="state_id">
                                        <option value="">Select State</option>
                                        <?php foreach ($states as $state): ?>
                                            <option value="<?php echo $state['state_id']; ?>">
                                                <?php echo htmlspecialchars($state['state_name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="city_id" class="form-label">City</label>
                                    <select class="form-select" id="city_id" name="city_id">
                                        <option value="">Select City</option>
                                        <?php foreach ($cities as $city): ?>
                                            <option value="<?php echo $city['city_id']; ?>" <?php echo $cityFilter == $city['city_id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($city['city_name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-2 d-flex align-items-end">
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-filter"></i> Filter
                                    </button>
                                </div>
                                <div class="col-md-2 d-flex align-items-end">
                                    <a href="locations.php" class="btn btn-secondary w-100">
                                        <i class="fas fa-sync-alt"></i> Reset
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                <?php endif; ?>
                
                <div class="row">
                    <!-- Add/Edit Location Form -->
                    <div class="col-md-4">
                        <div class="admin-card">
                            <div class="admin-card-header">
                                <h2 class="admin-card-title">
                                    <i class="fas fa-map-marker-alt me-2"></i> <?php echo $editLocation ? 'Edit Location' : 'Add New Location'; ?>
                                </h2>
                            </div>
                            <div class="admin-card-body">
                                <form action="" method="post">
                                    <?php if ($editLocation): ?>
                                        <input type="hidden" name="edit_id" value="<?php echo $editLocation['location_id']; ?>">
                                    <?php endif; ?>
                                    
                                    <div class="mb-3">
                                        <label for="form_state_id" class="form-label">State <span class="text-danger">*</span></label>
                                        <select class="form-select" id="form_state_id" required>
                                            <option value="">Select State</option>
                                            <?php foreach ($states as $state): ?>
                                                <option value="<?php echo $state['state_id']; ?>" <?php echo $editLocation && isset($cityState) && $cityState['state_id'] == $state['state_id'] ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($state['state_name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="form_city_id" class="form-label">City <span class="text-danger">*</span></label>
                                        <select class="form-select" id="form_city_id" name="city_id" required>
                                            <option value="">Select City</option>
                                            <?php foreach ($cities as $city): ?>
                                                <option value="<?php echo $city['city_id']; ?>" <?php echo $editLocation && $editLocation['city_id'] == $city['city_id'] ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($city['city_name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="location_name" class="form-label">Location Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="location_name" name="location_name" value="<?php echo $editLocation ? htmlspecialchars($editLocation['location_name']) : ''; ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="status" class="form-label">Status</label>
                                        <select class="form-select" id="status" name="status">
                                            <option value="active" <?php echo $editLocation && $editLocation['status'] === 'active' ? 'selected' : ''; ?>>Active</option>
                                            <option value="inactive" <?php echo $editLocation && $editLocation['status'] === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                                        </select>
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <?php if ($editLocation): ?>
                                            <button type="submit" class="btn btn-primary">Update Location</button>
                                            <a href="locations.php<?php echo $cityFilter ? '?city_id=' . $cityFilter : ''; ?>" class="btn btn-secondary">Cancel</a>
                                        <?php else: ?>
                                            <button type="submit" class="btn btn-primary">Add Location</button>
                                        <?php endif; ?>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Locations List -->
                    <div class="col-md-8">
                        <div class="admin-card">
                            <div class="admin-card-header">
                                <h2 class="admin-card-title">
                                    <i class="fas fa-list me-2"></i> All Locations
                                    <?php if ($cityFilter && !empty($cities)): ?>
                                        <?php foreach ($cities as $city): ?>
                                            <?php if ($city['city_id'] == $cityFilter): ?>
                                                <span class="text-muted fs-6 ms-2">in <?php echo htmlspecialchars($city['city_name']); ?></span>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </h2>
                                <span class="badge bg-primary"><?php echo count($locations); ?> Total</span>
                            </div>
                            <div class="admin-card-body p-0">
                                <div class="table-responsive">
                                    <table class="admin-table">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Name</th>
                                                <th>City</th>
                                                <th>State</th>
                                                <th>Slug</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (!empty($locations)): ?>
                                                <?php foreach ($locations as $location): ?>
                                                    <tr>
                                                        <td><?php echo $location['location_id']; ?></td>
                                                        <td><?php echo htmlspecialchars($location['location_name']); ?></td>
                                                        <td><?php echo htmlspecialchars($location['city_name']); ?></td>
                                                        <td><?php echo htmlspecialchars($location['state_name']); ?></td>
                                                        <td><?php echo htmlspecialchars($location['slug']); ?></td>
                                                        <td>
                                                            <?php if ($location['status'] === 'active'): ?>
                                                                <span class="badge bg-success">Active</span>
                                                            <?php else: ?>
                                                                <span class="badge bg-danger">Inactive</span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <div class="btn-group">
                                                                <a href="locations.php?action=edit&id=<?php echo $location['location_id']; ?><?php echo $cityFilter ? '&city_id=' . $cityFilter : ''; ?>" class="btn btn-sm btn-primary" title="Edit">
                                                                    <i class="fas fa-edit"></i>
                                                                </a>
                                                                <a href="locations.php?action=delete&id=<?php echo $location['location_id']; ?><?php echo $cityFilter ? '&city_id=' . $cityFilter : ''; ?>" class="btn btn-sm btn-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this location?');">
                                                                    <i class="fas fa-trash"></i>
                                                                </a>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="7" class="text-center">No locations found.</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Footer -->
            <?php include 'templates/footer.php'; ?>
        </div>
    </div>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Admin JS -->
    <script src="assets/js/admin.js"></script>
    
    <script>
        $(document).ready(function() {
            // Load cities when state is selected (for filter)
            $('#state_id').change(function() {
                var stateId = $(this).val();
                if (stateId) {
                    $.ajax({
                        url: 'ajax/get-cities.php',
                        type: 'POST',
                        data: {state_id: stateId},
                        dataType: 'json',
                        success: function(data) {
                            var citySelect = $('#city_id');
                            citySelect.empty();
                            citySelect.append('<option value="">Select City</option>');
                            
                            $.each(data, function(key, value) {
                                citySelect.append('<option value="' + value.city_id + '">' + value.city_name + '</option>');
                            });
                        }
                    });
                } else {
                    $('#city_id').empty();
                    $('#city_id').append('<option value="">Select City</option>');
                }
            });
            
            // Load cities when state is selected (for form)
            $('#form_state_id').change(function() {
                var stateId = $(this).val();
                if (stateId) {
                    $.ajax({
                        url: 'ajax/get-cities.php',
                        type: 'POST',
                        data: {state_id: stateId},
                        dataType: 'json',
                        success: function(data) {
                            var citySelect = $('#form_city_id');
                            citySelect.empty();
                            citySelect.append('<option value="">Select City</option>');
                            
                            $.each(data, function(key, value) {
                                citySelect.append('<option value="' + value.city_id + '">' + value.city_name + '</option>');
                            });
                        }
                    });
                } else {
                    $('#form_city_id').empty();
                    $('#form_city_id').append('<option value="">Select City</option>');
                }
            });
        });
    </script>
</body>
</html>