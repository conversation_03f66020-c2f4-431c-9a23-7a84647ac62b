<?php
require_once '../includes/autoload.php';
Auth::requireCoachingOwner();

$coachingId = $_SESSION['coaching_id'];
$reviewObj = new Review();

// Handle approve/reject/delete
$message = '';
if (isset($_GET['action'], $_GET['review_id'])) {
    $reviewId = (int)$_GET['review_id'];
    if ($_GET['action'] === 'delete') {
        $deleted = Database::getInstance()->delete('reviews', 'review_id = ? AND coaching_id = ?', [$reviewId, $coachingId]);
        $message = $deleted ? '<div class="alert alert-success">Review deleted.</div>' : '<div class="alert alert-danger">Failed to delete review.</div>';
    } elseif ($_GET['action'] === 'approve') {
        $reviewObj->update($reviewId, ['status' => 'approved']);
    } elseif ($_GET['action'] === 'reject') {
        $reviewObj->update($reviewId, ['status' => 'rejected']);
    }
}

// Fetch all reviews for this coaching center
$reviews = Database::getInstance()->fetchAll('SELECT r.*, u.username, u.email FROM reviews r LEFT JOIN users u ON r.user_id = u.user_id WHERE r.coaching_id = ? ORDER BY r.created_at DESC', [$coachingId]);
$pageTitle = 'Reviews';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Coaching Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="<?php echo getAssetUrl('css/admin.css'); ?>">
</head>
<body>
    <div class="dashboard-container">
        <?php include 'templates/sidebar.php'; ?>
        <div class="main-content">
            <?php include 'templates/header.php'; ?>
            <div class="container-fluid">
                <div class="row mb-4">
                    <div class="col-12">
                        <h1 class="page-title">Reviews</h1>
                        <p class="text-muted">View and manage all reviews for your coaching center.</p>
                    </div>
                </div>
                <?php echo $message; ?>
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">All Reviews</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-striped mb-0">
                                <thead>
                                    <tr>
                                        <th>User</th>
                                        <th>Rating</th>
                                        <th>Title</th>
                                        <th>Review</th>
                                        <th>Date</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($reviews)): ?>
                                        <?php foreach ($reviews as $review): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($review['username'] ?? $review['email'] ?? 'Anonymous'); ?></td>
                                                <td>
                                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                                        <i class="fas fa-star <?php echo $i <= $review['rating'] ? 'text-warning' : 'text-muted'; ?>"></i>
                                                    <?php endfor; ?>
                                                    (<?php echo number_format($review['rating'], 1); ?>)
                                                </td>
                                                <td><?php echo htmlspecialchars($review['title']); ?></td>
                                                <td><?php echo htmlspecialchars(substr($review['review_text'], 0, 100)); ?><?php echo strlen($review['review_text']) > 100 ? '...' : ''; ?></td>
                                                <td><?php echo date('M d, Y H:i', strtotime($review['created_at'])); ?></td>
                                                <td>
                                                    <span class="badge bg-<?php
                                                        if ($review['status'] === 'pending') echo 'warning';
                                                        elseif ($review['status'] === 'approved') echo 'success';
                                                        elseif ($review['status'] === 'rejected') echo 'danger';
                                                        else echo 'info';
                                                    ?>"><?php echo ucfirst($review['status']); ?></span>
                                                </td>
                                                <td>
                                                    <a href="#" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#reviewModal<?php echo $review['review_id']; ?>">View</a>
                                                    <a href="reviews.php?action=approve&review_id=<?php echo $review['review_id']; ?>" class="btn btn-sm btn-success">Approve</a>
                                                    <a href="reviews.php?action=reject&review_id=<?php echo $review['review_id']; ?>" class="btn btn-sm btn-warning">Reject</a>
                                                    <a href="reviews.php?action=delete&review_id=<?php echo $review['review_id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Delete this review?');">Delete</a>
                                                </td>
                                            </tr>
                                            <!-- Modal for viewing review details -->
                                            <div class="modal fade" id="reviewModal<?php echo $review['review_id']; ?>" tabindex="-1" aria-labelledby="reviewModalLabel<?php echo $review['review_id']; ?>" aria-hidden="true">
                                                <div class="modal-dialog">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title" id="reviewModalLabel<?php echo $review['review_id']; ?>">Review Details</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <p><strong>User:</strong> <?php echo htmlspecialchars($review['username'] ?? $review['email'] ?? 'Anonymous'); ?></p>
                                                            <p><strong>Rating:</strong> <?php echo number_format($review['rating'], 1); ?></p>
                                                            <p><strong>Title:</strong> <?php echo htmlspecialchars($review['title']); ?></p>
                                                            <p><strong>Review:</strong><br><?php echo nl2br(htmlspecialchars($review['review_text'])); ?></p>
                                                            <p><strong>Date:</strong> <?php echo date('M d, Y H:i', strtotime($review['created_at'])); ?></p>
                                                            <p><strong>Status:</strong> <?php echo ucfirst($review['status']); ?></p>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr><td colspan="7" class="text-center">No reviews found.</td></tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
