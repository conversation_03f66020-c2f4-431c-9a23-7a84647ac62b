# Coaching Directory Website

A comprehensive directory website for coaching centers, allowing users to search, compare, and review coaching institutes across various categories and locations.

## Features

- **User-friendly Interface**: Modern, responsive design with intuitive navigation
- **Advanced Search**: Find coaching centers by category, location, or keyword
- **Detailed Profiles**: Comprehensive information about each coaching center
- **Reviews & Ratings**: User-generated reviews and ratings for coaching centers
- **User Accounts**: Registration, login, and profile management
- **Favorites**: Save coaching centers to favorites for later reference
- **Blog Section**: Educational articles and updates
- **Admin Dashboard**: Manage coaching centers, users, reviews, and more
- **Coaching Owner Dashboard**: Manage coaching center profiles and respond to reviews

## Technology Stack

- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap 5
- **Backend**: PHP
- **Database**: MySQL
- **Additional Libraries**: 
  - Font Awesome (icons)
  - Swiper.js (sliders)
  - GLightbox (image gallery)

## Installation

1. **Clone the repository**
   ```
   git clone https://github.com/yourusername/coaching-directory.git
   ```

2. **Set up the database**
   - Create a new MySQL database
   - Import the SQL file from the `database` folder
   - Update database credentials in `includes/config.php`

3. **Configure the application**
   - Update site settings in the admin panel
   - Set up email configuration for notifications

4. **Set up virtual host (optional)**
   - Configure your web server to point to the project directory
   - Update base URL in `includes/config.php`

## Directory Structure

```
coaching/
├── admin/              # Admin dashboard
├── assets/             # Static assets (CSS, JS, images)
│   ├── css/            # Stylesheets
│   ├── js/             # JavaScript files
│   └── images/         # Images
├── dashboard/          # User and coaching owner dashboard
├── includes/           # PHP includes
│   ├── classes/        # PHP classes
│   ├── functions/      # Helper functions
│   └── templates/      # Reusable templates
├── uploads/            # User uploaded files
│   ├── coaching/       # Coaching center images
│   ├── blog/           # Blog post images
│   └── users/          # User profile images
└── templates/          # Page templates
```

## Development

### Using Dummy Data

For development and testing purposes, the application includes a dummy data generator:

1. Include the dummy data file:
   ```php
   require_once 'includes/dummy-data.php';
   ```

2. Use the dummy data functions:
   ```php
   $dummyCoachings = getDummyCoachingCenters(6);
   $dummyCategories = getDummyCategories(8);
   $dummyCities = getDummyCities(8);
   $dummyPosts = getDummyBlogPosts(3);
   $dummyTestimonials = getDummyTestimonials(5);
   ```

### Placeholder Images

The application uses placeholder images for development:

```php
require_once 'includes/generate-dummy-images.php';

// Get placeholder images
$coachingLogo = getCoachingLogoUrl(1, 'Coaching Name');
$categoryIcon = getCategoryIconUrl('engineering', 'Engineering');
$cityImage = getCityImageUrl('delhi', 'Delhi');
$blogImage = getBlogImageUrl('jee', 'JEE Preparation');
$testimonialImage = getTestimonialImageUrl(1);
$heroImage = getHeroImageUrl();
```

## Color Scheme

- **Primary Colors**:
  - Dark Blue: `#003366`
  - Light Blue: `#66B2FF`
  - White: `#FFFFFF`

- **Secondary Colors**:
  - Light Gray: `#F5F5F5`
  - Dark Gray: `#333333`

- **Accent Colors**:
  - Electric Cyan: `#00FFFF`
  - Neon Green: `#00FF00`
  - Magenta: `#FF00FF`

## Typography

- **Headings**:
  - H1: 40px
  - H2: 30px
  - H3: 24px
  - H4: 20px
  - H5: 18px
  - H6: 16px

- **Body Text**: 16px

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgements

- [Bootstrap](https://getbootstrap.com/)
- [Font Awesome](https://fontawesome.com/)
- [Swiper.js](https://swiperjs.com/)
- [GLightbox](https://biati-digital.github.io/glightbox/)