<?php
/**
 * Review Class
 * Handles review functionality
 */
class Review {
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Add a new review
     * @param array $data Review data
     * @return int|bool Review ID or false on failure
     */
    public function add($data) {
        // Set created_at
        $data['created_at'] = date('Y-m-d H:i:s');
        
        // Set status to pending if not provided
        if (!isset($data['status'])) {
            $data['status'] = 'pending';
        }
        
        // Insert review
        $reviewId = $this->db->insert('reviews', $data);
        
        if ($reviewId) {
            // Update coaching center rating
            $this->updateCoachingRating($data['coaching_id']);
        }
        
        return $reviewId;
    }
    
    /**
     * Update review
     * @param int $reviewId Review ID
     * @param array $data Review data
     * @return bool True if update successful
     */
    public function update($reviewId, $data) {
        // Set updated_at
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        // Update review
        $result = $this->db->update('reviews', $data, ['review_id' => $reviewId]);
        
        if ($result) {
            // Get coaching ID
            $review = $this->getById($reviewId);
            
            if ($review) {
                // Update coaching center rating
                $this->updateCoachingRating($review['coaching_id']);
            }
        }
        
        return $result;
    }
    
    /**
     * Delete review
     * @param int $reviewId Review ID
     * @return bool True if delete successful
     */
    public function delete($reviewId) {
        // Get coaching ID before deleting
        $review = $this->getById($reviewId);
        
        if (!$review) {
            return false;
        }
        
        // Delete review
        $result = $this->db->delete('reviews', ['review_id' => $reviewId]);
        
        if ($result) {
            // Update coaching center rating
            $this->updateCoachingRating($review['coaching_id']);
            
            // Delete review votes
            $this->db->delete('review_votes', ['review_id' => $reviewId]);
        }
        
        return $result;
    }
    
    /**
     * Get review by ID
     * @param int $reviewId Review ID
     * @return array|null Review data
     */
    public function getById($reviewId) {
        return $this->db->fetchRow(
            "SELECT r.*, u.username as user_name, u.profile_image as user_image, 
                    c.coaching_name, c.slug as coaching_slug
             FROM reviews r
             LEFT JOIN users u ON r.user_id = u.user_id
             LEFT JOIN coaching_centers c ON r.coaching_id = c.coaching_id
             WHERE r.review_id = ?",
            [$reviewId]
        );
    }
    
    /**
     * Get reviews by coaching center
     * @param int $coachingId Coaching center ID
     * @param string $status Review status (all, approved, pending, rejected)
     * @param int $page Page number
     * @param int $limit Records per page
     * @return array Reviews
     */
    public function getByCoaching($coachingId, $status = 'approved', $page = 1, $limit = RECORDS_PER_PAGE) {
        $offset = ($page - 1) * $limit;
        $params = [$coachingId];
        
        $sql = "SELECT r.*, u.username as user_name, u.profile_image as user_image,
                       (SELECT COUNT(*) FROM review_votes WHERE review_id = r.review_id AND vote_type = 'helpful') as helpful_count,
                       (SELECT COUNT(*) FROM review_votes WHERE review_id = r.review_id AND vote_type = 'unhelpful') as unhelpful_count
                FROM reviews r
                LEFT JOIN users u ON r.user_id = u.user_id
                WHERE r.coaching_id = ?";
        
        if ($status !== 'all') {
            $sql .= " AND r.status = ?";
            $params[] = $status;
        }
        
        $sql .= " ORDER BY r.created_at DESC LIMIT ?, ?";
        $params[] = $offset;
        $params[] = $limit;
        
        return $this->db->fetchAll($sql, $params);
    }

    
    /**
     * Get all reviews
     * @param string $status Review status (all, approved, pending, rejected)
     * @param int $page Page number
     * @param int $limit Records per page
     * @return array Reviews
     */
    public function getAll($status = 'all', $page = 1, $limit = RECORDS_PER_PAGE) {
        $offset = ($page - 1) * $limit;
        $params = [];
        
        $sql = "SELECT r.*, u.username as user_name, u.profile_image as user_image,
                       c.coaching_name, c.slug as coaching_slug
                FROM reviews r
                LEFT JOIN users u ON r.user_id = u.user_id
                LEFT JOIN coaching_centers c ON r.coaching_id = c.coaching_id";
        
        if ($status !== 'all') {
            $sql .= " WHERE r.status = ?";
            $params[] = $status;
        }
        
        $sql .= " ORDER BY r.created_at DESC LIMIT ?, ?";
        $params[] = $offset;
        $params[] = $limit;
        
        return $this->db->fetchAll($sql, $params);
    }
    
    
    /**
     * Update coaching center rating
     * @param int $coachingId Coaching center ID
     * @return bool True if update successful
     */
    public function updateCoachingRating($coachingId) {
        // Get average rating
        $result = $this->db->fetchRow(
            "SELECT AVG(rating) as avg_rating, COUNT(*) as review_count
             FROM reviews
             WHERE coaching_id = ? AND status = 'approved'",
            [$coachingId]
        );
        
        $avgRating = $result['avg_rating'] ? round($result['avg_rating'], 1) : 0;
        $reviewCount = $result['review_count'];
        
        // Update coaching center
        return $this->db->update(
            'coaching_centers',
            [
                'rating' => $avgRating,
                'review_count' => $reviewCount,
                'updated_at' => date('Y-m-d H:i:s')
            ],
            ['coaching_id' => $coachingId]
        );
    }
    
    /**
     * Vote on a review
     * @param int $reviewId Review ID
     * @param int $userId User ID
     * @param string $voteType Vote type (helpful, unhelpful)
     * @return bool True if vote successful
     */
    public function vote($reviewId, $userId, $voteType) {
        // Check if user has already voted
        $existingVote = $this->db->fetchRow(
            "SELECT * FROM review_votes WHERE review_id = ? AND user_id = ?",
            [$reviewId, $userId]
        );
        
        if ($existingVote) {
            // Update existing vote
            return $this->db->update(
                'review_votes',
                [
                    'vote_type' => $voteType,
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'review_id' => $reviewId,
                    'user_id' => $userId
                ]
            );
        } else {
            // Insert new vote
            return $this->db->insert(
                'review_votes',
                [
                    'review_id' => $reviewId,
                    'user_id' => $userId,
                    'vote_type' => $voteType,
                    'created_at' => date('Y-m-d H:i:s')
                ]
            );
        }
    }
    
    /**
     * Check if user has reviewed a coaching center
     * @param int $coachingId Coaching center ID
     * @param int $userId User ID
     * @return bool True if user has reviewed
     */
    public function hasUserReviewed($coachingId, $userId) {
        $result = $this->db->fetchRow(
            "SELECT COUNT(*) as count FROM reviews WHERE coaching_id = ? AND user_id = ?",
            [$coachingId, $userId]
        );
        
        return $result['count'] > 0;
    }
    
    /**
     * Get review by user and coaching center
     * @param int $coachingId Coaching center ID
     * @param int $userId User ID
     * @return array|null Review data
     */
    public function getByUserAndCoaching($coachingId, $userId) {
        return $this->db->fetchRow(
            "SELECT * FROM reviews WHERE coaching_id = ? AND user_id = ?",
            [$coachingId, $userId]
        );
    }
    
    /**
     * Get review distribution by coaching center
     * @param int $coachingId Coaching center ID
     * @return array Review distribution
     */
    public function getDistributionByCoaching($coachingId) {
        $distribution = [];
        
        for ($i = 5; $i >= 1; $i--) {
            $result = $this->db->fetchRow(
                "SELECT COUNT(*) as count FROM reviews WHERE coaching_id = ? AND rating = ? AND status = 'approved'",
                [$coachingId, $i]
            );
            
            $distribution[$i] = $result['count'];
        }
        
        return $distribution;
    }
    
    /**
     * Get count by status
     * @param string $status Status
     * @return int Count
     */
    public function getCountByStatus($status) {
        return $this->db->count('reviews', 'status = ?', [$status]);
    }
    
    /**
     * Get total count of reviews
     * @param string $status Optional status filter
     * @return int Total count
     */
    public function getTotalCount($status = null) {
        $where = "1=1";
        $params = [];
        
        if ($status !== null) {
            $where .= " AND status = ?";
            $params[] = $status;
        }
        
        return $this->db->count('reviews', $where, $params);
    }
    
    /**
     * Get recent reviews
     * @param int $limit Number of reviews to get
     * @return array Recent reviews
     */
    public function getRecent($limit = 5) {
        return $this->db->fetchAll(
            "SELECT r.*, u.username as user_name, u.profile_image as user_image,
                    c.coaching_name, c.slug as coaching_slug
             FROM reviews r
             LEFT JOIN users u ON r.user_id = u.user_id
             LEFT JOIN coaching_centers c ON r.coaching_id = c.coaching_id
             ORDER BY r.created_at DESC
             LIMIT ?",
            [$limit]
        );
    }
}