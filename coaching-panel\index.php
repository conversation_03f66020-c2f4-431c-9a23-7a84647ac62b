<?php
/**
 * Coaching Panel Dashboard
 */
require_once '../includes/autoload.php';

// Check if user is logged in as coaching owner
Auth::requireCoachingOwner();

// Get coaching center data
if (!isset($_SESSION['coaching_id'])) {
    redirect('login.php');
    exit;
}
$coachingId = $_SESSION['coaching_id'];
$coachingObj = new CoachingCenter();
$coaching = $coachingObj->getById($coachingId);

// If coaching center not found, redirect to login
if (!$coaching) {
    redirect('login.php');
}

// Get dashboard statistics
$stats = [
    'total_views' => $coaching['total_views'] ?? 0,
    'total_reviews' => $coaching['total_reviews'] ?? 0,
    'avg_rating' => $coaching['avg_rating'] ?? 0,
    'total_inquiries' => $coachingObj->getInquiryCount($coachingId),
    'total_courses' => $coachingObj->getCourseCount($coachingId),
    'total_success_stories' => $coachingObj->getSuccessStoryCount($coachingId)
];

// Get recent inquiries
$inquiryObj = new Inquiry();
$recentInquiries = $inquiryObj->getRecentByCoaching($coachingId, 5);

// Get recent reviews
$reviewObj = new Review();
$recentReviews = $reviewObj->getByCoaching($coachingId, 'approved', 1, 5);

// Page title
$pageTitle = 'Dashboard';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Coaching Panel</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo getAssetUrl('css/admin.css'); ?>">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <?php include 'templates/sidebar.php'; ?>
        
        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <?php include 'templates/header.php'; ?>
            
            <!-- Dashboard Content -->
            <div class="container-fluid">
                <div class="row mb-4">
                    <div class="col-12">
                        <h1 class="page-title">Dashboard</h1>
                        <p class="text-muted">Welcome back, <?php echo htmlspecialchars($coaching['coaching_name']); ?>!</p>
                    </div>
                </div>
                
                <!-- Stats Cards -->
                <div class="row mb-4">
                    <div class="col-md-4 col-sm-6 mb-3">
                        <div class="card stats-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="stats-title">Total Views</h6>
                                        <h3 class="stats-value"><?php echo number_format($stats['total_views']); ?></h3>
                                    </div>
                                    <div class="stats-icon bg-primary">
                                        <i class="fas fa-eye"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 col-sm-6 mb-3">
                        <div class="card stats-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="stats-title">Total Reviews</h6>
                                        <h3 class="stats-value"><?php echo number_format($stats['total_reviews']); ?></h3>
                                    </div>
                                    <div class="stats-icon bg-success">
                                        <i class="fas fa-star"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 col-sm-6 mb-3">
                        <div class="card stats-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="stats-title">Average Rating</h6>
                                        <h3 class="stats-value"><?php echo number_format($stats['avg_rating'], 1); ?> <small class="text-muted">/ 5.0</small></h3>
                                    </div>
                                    <div class="stats-icon bg-warning">
                                        <i class="fas fa-star-half-alt"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 col-sm-6 mb-3">
                        <div class="card stats-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="stats-title">Total Inquiries</h6>
                                        <h3 class="stats-value"><?php echo number_format($stats['total_inquiries']); ?></h3>
                                    </div>
                                    <div class="stats-icon bg-info">
                                        <i class="fas fa-envelope"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 col-sm-6 mb-3">
                        <div class="card stats-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="stats-title">Total Courses</h6>
                                        <h3 class="stats-value"><?php echo number_format($stats['total_courses']); ?></h3>
                                    </div>
                                    <div class="stats-icon bg-danger">
                                        <i class="fas fa-book"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 col-sm-6 mb-3">
                        <div class="card stats-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="stats-title">Success Stories</h6>
                                        <h3 class="stats-value"><?php echo number_format($stats['total_success_stories']); ?></h3>
                                    </div>
                                    <div class="stats-icon bg-secondary">
                                        <i class="fas fa-trophy"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Inquiries and Reviews -->
                <div class="row">
                    <!-- Recent Inquiries -->
                    <div class="col-lg-6 mb-4">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">Recent Inquiries</h5>
                                <a href="inquiries.php" class="btn btn-sm btn-primary">View All</a>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($recentInquiries)): ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Name</th>
                                                    <th>Email</th>
                                                    <th>Date</th>
                                                    <th>Status</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($recentInquiries as $inquiry): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($inquiry['name']); ?></td>
                                                        <td><?php echo htmlspecialchars($inquiry['email']); ?></td>
                                                        <td><?php echo date('M d, Y', strtotime($inquiry['created_at'])); ?></td>
                                                        <td>
                                                            <?php if ($inquiry['status'] == 'pending'): ?>
                                                                <span class="badge bg-warning">Pending</span>
                                                            <?php elseif ($inquiry['status'] == 'responded'): ?>
                                                                <span class="badge bg-success">Responded</span>
                                                            <?php else: ?>
                                                                <span class="badge bg-secondary">Closed</span>
                                                            <?php endif; ?>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else: ?>
                                    <p class="text-center text-muted my-4">No inquiries yet.</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recent Reviews -->
                    <div class="col-lg-6 mb-4">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">Recent Reviews</h5>
                                <a href="reviews.php" class="btn btn-sm btn-primary">View All</a>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($recentReviews)): ?>
                                    <div class="reviews-list">
                                        <?php foreach ($recentReviews as $review): ?>
                                            <div class="review-item mb-3 pb-3 border-bottom">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <div>
                                                        <h6 class="mb-0"><?php echo htmlspecialchars($review['title']); ?></h6>
                                                        <div class="text-warning">
                                                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                                                <?php if ($i <= $review['rating']): ?>
                                                                    <i class="fas fa-star"></i>
                                                                <?php elseif ($i - 0.5 <= $review['rating']): ?>
                                                                    <i class="fas fa-star-half-alt"></i>
                                                                <?php else: ?>
                                                                    <i class="far fa-star"></i>
                                                                <?php endif; ?>
                                                            <?php endfor; ?>
                                                        </div>
                                                    </div>
                                                    <small class="text-muted"><?php echo date('M d, Y', strtotime($review['created_at'])); ?></small>
                                                </div>
                                                <p class="review-text mb-0"><?php echo htmlspecialchars(substr($review['review_text'], 0, 100)) . (strlen($review['review_text']) > 100 ? '...' : ''); ?></p>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php else: ?>
                                    <p class="text-center text-muted my-4">No reviews yet.</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Subscription Status -->
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Subscription Status</h5>
                            </div>
                            <div class="card-body">
                                <?php 
                                $subscriptionObj = new Subscription();
                                $subscription = $subscriptionObj->getCurrentByCoaching($coachingId);
                                
                                if ($subscription): 
                                    $daysLeft = (strtotime($subscription['end_date']) - time()) / (60 * 60 * 24);
                                    $percentLeft = min(100, max(0, ($daysLeft / 30) * 100));
                                ?>
                                    <div class="subscription-info">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <div>
                                                <h6 class="mb-0"><?php echo htmlspecialchars($subscription['plan_name']); ?></h6>
                                                <p class="text-muted mb-0">Valid until: <?php echo date('F d, Y', strtotime($subscription['end_date'])); ?></p>
                                            </div>
                                            <div>
                                                <span class="badge bg-<?php echo $daysLeft > 7 ? 'success' : ($daysLeft > 3 ? 'warning' : 'danger'); ?>">
                                                    <?php echo ceil($daysLeft); ?> days left
                                                </span>
                                            </div>
                                        </div>
                                        <div class="progress" style="height: 10px;">
                                            <div class="progress-bar bg-<?php echo $daysLeft > 7 ? 'success' : ($daysLeft > 3 ? 'warning' : 'danger'); ?>" 
                                                 role="progressbar" 
                                                 style="width: <?php echo $percentLeft; ?>%" 
                                                 aria-valuenow="<?php echo $percentLeft; ?>" 
                                                 aria-valuemin="0" 
                                                 aria-valuemax="100"></div>
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <div class="text-center py-4">
                                        <div class="mb-3">
                                            <i class="fas fa-exclamation-circle text-warning fa-3x"></i>
                                        </div>
                                        <h5>No Active Subscription</h5>
                                        <p class="text-muted">Your coaching center is not currently featured. Subscribe to a plan to get more visibility.</p>
                                        <a href="subscription.php" class="btn btn-primary">View Subscription Plans</a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="<?php echo getAssetUrl('js/admin.js'); ?>"></script>
</body>
</html>