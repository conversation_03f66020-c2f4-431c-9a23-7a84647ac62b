Options +FollowSymLinks
RewriteEngine On
RewriteBase /coaching/

RewriteEngine On
RewriteBase /coaching/

# Redirect index.php to root
RewriteCond %{THE_REQUEST} /index\.php [NC]
RewriteRule ^index\.php$ /coaching/ [R=301,L]

# Categories listing: /coaching/categories
RewriteRule ^categories/?$ categories.php [L,QSA]

# Category page: /coaching/category/slug
RewriteRule ^category/([^/]+)/?$ category.php?slug=$1 [L,QSA]

# City page: /coaching/city/slug
RewriteRule ^city/([^/]+)/?$ city.php?slug=$1 [L,QSA]

# Coaching center page: /coaching/center/slug
RewriteRule ^center/([^/]+)/?$ center.php?slug=$1 [L,QSA]

# Blog listing: /coaching/blog
RewriteRule ^blog/?$ blog.php [L,QSA]

# Blog post: /coaching/blog/slug
RewriteRule ^blog/([^/]+)/?$ blog-post.php?slug=$1 [L,QSA]

# Blog category: /coaching/blog-category/slug
RewriteRule ^blog-category/([^/]+)/?$ blog-category.php?slug=$1 [L,QSA]

# State page: /coaching/state/slug
RewriteRule ^state/([^/]+)/?$ state.php?slug=$1 [L,QSA]

# Contact page: /coaching/contact
RewriteRule ^contact/?$ contact.php [L,QSA]

# Search page: /coaching/search
RewriteRule ^search/?$ search.php [L,QSA]

# Register page: /coaching/register
RewriteRule ^register/?$ register.php [L,QSA]

# Login page: /coaching/login
RewriteRule ^login/?$ login.php [L,QSA]

# Any other .php file (fallback)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^([a-zA-Z0-9_-]+)$ $1.php [L,QSA]

# 404 for anything else
ErrorDocument 404 /coaching/404.php
ErrorDocument 500 /coaching/500.php

# Prevent directory listing
Options -Indexes

# Set default character set
AddDefaultCharset UTF-8

# Compress text, HTML, JavaScript, CSS, and XML
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Browser caching
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# Protect wp-config.php
<Files ~ "^.*\.([Hh][Tt][Aa])">
    Order allow,deny
    Deny from all
    Satisfy all
</Files>

# Protect .htaccess
<Files .htaccess>
    Order allow,deny
    Deny from all
</Files>

# Protect config files
<Files ~ "^.*\.([Cc][Oo][Nn][Ff][Ii][Gg]\.php)">
    Order allow,deny
    Deny from all
</Files>

# Protect includes directory
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteRule ^includes/ - [F,L]
</IfModule>

# PHP settings
<IfModule mod_php7.c>
    # Maximum upload file size
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    
    # Maximum execution time
    php_value max_execution_time 300
    
    # Maximum input time
    php_value max_input_time 300
    
    # Memory limit
    php_value memory_limit 128M
</IfModule>