<?php
/**
 * Inquiry Class
 * Handles inquiries to coaching centers
 */
class Inquiry {
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Add a new inquiry
     * @param array $data Inquiry data
     * @return int|bool Inquiry ID or false on failure
     */
    public function add($data) {
        return $this->db->insert('inquiries', $data);
    }
    
    /**
     * Get inquiry by ID
     * @param int $inquiryId Inquiry ID
     * @return array|null Inquiry data
     */
    public function getById($inquiryId) {
        return $this->db->fetchRow(
            "SELECT i.*, c.coaching_name 
             FROM inquiries i
             LEFT JOIN coaching_centers c ON i.coaching_id = c.coaching_id
             WHERE i.inquiry_id = ?",
            [$inquiryId]
        );
    }
    
    /**
     * Get inquiries by coaching center
     * @param int $coachingId Coaching center ID
     * @param int $page Page number
     * @param int $limit Records per page
     * @return array Inquiries and pagination info
     */
    public function getByCoaching($coachingId, $page = 1, $limit = RECORDS_PER_PAGE) {
        // Get total count
        $countSql = "SELECT COUNT(*) as count FROM inquiries WHERE coaching_id = ?";
        $countResult = $this->db->fetchRow($countSql, [$coachingId]);
        $totalCount = $countResult['count'];
        
        // Calculate pagination
        $totalPages = ceil($totalCount / $limit);
        $page = max(1, min($page, $totalPages));
        $offset = ($page - 1) * $limit;
        
        // Get inquiries
        $sql = "SELECT * FROM inquiries 
                WHERE coaching_id = ? 
                ORDER BY created_at DESC 
                LIMIT {$offset}, {$limit}";
        $inquiries = $this->db->fetchAll($sql, [$coachingId]);
        
        // Return inquiries and pagination info
        return [
            'inquiries' => $inquiries,
            'pagination' => [
                'total' => $totalCount,
                'per_page' => $limit,
                'current_page' => $page,
                'last_page' => $totalPages
            ]
        ];
    }
    
    /**
     * Get recent inquiries by coaching center
     * @param int $coachingId Coaching center ID
     * @param int $limit Number of inquiries to get
     * @return array Inquiries
     */
    public function getRecentByCoaching($coachingId, $limit = 5) {
        $sql = "SELECT * FROM inquiries 
                WHERE coaching_id = ? 
                ORDER BY created_at DESC 
                LIMIT {$limit}";
        return $this->db->fetchAll($sql, [$coachingId]);
    }
    
    /**
     * Get unread inquiries count
     * @param int $coachingId Coaching center ID
     * @return int Number of unread inquiries
     */
    public function getUnreadCount($coachingId) {
        $sql = "SELECT COUNT(*) as count FROM inquiries 
                WHERE coaching_id = ? AND is_read = 0";
        $result = $this->db->fetchRow($sql, [$coachingId]);
        return $result['count'];
    }
    
    /**
     * Mark inquiry as read
     * @param int $inquiryId Inquiry ID
     * @return bool True if update successful
     */
    public function markAsRead($inquiryId) {
        return $this->db->update(
            'inquiries',
            ['is_read' => 1],
            'inquiry_id = ?',
            [$inquiryId]
        );
    }
    
    /**
     * Update inquiry status
     * @param int $inquiryId Inquiry ID
     * @param string $status New status
     * @return bool True if update successful
     */
    public function updateStatus($inquiryId, $status) {
        return $this->db->update(
            'inquiries',
            ['status' => $status],
            'inquiry_id = ?',
            [$inquiryId]
        );
    }
    
    /**
     * Delete inquiry
     * @param int $inquiryId Inquiry ID
     * @return bool True if deletion successful
     */
    public function delete($inquiryId) {
        return $this->db->delete('inquiries', 'inquiry_id = ?', [$inquiryId]);
    }
}