<?php
/**
 * Blog Class
 * Handles blog posts and comments
 */
class Blog {
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Add a new blog post
     * @param array $data Blog post data
     * @return int|bool Post ID or false on failure
     */
    public function addPost($data) {
        // Generate slug
        $data['slug'] = Utility::generateUniqueSlug($data['title'], 'blog_posts');
        
        return $this->db->insert('blog_posts', $data);
    }
    
    /**
     * Update a blog post
     * @param int $postId Post ID
     * @param array $data Blog post data
     * @return bool True if update successful
     */
    public function updatePost($postId, $data) {
        // Check if title is being changed
        if (isset($data['title'])) {
            $currentPost = $this->getPostById($postId);
            
            if ($currentPost && $data['title'] !== $currentPost['title']) {
                $data['slug'] = Utility::generateUniqueSlug($data['title'], 'blog_posts', 'slug', $postId);
            }
        }
        
        return $this->db->update('blog_posts', $data, 'post_id = ?', [$postId]);
    }
    
    /**
     * Delete a blog post
     * @param int $postId Post ID
     * @return bool True if deletion successful
     */
    public function deletePost($postId) {
        // Begin transaction
        $this->db->beginTransaction();
        
        try {
            // Delete comments
            $this->db->delete('blog_comments', 'post_id = ?', [$postId]);
            
            // Delete post
            $deleted = $this->db->delete('blog_posts', 'post_id = ?', [$postId]);
            
            if (!$deleted) {
                $this->db->rollback();
                return false;
            }
            
            // Commit transaction
            $this->db->commit();
            
            return true;
        } catch (Exception $e) {
            $this->db->rollback();
            return false;
        }
    }
    
    /**
     * Get blog post by ID
     * @param int $postId Post ID
     * @return array|null Blog post data
     */
    public function getPostById($postId) {
        return $this->db->fetchRow(
            "SELECT p.*, u.username, u.first_name, u.last_name, c.category_name
             FROM blog_posts p
             LEFT JOIN users u ON p.user_id = u.user_id
             LEFT JOIN course_categories c ON p.category_id = c.category_id
             WHERE p.post_id = ?",
            [$postId]
        );
    }
    
    /**
     * Get blog post by slug
     * @param string $slug Post slug
     * @return array|null Blog post data
     */
    public function getPostBySlug($slug) {
        $post = $this->db->fetchRow(
            "SELECT p.*, u.username, u.first_name, u.last_name, c.category_name, c.slug as category_slug
             FROM blog_posts p
             LEFT JOIN users u ON p.user_id = u.user_id
             LEFT JOIN course_categories c ON p.category_id = c.category_id
             WHERE p.slug = ? AND p.status = 'published'",
            [$slug]
        );
        
        if (!$post) {
            return null;
        }
        
        // Increment view count
        $this->db->update(
            'blog_posts',
            ['view_count' => $post['view_count'] + 1],
            'post_id = ?',
            [$post['post_id']]
        );
        
        return $post;
    }
    
    /**
     * Get blog posts
     * @param array $filters Filters (optional)
     * @param int $page Page number (optional)
     * @param int $limit Records per page (optional)
     * @return array Blog posts and pagination info
     */
    public function getPosts($filters = [], $page = 1, $limit = RECORDS_PER_PAGE) {
        $where = "1=1";
        $params = [];
        
        // Apply filters
        if (isset($filters['status']) && !empty($filters['status'])) {
            $where .= " AND p.status = ?";
            $params[] = $filters['status'];
        } else {
            $where .= " AND p.status = 'published'";
        }
        
        if (isset($filters['category_id']) && !empty($filters['category_id'])) {
            $where .= " AND p.category_id = ?";
            $params[] = $filters['category_id'];
        }
        
        if (isset($filters['user_id']) && !empty($filters['user_id'])) {
            $where .= " AND p.user_id = ?";
            $params[] = $filters['user_id'];
        }
        
        if (isset($filters['search']) && !empty($filters['search'])) {
            $where .= " AND (p.title LIKE ? OR p.content LIKE ?)";
            $searchTerm = '%' . $filters['search'] . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }
        
        // Get total count
        $countSql = "SELECT COUNT(*) as count
                     FROM blog_posts p
                     WHERE {$where}";
        
        $countResult = $this->db->fetchRow($countSql, $params);
        $totalCount = $countResult['count'] ?? 0;
        
        // Calculate offset
        $offset = ($page - 1) * $limit;
        
        // Determine sort order
        $orderBy = "p.published_at DESC";
        
        if (isset($filters['sort']) && !empty($filters['sort'])) {
            switch ($filters['sort']) {
                case 'title_asc':
                    $orderBy = "p.title ASC";
                    break;
                case 'title_desc':
                    $orderBy = "p.title DESC";
                    break;
                case 'views_desc':
                    $orderBy = "p.view_count DESC";
                    break;
                case 'oldest':
                    $orderBy = "p.published_at ASC";
                    break;
            }
        }
        
        // Get posts
        $sql = "SELECT p.*, u.username, u.first_name, u.last_name, c.category_name, c.slug as category_slug
                FROM blog_posts p
                LEFT JOIN users u ON p.user_id = u.user_id
                LEFT JOIN course_categories c ON p.category_id = c.category_id
                WHERE {$where}
                ORDER BY {$orderBy}
                LIMIT ?, ?";
        
        $params[] = $offset;
        $params[] = $limit;
        
        $posts = $this->db->fetchAll($sql, $params);
        
        return [
            'posts' => $posts,
            'total' => $totalCount,
            'page' => $page,
            'limit' => $limit,
            'total_pages' => ceil($totalCount / $limit)
        ];
    }
    
    /**
     * Get recent blog posts
     * @param int $limit Number of posts to get
     * @return array Recent blog posts
     */
    public function getRecentPosts($limit = 5) {
        return $this->db->fetchAll(
            "SELECT p.*, u.username, u.first_name, u.last_name, c.category_name, c.slug as category_slug
             FROM blog_posts p
             LEFT JOIN users u ON p.user_id = u.user_id
             LEFT JOIN course_categories c ON p.category_id = c.category_id
             WHERE p.status = 'published'
             ORDER BY p.published_at DESC
             LIMIT ?",
            [$limit]
        );
    }
    
    /**
     * Get popular blog posts
     * @param int $limit Number of posts to get
     * @return array Popular blog posts
     */
    public function getPopularPosts($limit = 5) {
        return $this->db->fetchAll(
            "SELECT p.*, u.username, u.first_name, u.last_name, c.category_name, c.slug as category_slug
             FROM blog_posts p
             LEFT JOIN users u ON p.user_id = u.user_id
             LEFT JOIN course_categories c ON p.category_id = c.category_id
             WHERE p.status = 'published'
             ORDER BY p.view_count DESC
             LIMIT ?",
            [$limit]
        );
    }
    
    /**
     * Get related blog posts
     * @param int $postId Current post ID
     * @param int $categoryId Category ID
     * @param int $limit Number of posts to get
     * @return array Related blog posts
     */
    public function getRelatedPosts($postId, $categoryId, $limit = 3) {
        return $this->db->fetchAll(
            "SELECT p.*, u.username, u.first_name, u.last_name, c.category_name, c.slug as category_slug
             FROM blog_posts p
             LEFT JOIN users u ON p.user_id = u.user_id
             LEFT JOIN course_categories c ON p.category_id = c.category_id
             WHERE p.status = 'published' AND p.post_id != ? AND p.category_id = ?
             ORDER BY p.published_at DESC
             LIMIT ?",
            [$postId, $categoryId, $limit]
        );
    }
    
    /**
     * Add a comment
     * @param array $data Comment data
     * @return int|bool Comment ID or false on failure
     */
    public function addComment($data) {
        return $this->db->insert('blog_comments', $data);
    }
    
    /**
     * Update a comment
     * @param int $commentId Comment ID
     * @param array $data Comment data
     * @return bool True if update successful
     */
    public function updateComment($commentId, $data) {
        return $this->db->update('blog_comments', $data, 'comment_id = ?', [$commentId]);
    }
    
    /**
     * Delete a comment
     * @param int $commentId Comment ID
     * @return bool True if deletion successful
     */
    public function deleteComment($commentId) {
        // Begin transaction
        $this->db->beginTransaction();
        
        try {
            // Delete child comments
            $this->db->delete('blog_comments', 'parent_id = ?', [$commentId]);
            
            // Delete comment
            $deleted = $this->db->delete('blog_comments', 'comment_id = ?', [$commentId]);
            
            if (!$deleted) {
                $this->db->rollback();
                return false;
            }
            
            // Commit transaction
            $this->db->commit();
            
            return true;
        } catch (Exception $e) {
            $this->db->rollback();
            return false;
        }
    }
    
    /**
     * Get comments for a post
     * @param int $postId Post ID
     * @return array Comments
     */
    public function getComments($postId) {
        // Get parent comments
        $parentComments = $this->db->fetchAll(
            "SELECT c.*, u.username, u.first_name, u.last_name, u.profile_image
             FROM blog_comments c
             LEFT JOIN users u ON c.user_id = u.user_id
             WHERE c.post_id = ? AND c.parent_id IS NULL AND c.is_approved = 1
             ORDER BY c.created_at ASC",
            [$postId]
        );
        
        // Get child comments
        foreach ($parentComments as &$comment) {
            $comment['replies'] = $this->db->fetchAll(
                "SELECT c.*, u.username, u.first_name, u.last_name, u.profile_image
                 FROM blog_comments c
                 LEFT JOIN users u ON c.user_id = u.user_id
                 WHERE c.parent_id = ? AND c.is_approved = 1
                 ORDER BY c.created_at ASC",
                [$comment['comment_id']]
            );
        }
        
        return $parentComments;
    }
    
    /**
     * Get comment count for a post
     * @param int $postId Post ID
     * @return int Comment count
     */
    public function getCommentCount($postId) {
        $result = $this->db->fetchRow(
            "SELECT COUNT(*) as count FROM blog_comments WHERE post_id = ? AND is_approved = 1",
            [$postId]
        );
        
        return $result['count'] ?? 0;
    }
    
    /**
     * Approve a comment
     * @param int $commentId Comment ID
     * @return bool True if approval successful
     */
    public function approveComment($commentId) {
        return $this->db->update(
            'blog_comments',
            ['is_approved' => 1],
            'comment_id = ?',
            [$commentId]
        );
    }
    
    /**
     * Get pending comments
     * @return array Pending comments
     */
    public function getPendingComments() {
        return $this->db->fetchAll(
            "SELECT c.*, p.title as post_title, u.username, u.first_name, u.last_name
             FROM blog_comments c
             JOIN blog_posts p ON c.post_id = p.post_id
             LEFT JOIN users u ON c.user_id = u.user_id
             WHERE c.is_approved = 0
             ORDER BY c.created_at DESC"
        );
    }
}