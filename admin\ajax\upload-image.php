<?php
/**
 * AJAX Upload Image
 * Used for uploading images in the blog editor
 */
error_reporting(E_ALL);
ini_set('display_errors', 0);

header('Content-Type: application/json');

try {
    require_once '../../includes/autoload.php';
    
    // Check user authentication
    $user = new User();
    if (!$user->isLoggedIn() || !$user->isAdmin()) {
        throw new Exception('Unauthorized access');
    }
    
    // Check if file was uploaded
    if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('No file uploaded or upload error');
    }
    
    // Validate file type
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    $fileType = $_FILES['file']['type'];
    
    if (!in_array($fileType, $allowedTypes)) {
        throw new Exception('Invalid file type. Only JPG, PNG, GIF, and WEBP images are allowed.');
    }
    
    // Validate file size (max 5MB)
    $maxSize = 5 * 1024 * 1024; // 5MB
    if ($_FILES['file']['size'] > $maxSize) {
        throw new Exception('File size exceeds the maximum limit of 5MB.');
    }
    
    // Create upload directory if it doesn't exist
    $uploadDir = '../../uploads/blog/images/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    // Generate unique filename
    $fileName = 'image_' . time() . '_' . mt_rand(1000, 9999) . '.' . pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION);
    $targetFile = $uploadDir . $fileName;
    
    // Move uploaded file
    if (move_uploaded_file($_FILES['file']['tmp_name'], $targetFile)) {
        // Return success response
        echo json_encode([
            'success' => true,
            'url' => BASE_URL . 'uploads/blog/images/' . $fileName,
            'message' => 'Image uploaded successfully'
        ]);
    } else {
        throw new Exception('Failed to move uploaded file');
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}