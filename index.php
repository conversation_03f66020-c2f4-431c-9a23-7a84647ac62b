<?php
/**
 * Homepage
 */
require_once 'includes/autoload.php';

// Get settings
$settings = Settings::getInstance();

// Get data from database only
try {
    // Get categories
    $categoryObj = new Category();
    $popularCategories = $categoryObj->getPopular(8);
    
    // Get locations
    $locationObj = new Location();
    $popularCities = $locationObj->getPopularCities(8);
    
    // Get coaching centers
    $coachingObj = new CoachingCenter();
    $featuredCoachings = $coachingObj->getFeatured(6);
    $topRatedCoachings = $coachingObj->getTopRated(6);
    $popularCoachings = $coachingObj->getPopular(6);
    
    // Get blog posts
    $blogObj = new Blog();
    $recentPosts = $blogObj->getRecentPosts(3);
    
    // Initialize testimonials as empty array
    $testimonials = [];
} catch (Exception $e) {
    // If any error occurs, initialize empty arrays
    $popularCategories = [];
    $popularCities = [];
    $featuredCoachings = [];
    $topRatedCoachings = [];
    $popularCoachings = [];
    $recentPosts = [];
    $testimonials = [];
    
    // Log the error
    error_log("Error on homepage: " . $e->getMessage());
}

// Page title and meta
$pageTitle = $settings->getMetaTitle();
$pageDescription = $settings->getMetaDescription();
$pageKeywords = $settings->getMetaKeywords();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?php echo getMetaTags($pageTitle, $pageDescription, $pageKeywords); ?>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo getAssetUrl($settings->getSiteFavicon()); ?>" type="image/x-icon">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo getAssetUrl('css/style.css'); ?>">
</head>
<body>
    <!-- Header -->
    <?php include 'templates/header.php'; ?>
    
    <!-- Hero Section -->
    <section class="hero-section">
        <div class="hero-background">
            <div class="overlay"></div>
            <div class="glowing-circles">
                <div class="circle circle-1"></div>
                <div class="circle circle-2"></div>
                <div class="circle circle-3"></div>
            </div>
        </div>
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="hero-content slide-in-up">
                        <h1>Find the Best Coaching Centers Near You</h1>
                        <p>Discover top-rated coaching institutes for various competitive exams, courses, and skills. Compare, review, and choose the best for your educational journey.</p>
                        <div class="hero-buttons">
                            <a href="search.php" class="btn btn-primary btn-lg hover-glow">Explore Coaching Centers</a>
                            <a href="register.php?type=coaching_owner" class="btn btn-outline-primary btn-lg">List Your Coaching</a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="hero-image fade-in">
                        <?php 
                        // Include the placeholder image generator if not already included
                        if (!function_exists('getHeroImageUrl')) {
                            require_once 'includes/generate-dummy-images.php';
                        }
                        ?>
                        <img src="<?php echo getHeroImageUrl(); ?>" alt="Find Coaching Centers" class="img-fluid">
                    </div>
                </div>
            </div>
            
            <!-- Search Form -->
            <div class="search-form-container">
                <form action="search.php" method="GET" class="search-form">
                    <div class="row">
                        <div class="col-lg-4 col-md-6">
                            <div class="form-group">
                                <label for="keyword">What are you looking for?</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    <input type="text" id="keyword" name="keyword" class="form-control" placeholder="Search coaching centers...">
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="form-group">
                                <label for="category_id">Category</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-list"></i></span>
                                    <select id="category_id" name="category_id" class="form-select">
                                        <option value="">All Categories</option>
                                        <?php if (!empty($popularCategories)): ?>
                                            <?php foreach ($popularCategories as $category): ?>
                                                <option value="<?php echo $category['category_id']; ?>"><?php echo $category['category_name']; ?></option>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="form-group">
                                <label for="city_id">Location</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                                    <select id="city_id" name="city_id" class="form-select">
                                        <option value="">All Cities</option>
                                        <?php if (!empty($popularCities)): ?>
                                            <?php foreach ($popularCities as $city): ?>
                                                <option value="<?php echo $city['city_id']; ?>"><?php echo $city['city_name']; ?></option>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-2 col-md-6">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <button type="submit" class="btn btn-primary w-100 hover-glow">Search</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </section>
    
    <!-- Popular Categories Section -->
    <section class="categories-section section-padding">
        <div class="container">
            <div class="section-header text-center">
                <h2 class="with-line">Popular Categories</h2>
                <p>Explore coaching centers by categories</p>
            </div>
            
            <div class="row">
                <?php 
                // Define category icons if not available in database
                $categoryIcons = [
                    1 => 'fas fa-graduation-cap', // JEE/Engineering
                    2 => 'fas fa-heartbeat',      // NEET/Medical
                    3 => 'fas fa-landmark',       // UPSC/Civil Services
                    4 => 'fas fa-balance-scale',  // Law Entrance
                    5 => 'fas fa-chart-line',     // MBA Entrance
                    6 => 'fas fa-calculator',     // Banking/SSC
                    7 => 'fas fa-language',       // Language Learning
                    8 => 'fas fa-laptop-code',    // Computer/IT
                    9 => 'fas fa-palette',        // Arts & Design
                    10 => 'fas fa-music',         // Music & Dance
                    11 => 'fas fa-dumbbell',      // Sports & Fitness
                    12 => 'fas fa-book-open'      // School Tuition
                ];
                
                // Define category colors
                $categoryColors = [
                    'bg-primary', 'bg-success', 'bg-danger', 'bg-warning', 
                    'bg-info', 'bg-dark', 'bg-secondary', 'bg-primary'
                ];
                
                if (!empty($popularCategories)):
                    foreach ($popularCategories as $index => $category): 
                        $colorClass = $categoryColors[$index % count($categoryColors)];
                        $iconClass = isset($categoryIcons[$category['category_id']]) ? $categoryIcons[$category['category_id']] : 'fas fa-book';
                ?>
                    <div class="col-lg-3 col-md-4 col-sm-6">
                        <a href="<?php echo getCategoryUrl($category['slug']); ?>" class="category-card hover-scale">
                            <div class="category-icon <?php echo $colorClass; ?>">
                                <?php if (!empty($category['icon'])): ?>
                                    <img src="<?php echo getAssetUrl($category['icon']); ?>" alt="<?php echo $category['category_name']; ?>">
                                <?php else: ?>
                                    <i class="<?php echo $iconClass; ?>"></i>
                                <?php endif; ?>
                            </div>
                            <div class="category-content">
                                <h3><?php echo $category['category_name']; ?></h3>
                                <p><span class="count-badge"><?php echo $category['coaching_count']; ?></span> Coaching Centers</p>
                            </div>
                            <div class="category-arrow">
                                <i class="fas fa-arrow-right"></i>
                            </div>
                        </a>
                    </div>
                <?php endforeach; ?>
                <?php else: ?>
                    <div class="col-12 text-center">
                        <p>No categories available yet. Please check back later.</p>
                    </div>
                <?php endif; ?>
            </div>
            
            <div class="text-center mt-5">
                <a href="categories.php" class="btn btn-primary btn-lg hover-glow">View All Categories</a>
            </div>
        </div>
    </section>
    
    <!-- Featured Coaching Centers Section -->
    <section class="featured-section section-padding bg-light">
        <div class="container">
            <div class="section-header text-center">
                <h2>Featured Coaching Centers</h2>
                <p>Top coaching centers recommended for you</p>
            </div>
            
            <div class="row">
                <?php if (!empty($featuredCoachings)): ?>
                    <?php foreach ($featuredCoachings as $coaching): ?>
                    <div class="col-lg-4 col-md-6">
                        <div class="coaching-card">
                            <div class="coaching-image">
                                <img src="<?php echo isset($coaching['logo']) ? getUploadUrl($coaching['logo']) : getAssetUrl('images/default-logo.png'); ?>" alt="<?php echo $coaching['coaching_name']; ?>">
                                <?php if ($coaching['is_featured']): ?>
                                    <span class="featured-badge"><i class="fas fa-star"></i> Featured</span>
                                <?php endif; ?>
                            </div>
                            <div class="coaching-content">
                                <h3><a href="<?php echo getCoachingUrl($coaching['slug']); ?>"><?php echo $coaching['coaching_name']; ?></a></h3>
                                <div class="coaching-meta">
                                    <div class="location">
                                        <i class="fas fa-map-marker-alt"></i>
                                        <?php echo $coaching['city_name']; ?>, <?php echo $coaching['state_name']; ?>
                                    </div>
                                    <div class="rating">
                                        <?php echo getStarRating($coaching['avg_rating']); ?>
                                        <span class="reviews">(<?php echo $coaching['total_reviews']; ?> reviews)</span>
                                    </div>
                                </div>
                                <div class="coaching-categories">
                                    <?php foreach ($coaching['categories'] as $index => $category): ?>
                                        <?php if ($index < 3): ?>
                                            <a href="<?php echo getCategoryUrl($category['category_slug']); ?>" class="category-badge"><?php echo $category['category_name']; ?></a>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                    <?php if (count($coaching['categories']) > 3): ?>
                                        <span class="category-badge">+<?php echo count($coaching['categories']) - 3; ?> more</span>
                                    <?php endif; ?>
                                </div>
                                <a href="<?php echo getCoachingUrl($coaching['slug']); ?>" class="btn btn-outline-primary btn-sm mt-3">View Details</a>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="col-12 text-center">
                        <p>No featured coaching centers available yet. Please check back later.</p>
                    </div>
                <?php endif; ?>
            </div>
            
            <div class="text-center mt-4">
                <a href="search.php?featured=1" class="btn btn-primary">View All Featured</a>
            </div>
        </div>
    </section>
    
    <!-- Top Rated Coaching Centers Section -->
    <section class="top-rated-section section-padding">
        <div class="container">
            <div class="section-header text-center">
                <h2>Top Rated Coaching Centers</h2>
                <p>Highest rated coaching centers by our users</p>
            </div>
            
            <div class="row">
                <?php if (!empty($topRatedCoachings)): ?>
                    <?php foreach ($topRatedCoachings as $coaching): ?>
                    <div class="col-lg-4 col-md-6">
                        <div class="coaching-card">
                            <div class="coaching-image">
                                <img src="<?php echo isset($coaching['logo']) ? getUploadUrl($coaching['logo']) : getAssetUrl('images/default-logo.png'); ?>" alt="<?php echo $coaching['coaching_name']; ?>">
                                <span class="rating-badge"><i class="fas fa-star"></i> <?php echo number_format($coaching['avg_rating'], 1); ?></span>
                            </div>
                            <div class="coaching-content">
                                <h3><a href="<?php echo getCoachingUrl($coaching['slug']); ?>"><?php echo $coaching['coaching_name']; ?></a></h3>
                                <div class="coaching-meta">
                                    <div class="location">
                                        <i class="fas fa-map-marker-alt"></i>
                                        <?php echo $coaching['city_name']; ?>, <?php echo $coaching['state_name']; ?>
                                    </div>
                                    <div class="rating">
                                        <?php echo getStarRating($coaching['avg_rating']); ?>
                                        <span class="reviews">(<?php echo $coaching['total_reviews']; ?> reviews)</span>
                                    </div>
                                </div>
                                <div class="coaching-categories">
                                    <?php foreach ($coaching['categories'] as $index => $category): ?>
                                        <?php if ($index < 3): ?>
                                            <a href="<?php echo getCategoryUrl($category['category_slug']); ?>" class="category-badge"><?php echo $category['category_name']; ?></a>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                    <?php if (count($coaching['categories']) > 3): ?>
                                        <span class="category-badge">+<?php echo count($coaching['categories']) - 3; ?> more</span>
                                    <?php endif; ?>
                                </div>
                                <a href="<?php echo getCoachingUrl($coaching['slug']); ?>" class="btn btn-outline-primary btn-sm mt-3">View Details</a>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="col-12 text-center">
                        <p>No top rated coaching centers available yet. Please check back later.</p>
                    </div>
                <?php endif; ?>
            </div>
            
            <div class="text-center mt-4">
                <a href="search.php?sort=rating_desc" class="btn btn-primary">View All Top Rated</a>
            </div>
        </div>
    </section>
    
    <!-- Popular Cities Section -->
    <section class="cities-section section-padding bg-light">
        <div class="container">
            <div class="section-header text-center">
                <h2>Popular Cities</h2>
                <p>Find coaching centers in your city</p>
            </div>
            
            <div class="row">
                <?php if (!empty($popularCities)): ?>
                    <?php foreach ($popularCities as $city): ?>
                    <div class="col-lg-4 col-md-6">
                        <div class="city-card">
                            <div class="city-image">
                                <img src="<?php echo getAssetUrl('images/cities/' . Utility::generateSlug($city['city_name']) . '.jpg'); ?>" alt="<?php echo $city['city_name']; ?>" onerror="this.src='<?php echo getAssetUrl('images/city-default.jpg'); ?>'">
                            </div>
                            <div class="city-content">
                                <h3><?php echo $city['city_name']; ?></h3>
                                <p><?php echo $city['coaching_count']; ?> Coaching Centers</p>
                                <a href="<?php echo getCityUrl($city['slug']); ?>" class="btn btn-outline-primary btn-sm">View All</a>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="col-12 text-center">
                        <p>No popular cities available yet. Please check back later.</p>
                    </div>
                <?php endif; ?>
            </div>
            
            <div class="text-center mt-4">
                <a href="cities.php" class="btn btn-primary">View All Cities</a>
            </div>
        </div>
    </section>
    
    <!-- How It Works Section -->
    <section class="how-it-works-section section-padding">
        <div class="container">
            <div class="section-header text-center">
                <h2>How It Works</h2>
                <p>Find the perfect coaching center in 3 simple steps</p>
            </div>
            
            <div class="row">
                <div class="col-lg-4">
                    <div class="step-card">
                        <div class="step-icon">
                            <i class="fas fa-search"></i>
                            <span class="step-number">1</span>
                        </div>
                        <div class="step-content">
                            <h3>Search</h3>
                            <p>Search for coaching centers by location, category, or name to find the perfect match for your needs.</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="step-card">
                        <div class="step-icon">
                            <i class="fas fa-star"></i>
                            <span class="step-number">2</span>
                        </div>
                        <div class="step-content">
                            <h3>Compare</h3>
                            <p>Compare coaching centers based on ratings, reviews, facilities, success stories, and more.</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="step-card">
                        <div class="step-icon">
                            <i class="fas fa-check-circle"></i>
                            <span class="step-number">3</span>
                        </div>
                        <div class="step-content">
                            <h3>Choose</h3>
                            <p>Make an informed decision and choose the best coaching center for your educational journey.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Recent Blog Posts Section -->
    <section class="blog-section section-padding bg-light">
        <div class="container">
            <div class="section-header text-center">
                <h2>Latest Articles</h2>
                <p>Read our latest articles and tips</p>
            </div>
            
            <div class="row">
                <?php if (!empty($recentPosts)): ?>
                    <?php foreach ($recentPosts as $post): ?>
                    <div class="col-lg-4 col-md-6">
                        <div class="blog-card">
                            <div class="blog-image">
                                <?php if (!empty($post['featured_image'])): ?>
                                    <img src="<?php echo getUploadUrl($post['featured_image']); ?>" alt="<?php echo $post['title']; ?>">
                                <?php else: ?>
                                    <img src="<?php echo getAssetUrl('images/blog-default.jpg'); ?>" alt="<?php echo $post['title']; ?>">
                                <?php endif; ?>
                                <?php if (!empty($post['category_name']) && isset($post['category_slug'])): ?>
                                    <a href="<?php echo getBlogCategoryUrl($post['category_slug']); ?>" class="category"><?php echo $post['category_name']; ?></a>
                                <?php elseif (!empty($post['category_name'])): ?>
                                    <span class="category"><?php echo $post['category_name']; ?></span>
                                <?php endif; ?>
                            </div>
                            <div class="blog-content">
                                <div class="blog-meta">
                                    <span><i class="far fa-calendar"></i> <?php echo formatDate($post['published_at']); ?></span>
                                    <span><i class="far fa-eye"></i> <?php echo isset($post['view_count']) ? $post['view_count'] : 0; ?> views</span>
                                </div>
                                <h3><a href="<?php echo getBlogPostUrl($post['slug']); ?>"><?php echo $post['title']; ?></a></h3>
                                <p><?php echo truncate($post['excerpt'] ?? strip_tags($post['content']), 120); ?></p>
                                <a href="<?php echo getBlogPostUrl($post['slug']); ?>" class="read-more">Read More <i class="fas fa-arrow-right"></i></a>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="col-12 text-center">
                        <p>No blog posts available yet. Please check back later.</p>
                    </div>
                <?php endif; ?>
            </div>
            
            <div class="text-center mt-4">
                <a href="blog.php" class="btn btn-primary">View All Articles</a>
            </div>
        </div>
    </section>
    
    <!-- Call to Action Section -->
    <section class="cta-section section-padding">
        <div class="container">
            <div class="cta-content text-center">
                <h2>Are You a Coaching Center Owner?</h2>
                <p>List your coaching center on our platform and reach thousands of potential students.</p>
                <div class="cta-buttons">
                    <a href="register.php?type=coaching_owner" class="btn btn-primary">Register Now</a>
                    <a href="contact.php" class="btn btn-outline-primary">Contact Us</a>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Footer -->
    <?php include 'templates/footer.php'; ?>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="<?php echo getAssetUrl('js/main.js'); ?>"></script>
</body>
</html>