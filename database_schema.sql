-- Database Schema for Coaching Centers Listing Website

-- Create database
CREATE DATABASE IF NOT EXISTS coaching_directory;
USE coaching_directory;

-- Users table (for website users)
CREATE TABLE IF NOT EXISTS users (
    user_id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(50),
    last_name <PERSON><PERSON><PERSON><PERSON>(50),
    phone VARCHAR(20),
    profile_image VARCHAR(255),
    user_type ENUM('admin', 'user', 'coaching_owner') NOT NULL DEFAULT 'user',
    is_verified BOOLEAN DEFAULT FALSE,
    verification_token VARCHAR(255),
    reset_token VARCHAR(255),
    reset_token_expiry DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login DATETIME,
    status ENUM('active', 'inactive', 'banned') DEFAULT 'active'
);

-- States table
CREATE TABLE IF NOT EXISTS states (
    state_id INT AUTO_INCREMENT PRIMARY KEY,
    state_name VARCHAR(100) NOT NULL,
    state_code VARCHAR(10),
    country_id INT DEFAULT 1, -- Assuming India is country_id 1
    status ENUM('active', 'inactive') DEFAULT 'active'
);

-- Cities table
CREATE TABLE IF NOT EXISTS cities (
    city_id INT AUTO_INCREMENT PRIMARY KEY,
    city_name VARCHAR(100) NOT NULL,
    state_id INT,
    status ENUM('active', 'inactive') DEFAULT 'active',
    FOREIGN KEY (state_id) REFERENCES states(state_id) ON DELETE SET NULL
);

-- Locations/Areas within cities
CREATE TABLE IF NOT EXISTS locations (
    location_id INT AUTO_INCREMENT PRIMARY KEY,
    location_name VARCHAR(100) NOT NULL,
    city_id INT,
    pincode VARCHAR(20),
    status ENUM('active', 'inactive') DEFAULT 'active',
    FOREIGN KEY (city_id) REFERENCES cities(city_id) ON DELETE SET NULL
);

-- Course categories (IIT JEE, NEET, UPSC, etc.)
CREATE TABLE IF NOT EXISTS course_categories (
    category_id INT AUTO_INCREMENT PRIMARY KEY,
    category_name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    parent_id INT DEFAULT NULL,
    meta_title VARCHAR(255),
    meta_description TEXT,
    meta_keywords VARCHAR(255),
    icon VARCHAR(255),
    display_order INT DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES course_categories(category_id) ON DELETE SET NULL
);

-- Coaching centers table
CREATE TABLE IF NOT EXISTS coaching_centers (
    coaching_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    coaching_name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    logo VARCHAR(255),
    banner_image VARCHAR(255),
    established_year YEAR,
    website VARCHAR(255),
    email VARCHAR(100),
    phone VARCHAR(20),
    whatsapp VARCHAR(20),
    address TEXT,
    location_id INT,
    city_id INT,
    state_id INT,
    pincode VARCHAR(20),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    working_hours TEXT,
    is_featured BOOLEAN DEFAULT FALSE,
    is_verified BOOLEAN DEFAULT FALSE,
    verification_date DATETIME,
    avg_rating DECIMAL(3, 2) DEFAULT 0.00,
    total_reviews INT DEFAULT 0,
    total_views INT DEFAULT 0,
    meta_title VARCHAR(255),
    meta_description TEXT,
    meta_keywords VARCHAR(255),
    status ENUM('active', 'inactive', 'pending', 'rejected') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL,
    FOREIGN KEY (location_id) REFERENCES locations(location_id) ON DELETE SET NULL,
    FOREIGN KEY (city_id) REFERENCES cities(city_id) ON DELETE SET NULL,
    FOREIGN KEY (state_id) REFERENCES states(state_id) ON DELETE SET NULL
);

-- Coaching center categories (mapping table)
CREATE TABLE IF NOT EXISTS coaching_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    coaching_id INT,
    category_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (coaching_id) REFERENCES coaching_centers(coaching_id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES course_categories(category_id) ON DELETE CASCADE
);

-- Coaching center images
CREATE TABLE IF NOT EXISTS coaching_images (
    image_id INT AUTO_INCREMENT PRIMARY KEY,
    coaching_id INT,
    image_path VARCHAR(255) NOT NULL,
    caption VARCHAR(255),
    display_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (coaching_id) REFERENCES coaching_centers(coaching_id) ON DELETE CASCADE
);

-- Coaching center facilities
CREATE TABLE IF NOT EXISTS facilities (
    facility_id INT AUTO_INCREMENT PRIMARY KEY,
    facility_name VARCHAR(100) NOT NULL,
    icon VARCHAR(255),
    status ENUM('active', 'inactive') DEFAULT 'active'
);

-- Coaching center facilities mapping
CREATE TABLE IF NOT EXISTS coaching_facilities (
    id INT AUTO_INCREMENT PRIMARY KEY,
    coaching_id INT,
    facility_id INT,
    FOREIGN KEY (coaching_id) REFERENCES coaching_centers(coaching_id) ON DELETE CASCADE,
    FOREIGN KEY (facility_id) REFERENCES facilities(facility_id) ON DELETE CASCADE
);

-- Coaching center locations mapping
CREATE TABLE IF NOT EXISTS coaching_locations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    coaching_id INT NOT NULL,
    location_id INT NOT NULL,
    FOREIGN KEY (coaching_id) REFERENCES coaching_centers(coaching_id) ON DELETE CASCADE,
    FOREIGN KEY (location_id) REFERENCES locations(location_id) ON DELETE CASCADE
);


-- Courses offered by coaching centers
CREATE TABLE IF NOT EXISTS courses (
    course_id INT AUTO_INCREMENT PRIMARY KEY,
    coaching_id INT,
    category_id INT,
    course_name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    description TEXT,
    duration VARCHAR(100),
    fee_structure TEXT,
    batch_size INT,
    batch_timings TEXT,
    study_material TEXT,
    faculty_details TEXT,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (coaching_id) REFERENCES coaching_centers(coaching_id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES course_categories(category_id) ON DELETE SET NULL,
    UNIQUE KEY (coaching_id, slug)
);

-- Course locations mapping
CREATE TABLE IF NOT EXISTS course_locations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    location_id INT NOT NULL,
    FOREIGN KEY (course_id) REFERENCES courses(course_id) ON DELETE CASCADE,
    FOREIGN KEY (location_id) REFERENCES locations(location_id) ON DELETE CASCADE,
    UNIQUE KEY (course_id, location_id)
);


-- Success stories/Results
CREATE TABLE IF NOT EXISTS success_stories (
    story_id INT AUTO_INCREMENT PRIMARY KEY,
    coaching_id INT,
    course_id INT,
    student_name VARCHAR(100) NOT NULL,
    student_image VARCHAR(255),
    achievement TEXT NOT NULL,
    rank_achieved VARCHAR(50),
    year YEAR,
    testimonial TEXT,
    is_featured BOOLEAN DEFAULT FALSE,
    display_order INT DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (coaching_id) REFERENCES coaching_centers(coaching_id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(course_id) ON DELETE SET NULL
);

-- Reviews table
CREATE TABLE IF NOT EXISTS reviews (
    review_id INT AUTO_INCREMENT PRIMARY KEY,
    coaching_id INT,
    user_id INT,
    rating DECIMAL(3, 2) NOT NULL,
    title VARCHAR(255),
    review_text TEXT,
    pros TEXT,
    cons TEXT,
    is_anonymous BOOLEAN DEFAULT FALSE,
    is_verified BOOLEAN DEFAULT FALSE,
    helpful_count INT DEFAULT 0,
    report_count INT DEFAULT 0,
    admin_response TEXT,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (coaching_id) REFERENCES coaching_centers(coaching_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL
);

-- Review helpful votes
CREATE TABLE IF NOT EXISTS review_votes (
    vote_id INT AUTO_INCREMENT PRIMARY KEY,
    review_id INT,
    user_id INT,
    vote_type ENUM('helpful', 'report') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (review_id) REFERENCES reviews(review_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    UNIQUE KEY (review_id, user_id, vote_type)
);

-- Coaching center subscription plans
CREATE TABLE IF NOT EXISTS subscription_plans (
    plan_id INT AUTO_INCREMENT PRIMARY KEY,
    plan_name VARCHAR(100) NOT NULL,
    description TEXT,
    duration INT NOT NULL, -- in days
    price DECIMAL(10, 2) NOT NULL,
    features TEXT,
    is_featured BOOLEAN DEFAULT FALSE,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Coaching center subscriptions
CREATE TABLE IF NOT EXISTS coaching_subscriptions (
    subscription_id INT AUTO_INCREMENT PRIMARY KEY,
    coaching_id INT,
    plan_id INT,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    payment_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    payment_method VARCHAR(50),
    transaction_id VARCHAR(255),
    amount_paid DECIMAL(10, 2),
    is_active BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (coaching_id) REFERENCES coaching_centers(coaching_id) ON DELETE CASCADE,
    FOREIGN KEY (plan_id) REFERENCES subscription_plans(plan_id) ON DELETE SET NULL
);

-- User saved/favorite coaching centers
CREATE TABLE IF NOT EXISTS user_favorites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    coaching_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (coaching_id) REFERENCES coaching_centers(coaching_id) ON DELETE CASCADE,
    UNIQUE KEY (user_id, coaching_id)
);

-- User inquiries to coaching centers
CREATE TABLE IF NOT EXISTS inquiries (
    inquiry_id INT AUTO_INCREMENT PRIMARY KEY,
    coaching_id INT,
    user_id INT,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    subject VARCHAR(255),
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    status ENUM('pending', 'responded', 'closed') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (coaching_id) REFERENCES coaching_centers(coaching_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL
);

-- Blog posts for SEO
CREATE TABLE IF NOT EXISTS blog_posts (
    post_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL UNIQUE,
    content TEXT NOT NULL,
    excerpt TEXT,
    featured_image VARCHAR(255),
    category_id INT, -- Can reference course_categories
    view_count INT DEFAULT 0,
    meta_title VARCHAR(255),
    meta_description TEXT,
    meta_keywords VARCHAR(255),
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    published_at DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL,
    FOREIGN KEY (category_id) REFERENCES course_categories(category_id) ON DELETE SET NULL
);

-- Blog post comments
CREATE TABLE IF NOT EXISTS blog_comments (
    comment_id INT AUTO_INCREMENT PRIMARY KEY,
    post_id INT,
    user_id INT,
    parent_id INT DEFAULT NULL,
    comment TEXT NOT NULL,
    is_approved BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (post_id) REFERENCES blog_posts(post_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL,
    FOREIGN KEY (parent_id) REFERENCES blog_comments(comment_id) ON DELETE SET NULL
);

-- FAQ categories
CREATE TABLE IF NOT EXISTS faq_categories (
    category_id INT AUTO_INCREMENT PRIMARY KEY,
    category_name VARCHAR(100) NOT NULL,
    display_order INT DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active'
);

-- FAQs
CREATE TABLE IF NOT EXISTS faqs (
    faq_id INT AUTO_INCREMENT PRIMARY KEY,
    category_id INT,
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    display_order INT DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES faq_categories(category_id) ON DELETE SET NULL
);

-- Website settings
CREATE TABLE IF NOT EXISTS settings (
    setting_id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    setting_group VARCHAR(50) DEFAULT 'general',
    is_public BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- SEO pages (for static pages with SEO optimization)
CREATE TABLE IF NOT EXISTS seo_pages (
    page_id INT AUTO_INCREMENT PRIMARY KEY,
    page_title VARCHAR(255) NOT NULL,
    page_slug VARCHAR(255) NOT NULL UNIQUE,
    content TEXT,
    meta_title VARCHAR(255),
    meta_description TEXT,
    meta_keywords VARCHAR(255),
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Contact messages
CREATE TABLE IF NOT EXISTS contact_messages (
    message_id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    subject VARCHAR(255),
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    status ENUM('pending', 'responded', 'closed') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Notifications
CREATE TABLE IF NOT EXISTS notifications (
    notification_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    link VARCHAR(255),
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- User activity logs
CREATE TABLE IF NOT EXISTS activity_logs (
    log_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    action VARCHAR(255) NOT NULL,
    entity_type VARCHAR(50),
    entity_id INT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL
);

-- Insert initial admin user
INSERT INTO users (username, email, password, first_name, last_name, user_type, is_verified, status)
VALUES ('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin', 'User', 'admin', TRUE, 'active');

-- Insert some initial settings
INSERT INTO settings (setting_key, setting_value, setting_group) VALUES
('site_name', 'Coaching Directory - Find the Best Coaching Centers in India', 'general'),
('site_description', 'India\'s largest directory of coaching centers for IIT JEE, NEET, UPSC and more. Find the best coaching centers in your city.', 'general'),
('site_logo', 'assets/images/logo.png', 'general'),
('site_favicon', 'assets/images/favicon.ico', 'general'),
('contact_email', '<EMAIL>', 'contact'),
('contact_phone', '+91 9876543210', 'contact'),
('contact_address', 'New Delhi, India', 'contact'),
('social_facebook', 'https://facebook.com/coachingdirectory', 'social'),
('social_twitter', 'https://twitter.com/coachingdirectory', 'social'),
('social_instagram', 'https://instagram.com/coachingdirectory', 'social'),
('social_linkedin', 'https://linkedin.com/company/coachingdirectory', 'social'),
('footer_text', '© 2023 Coaching Directory. All rights reserved.', 'general'),
('google_analytics', '', 'seo'),
('meta_title', 'Coaching Directory - Find the Best Coaching Centers in India', 'seo'),
('meta_description', 'India\'s largest directory of coaching centers for IIT JEE, NEET, UPSC and more. Find the best coaching centers in your city.', 'seo'),
('meta_keywords', 'coaching centers, IIT JEE coaching, NEET coaching, UPSC coaching, best coaching in India', 'seo');