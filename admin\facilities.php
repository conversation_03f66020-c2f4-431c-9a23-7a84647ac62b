<?php
/**
 * Admin Facilities Management
 */
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    require_once '../includes/autoload.php';
    
    // Check user authentication
    $user = new User();
    if (!$user->isLoggedIn() || !$user->isAdmin()) {
        header('Location: login.php');
        exit;
    }
    
    // Get admin info
    $adminInfo = $user->getUserData();
    
    // Initialize settings
    $settings = Settings::getInstance();
    
    // Initialize facility object
    $facilityObj = new Facility();
    
    // Handle actions
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    $facilityId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    $message = '';
    
    // Handle delete action
    if ($action === 'delete' && $facilityId > 0) {
        if ($facilityObj->delete($facilityId)) {
            $message = '<div class="alert alert-success">Facility deleted successfully.</div>';
        } else {
            $message = '<div class="alert alert-danger">Failed to delete facility.</div>';
        }
    }
    
    // Handle form submission for add/edit
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $formData = [
            'facility_name' => trim($_POST['facility_name'] ?? ''),
            'icon' => trim($_POST['icon'] ?? ''),
            'status' => $_POST['status'] ?? 'active'
        ];
        
        // Validate form data
        $errors = [];
        if (empty($formData['facility_name'])) {
            $errors[] = 'Facility name is required.';
        }
        
        if (empty($errors)) {
            if (isset($_POST['facility_id']) && $_POST['facility_id'] > 0) {
                // Update existing facility
                $facilityId = (int)$_POST['facility_id'];
                if ($facilityObj->update($facilityId, $formData)) {
                    $message = '<div class="alert alert-success">Facility updated successfully.</div>';
                } else {
                    $message = '<div class="alert alert-danger">Failed to update facility.</div>';
                }
            } else {
                // Add new facility
                if ($facilityObj->create($formData)) {
                    $message = '<div class="alert alert-success">Facility added successfully.</div>';
                    // Clear form data after successful submission
                    $formData = [
                        'facility_name' => '',
                        'icon' => '',
                        'status' => 'active'
                    ];
                } else {
                    $message = '<div class="alert alert-danger">Failed to add facility.</div>';
                }
            }
        } else {
            $message = '<div class="alert alert-danger"><ul>';
            foreach ($errors as $error) {
                $message .= '<li>' . htmlspecialchars($error) . '</li>';
            }
            $message .= '</ul></div>';
        }
    }
    
    // Get facility for editing
    $editFacility = null;
    if ($action === 'edit' && $facilityId > 0) {
        $editFacility = $facilityObj->getById($facilityId);
        if ($editFacility) {
            $formData = $editFacility;
        }
    }
    
    // Get facilities with pagination
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = 10;
    $search = isset($_GET['search']) ? $_GET['search'] : '';
    
    // Prepare filters
    $filters = [];
    if (!empty($search)) {
        $filters['search'] = $search;
    }
    
    // Get facilities
    $result = $facilityObj->getAll($filters, $page, $limit);
    $facilities = $result['facilities'] ?? [];
    $totalPages = $result['total_pages'] ?? 1;
    $totalCount = $result['total_count'] ?? 0;
    
} catch (Exception $e) {
    die('<div style="color:red;padding:20px;border:2px solid red;margin:20px;">'
        . '<h2>Error</h2>'
        . '<p><strong>Message:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>'
        . '<p><strong>File:</strong> ' . htmlspecialchars($e->getFile()) . '</p>'
        . '<p><strong>Line:</strong> ' . htmlspecialchars($e->getLine()) . '</p>'
        . '</div>');
}

// Page title
$pageTitle = 'Facilities Management';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Admin Panel | <?php echo $settings->getSiteName(); ?></title>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo getAssetUrl($settings->getSiteFavicon()); ?>" type="image/x-icon">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Admin CSS -->
    <link rel="stylesheet" href="assets/css/admin.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <?php include 'templates/sidebar.php'; ?>
        
        <!-- Main Content -->
        <div class="admin-main" id="adminMain">
            <!-- Header -->
            <?php include 'templates/header.php'; ?>
            
            <!-- Content -->
            <div class="admin-content">
                <div class="page-title">
                    <div>
                        <h1><?php echo $pageTitle; ?></h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="dashboard.php">Home</a></li>
                                <li class="breadcrumb-item active" aria-current="page"><?php echo $pageTitle; ?></li>
                            </ol>
                        </nav>
                    </div>
                </div>
                
                <?php echo $message; ?>
                
                <!-- Add/Edit Facility Form -->
                <div class="admin-card mb-4">
                    <div class="admin-card-header">
                        <h2 class="admin-card-title">
                            <i class="fas fa-plus-circle me-2"></i> <?php echo $editFacility ? 'Edit Facility' : 'Add New Facility'; ?>
                        </h2>
                    </div>
                    <div class="admin-card-body">
                        <form action="" method="post" class="row g-3">
                            <?php if ($editFacility): ?>
                                <input type="hidden" name="facility_id" value="<?php echo $editFacility['facility_id']; ?>">
                            <?php endif; ?>
                            
                            <div class="col-md-6">
                                <label for="facility_name" class="form-label">Facility Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="facility_name" name="facility_name" value="<?php echo htmlspecialchars($formData['facility_name'] ?? ''); ?>" required>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="icon" class="form-label">Icon Class (Font Awesome)</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-icons"></i></span>
                                    <input type="text" class="form-control" id="icon" name="icon" value="<?php echo htmlspecialchars($formData['icon'] ?? 'fas fa-check-circle'); ?>" placeholder="fas fa-check-circle">
                                </div>
                                <small class="text-muted">Enter a Font Awesome icon class (e.g., fas fa-wifi, fas fa-book, etc.)</small>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="active" <?php echo (isset($formData['status']) && $formData['status'] === 'active') ? 'selected' : ''; ?>>Active</option>
                                    <option value="inactive" <?php echo (isset($formData['status']) && $formData['status'] === 'inactive') ? 'selected' : ''; ?>>Inactive</option>
                                </select>
                            </div>
                            
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i> <?php echo $editFacility ? 'Update Facility' : 'Add Facility'; ?>
                                </button>
                                <?php if ($editFacility): ?>
                                    <a href="facilities.php" class="btn btn-secondary ms-2">
                                        <i class="fas fa-times me-1"></i> Cancel
                                    </a>
                                <?php endif; ?>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Filters -->
                <div class="admin-card mb-4">
                    <div class="admin-card-body">
                        <form action="" method="get" class="row g-3">
                            <div class="col-md-6">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" class="form-control" id="search" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="Search by name...">
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <a href="facilities.php" class="btn btn-secondary w-100">
                                    <i class="fas fa-sync-alt"></i> Reset
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Facilities Table -->
                <div class="admin-card">
                    <div class="admin-card-header">
                        <h2 class="admin-card-title">
                            <i class="fas fa-list me-2"></i> All Facilities
                        </h2>
                        <span class="badge bg-primary"><?php echo $totalCount; ?> Total</span>
                    </div>
                    <div class="admin-card-body p-0">
                        <div class="table-responsive">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Icon</th>
                                        <th>Name</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($facilities)): ?>
                                        <?php foreach ($facilities as $facility): ?>
                                            <tr>
                                                <td><?php echo $facility['facility_id']; ?></td>
                                                <td>
                                                    <i class="<?php echo !empty($facility['icon']) ? htmlspecialchars($facility['icon']) : 'fas fa-check-circle'; ?>"></i>
                                                </td>
                                                <td><?php echo htmlspecialchars($facility['facility_name']); ?></td>
                                                <td>
                                                    <?php if ($facility['status'] === 'active'): ?>
                                                        <span class="badge bg-success">Active</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">Inactive</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group">
                                                        <a href="facilities.php?action=edit&id=<?php echo $facility['facility_id']; ?>" class="btn btn-sm btn-primary" title="Edit">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <a href="facilities.php?action=delete&id=<?php echo $facility['facility_id']; ?>" class="btn btn-sm btn-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this facility? This action cannot be undone.');">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="5" class="text-center">No facilities found.</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <div class="admin-card-footer">
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center mb-0">
                                    <?php if ($page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="facilities.php?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>" aria-label="Previous">
                                                <span aria-hidden="true">&laquo;</span>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                    
                                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                            <a class="page-link" href="facilities.php?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>">
                                                <?php echo $i; ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>
                                    
                                    <?php if ($page < $totalPages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="facilities.php?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>" aria-label="Next">
                                                <span aria-hidden="true">&raquo;</span>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Footer -->
            <footer class="admin-footer">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-md-6">
                            <p class="mb-0">&copy; <?php echo date('Y'); ?> <?php echo $settings->getSiteName(); ?>. All rights reserved.</p>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <p class="mb-0">Version 1.0</p>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Admin JS -->
    <script src="assets/js/admin.js"></script>
    
    <!-- Icon Preview Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const iconInput = document.getElementById('icon');
            const iconPreview = document.querySelector('.input-group-text i');
            
            iconInput.addEventListener('input', function() {
                const iconClass = this.value.trim();
                if (iconClass) {
                    iconPreview.className = iconClass;
                } else {
                    iconPreview.className = 'fas fa-icons';
                }
            });
        });
    </script>
</body>
</html>