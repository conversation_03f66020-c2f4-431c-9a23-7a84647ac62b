<?php
/**
 * Login Page
 */
require_once 'includes/autoload.php';

// Get settings
$settings = Settings::getInstance();

// Check if user is already logged in
if ($user->isLoggedIn()) {
    if ($user->isAdmin()) {
        redirect('admin/dashboard.php');
    } elseif ($user->isCoachingOwner()) {
        redirect('coaching-panel/dashboard.php');
    } else {
        redirect('index.php');
    }
}

// Get redirect URL
$redirect = isset($_GET['redirect']) ? $_GET['redirect'] : '';

// Process login form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (checkCSRFToken()) {
        $username = $_POST['username'];
        $password = $_POST['password'];
        $remember = isset($_POST['remember']) ? true : false;
        
        // Validate form data
        $errors = [];
        
        if (empty($username)) {
            $errors['username'] = 'Username or email is required';
        }
        
        if (empty($password)) {
            $errors['password'] = 'Password is required';
        }
        
        if (empty($errors)) {
            // Log login attempt
            error_log(date('Y-m-d H:i:s') . " - Login attempt from login.php for username: $username\n", 3, BASE_PATH . '/logs/login.log');
            
            // Attempt to login
            if ($user->login($username, $password, $remember)) {
                // Log successful login
                error_log(date('Y-m-d H:i:s') . " - Login successful for username: $username\n", 3, BASE_PATH . '/logs/login.log');
                
                // Redirect to appropriate page
                if (!empty($redirect)) {
                    redirect($redirect);
                } elseif ($user->isAdmin()) {
                    redirect('admin/dashboard.php');
                } elseif ($user->isCoachingOwner()) {
                    // Get coaching center ID for this owner
                    $coachingCenter = new CoachingCenter();
                    $coachingCenters = $coachingCenter->getByUser($user->getUserData('user_id'));
                    if (!empty($coachingCenters)) {
                        $_SESSION['coaching_id'] = $coachingCenters[0]['coaching_id'];
                    }
                    redirect('coaching-panel/dashboard.php');
                } else {
                    redirect('index.php');
                }
            } else {
                // Log failed login
                error_log(date('Y-m-d H:i:s') . " - Login failed for username: $username\n", 3, BASE_PATH . '/logs/login.log');
                setFlashMessage('Invalid username/email or password', 'danger');
            }
        } else {
            setValidationErrors($errors);
        }
    } else {
        setFlashMessage('Invalid form submission. Please try again.', 'danger');
    }
}

// Page title and meta
$pageTitle = 'Login';
$pageDescription = 'Login to your account to access your dashboard, save favorite coaching centers, write reviews, and more.';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?php echo getMetaTags($pageTitle . ' - ' . $settings->getSiteName(), $pageDescription); ?>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo getAssetUrl($settings->getSiteFavicon()); ?>" type="image/x-icon">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo getAssetUrl('css/style.css'); ?>">
</head>
<body>
    <!-- Header -->
    <?php include 'templates/header.php'; ?>
    
    <!-- Breadcrumb -->
    <section class="breadcrumb-section">
        <div class="container">
            <?php
            $breadcrumbItems = [
                ['title' => 'Home', 'url' => getBaseUrl()],
                ['title' => 'Login', 'url' => '']
            ];
            echo getBreadcrumb($breadcrumbItems);
            ?>
        </div>
    </section>
    
    <!-- Login Section -->
    <section class="login-section section-padding">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-5 col-md-8">
                    <div class="auth-form-container">
                        <div class="auth-header text-center">
                            <h2>Login to Your Account</h2>
                            <p>Welcome back! Please login to access your account.</p>
                        </div>
                        
                        <form action="login.php<?php echo !empty($redirect) ? '?redirect=' . urlencode($redirect) : ''; ?>" method="POST" class="auth-form">
                            <?php echo getCSRFTokenField(); ?>
                            
                            <div class="form-group mb-3">
                                <label for="username">Username or Email</label>
                                <input type="text" id="username" name="username" class="form-control <?php echo hasValidationError('username') ? 'is-invalid' : ''; ?>" value="<?php echo getOldFormValue('username'); ?>" required>
                                <?php if (hasValidationError('username')): ?>
                                    <div class="invalid-feedback"><?php echo getValidationError('username'); ?></div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="form-group mb-3">
                                <label for="password">Password</label>
                                <div class="password-field">
                                    <input type="password" id="password" name="password" class="form-control <?php echo hasValidationError('password') ? 'is-invalid' : ''; ?>" required>
                                    <span class="password-toggle"><i class="fas fa-eye"></i></span>
                                    <?php if (hasValidationError('password')): ?>
                                        <div class="invalid-feedback"><?php echo getValidationError('password'); ?></div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <div class="form-group mb-3 d-flex justify-content-between align-items-center">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="remember" name="remember">
                                    <label class="form-check-label" for="remember">Remember Me</label>
                                </div>
                                <a href="forgot-password.php" class="forgot-password">Forgot Password?</a>
                            </div>
                            
                            <div class="form-group mb-3">
                                <button type="submit" class="btn btn-primary w-100">Login</button>
                            </div>
                            
                            <div class="auth-footer text-center">
                                <p>Don't have an account? <a href="register.php<?php echo !empty($redirect) ? '?redirect=' . urlencode($redirect) : ''; ?>">Register Now</a></p>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Footer -->
    <?php include 'templates/footer.php'; ?>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="<?php echo getAssetUrl('js/main.js'); ?>"></script>
</body>
</html>
