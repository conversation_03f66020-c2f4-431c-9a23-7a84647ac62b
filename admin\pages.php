<?php
/**
 * Admin Pages
 */
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    require_once '../includes/autoload.php';
    
    // Check user authentication
    $user = new User();
    if (!$user->isLoggedIn() || !$user->isAdmin()) {
        header('Location: login.php');
        exit;
    }
    
    // Get admin info
    $adminInfo = $user->getUserData();
    
    // Initialize settings
    $settings = Settings::getInstance();
    
    // Initialize page object
    $pageObj = new Page();
    
    // Handle actions
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    $pageId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    $message = '';
    
    if ($action === 'delete' && $pageId > 0) {
        if ($pageObj->delete($pageId)) {
            $message = '<div class="alert alert-success">Page deleted successfully.</div>';
        } else {
            $message = '<div class="alert alert-danger">Failed to delete page.</div>';
        }
    } else if ($action === 'publish' && $pageId > 0) {
        if ($pageObj->update($pageId, ['status' => 'published'])) {
            $message = '<div class="alert alert-success">Page published successfully.</div>';
        } else {
            $message = '<div class="alert alert-danger">Failed to publish page.</div>';
        }
    } else if ($action === 'draft' && $pageId > 0) {
        if ($pageObj->update($pageId, ['status' => 'draft'])) {
            $message = '<div class="alert alert-success">Page moved to draft successfully.</div>';
        } else {
            $message = '<div class="alert alert-danger">Failed to update page status.</div>';
        }
    }
    
    // Get pages with pagination
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = 10;
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    $search = isset($_GET['search']) ? $_GET['search'] : '';
    
    // Prepare filters
    $filters = [];
    if (!empty($status)) {
        $filters['status'] = $status;
    }
    if (!empty($search)) {
        $filters['search'] = $search;
    }
    
    // Get pages
    $result = $pageObj->getAll($filters, $page, $limit);
    $pages = $result['pages'] ?? [];
    $totalPages = $result['total_pages'] ?? 1;
    $totalCount = $result['total_count'] ?? 0;
    
} catch (Exception $e) {
    die('<div style="color:red;padding:20px;border:2px solid red;margin:20px;">'
        . '<h2>Error</h2>'
        . '<p><strong>Message:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>'
        . '<p><strong>File:</strong> ' . htmlspecialchars($e->getFile()) . '</p>'
        . '<p><strong>Line:</strong> ' . htmlspecialchars($e->getLine()) . '</p>'
        . '</div>');
}

// Page title
$pageTitle = 'Pages';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Admin Panel | <?php echo $settings->getSiteName(); ?></title>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo getAssetUrl($settings->getSiteFavicon()); ?>" type="image/x-icon">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Admin CSS -->
    <link rel="stylesheet" href="assets/css/admin.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <?php include 'templates/sidebar.php'; ?>
        
        <!-- Main Content -->
        <div class="admin-main" id="adminMain">
            <!-- Header -->
            <?php include 'templates/header.php'; ?>
            
            <!-- Content -->
            <div class="admin-content">
                <div class="page-title">
                    <div>
                        <h1><?php echo $pageTitle; ?></h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="dashboard.php">Home</a></li>
                                <li class="breadcrumb-item">Content</li>
                                <li class="breadcrumb-item active" aria-current="page"><?php echo $pageTitle; ?></li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <a href="page-add.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add New Page
                        </a>
                    </div>
                </div>
                
                <?php echo $message; ?>
                
                <!-- Filters -->
                <div class="admin-card mb-4">
                    <div class="admin-card-body">
                        <form action="" method="get" class="row g-3">
                            <div class="col-md-5">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" class="form-control" id="search" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="Search by title...">
                            </div>
                            <div class="col-md-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">All Status</option>
                                    <option value="published" <?php echo $status === 'published' ? 'selected' : ''; ?>>Published</option>
                                    <option value="draft" <?php echo $status === 'draft' ? 'selected' : ''; ?>>Draft</option>
                                </select>
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <a href="pages.php" class="btn btn-secondary w-100">
                                    <i class="fas fa-sync-alt"></i> Reset
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Pages Table -->
                <div class="admin-card">
                    <div class="admin-card-header">
                        <h2 class="admin-card-title">
                            <i class="fas fa-file-alt me-2"></i> All Pages
                        </h2>
                        <span class="badge bg-primary"><?php echo $totalCount; ?> Total</span>
                    </div>
                    <div class="admin-card-body p-0">
                        <div class="table-responsive">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Title</th>
                                        <th>Slug</th>
                                        <th>Status</th>
                                        <th>Last Updated</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($pages)): ?>
                                        <?php foreach ($pages as $pageItem): ?>
                                            <tr>
                                                <td><?php echo $pageItem['page_id']; ?></td>
                                                <td>
                                                    <div class="fw-bold"><?php echo htmlspecialchars($pageItem['title']); ?></div>
                                                </td>
                                                <td><?php echo htmlspecialchars($pageItem['slug']); ?></td>
                                                <td>
                                                    <?php if ($pageItem['status'] === 'published'): ?>
                                                        <span class="badge bg-success">Published</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-warning">Draft</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo date('M d, Y', strtotime($pageItem['updated_at'])); ?></td>
                                                <td>
                                                    <div class="btn-group">
                                                        <a href="page-edit.php?id=<?php echo $pageItem['page_id']; ?>" class="btn btn-sm btn-primary" title="Edit">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <a href="../<?php echo $pageItem['slug']; ?>" target="_blank" class="btn btn-sm btn-info" title="View">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <?php if ($pageItem['status'] === 'draft'): ?>
                                                            <a href="pages.php?action=publish&id=<?php echo $pageItem['page_id']; ?>" class="btn btn-sm btn-success" title="Publish" onclick="return confirm('Are you sure you want to publish this page?');">
                                                                <i class="fas fa-check"></i>
                                                            </a>
                                                        <?php else: ?>
                                                            <a href="pages.php?action=draft&id=<?php echo $pageItem['page_id']; ?>" class="btn btn-sm btn-warning" title="Move to Draft" onclick="return confirm('Are you sure you want to move this page to draft?');">
                                                                <i class="fas fa-file"></i>
                                                            </a>
                                                        <?php endif; ?>
                                                        <a href="pages.php?action=delete&id=<?php echo $pageItem['page_id']; ?>" class="btn btn-sm btn-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this page? This action cannot be undone.');">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="6" class="text-center">No pages found.</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <div class="admin-card-footer">
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center mb-0">
                                    <?php if ($page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $page - 1; ?>&status=<?php echo urlencode($status); ?>&search=<?php echo urlencode($search); ?>" aria-label="Previous">
                                                <span aria-hidden="true">&laquo;</span>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                    
                                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo urlencode($status); ?>&search=<?php echo urlencode($search); ?>">
                                                <?php echo $i; ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>
                                    
                                    <?php if ($page < $totalPages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $page + 1; ?>&status=<?php echo urlencode($status); ?>&search=<?php echo urlencode($search); ?>" aria-label="Next">
                                                <span aria-hidden="true">&raquo;</span>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Footer -->
            <?php include 'templates/footer.php'; ?>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Admin JS -->
    <script src="assets/js/admin.js"></script>
</body>
</html>