/**
 * Admin Panel Stylesheet
 */

/* ===== Variables ===== */
:root {
    /* Primary Colors */
    --dark-blue: #003366;
    --light-blue: #66B2FF;
    --white: #FFFFFF;
    
    /* Secondary Colors */
    --light-gray: #F5F5F5;
    --dark-gray: #333333;
    --medium-gray: #E0E0E0;
    
    /* Glowing/Accent Colors */
    --electric-cyan: #00FFFF;
    --neon-green: #00FF00;
    --magenta: #FF00FF;
    
    /* Text Colors */
    --dark-text: #1A1A1A;
    --light-text: #F5F5F5;
    
    /* Other Colors */
    --success: #28a745;
    --danger: #dc3545;
    --warning: #ffc107;
    --info: #17a2b8;
    
    /* Spacing */
    --spacing-xs: 5px;
    --spacing-sm: 10px;
    --spacing-md: 15px;
    --spacing-lg: 20px;
    --spacing-xl: 30px;
    --spacing-xxl: 50px;
    
    /* Border Radius */
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;
    
    /* Box Shadow */
    --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --box-shadow-hover: 0 5px 15px rgba(0, 0, 0, 0.2);
    
    /* Sidebar */
    --sidebar-width: 250px;
    --sidebar-collapsed-width: 70px;
    
    /* Header */
    --header-height: 70px;
}

/* ===== Base Styles ===== */
body {
    font-family: 'Roboto', sans-serif;
    color: var(--dark-text);
    line-height: 1.5;
    background-color: var(--light-gray);
    min-height: 100vh;
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    color: var(--dark-blue);
}

a {
    color: var(--light-blue);
    text-decoration: none;
    transition: all 0.3s ease;
}

a:hover {
    color: var(--dark-blue);
}

/* ===== Login Page ===== */
.admin-login-page {
    background: linear-gradient(135deg, var(--dark-blue) 0%, #001a33 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: var(--spacing-lg);
}

.login-container {
    width: 100%;
    max-width: 450px;
}

.login-card {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    position: relative;
}

.login-card:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, var(--light-blue), var(--electric-cyan));
}

.login-header {
    padding: var(--spacing-xl);
    text-align: center;
    border-bottom: 1px solid var(--light-gray);
}

.login-logo {
    margin-bottom: var(--spacing-lg);
}

.login-logo img {
    max-height: 60px;
}

.login-header h1 {
    margin-bottom: var(--spacing-sm);
    color: var(--dark-blue);
    font-size: 24px;
}

.login-header p {
    color: var(--dark-gray);
    margin-bottom: 0;
}

.login-form {
    padding: var(--spacing-xl);
}

.login-form .input-group-text {
    background-color: var(--light-gray);
    border-color: var(--medium-gray);
    color: var(--dark-blue);
}

.login-form .form-control {
    border-color: var(--medium-gray);
}

.login-form .form-control:focus {
    border-color: var(--light-blue);
    box-shadow: 0 0 0 0.2rem rgba(102, 178, 255, 0.25);
}

.login-form .btn-primary {
    background-color: var(--light-blue);
    border-color: var(--light-blue);
    padding: 12px;
    font-weight: 600;
}

.login-form .btn-primary:hover {
    background-color: var(--dark-blue);
    border-color: var(--dark-blue);
}

.login-form .toggle-password {
    border-color: var(--medium-gray);
    color: var(--dark-gray);
}

.login-form .toggle-password:hover {
    background-color: var(--light-gray);
}

.forgot-password a {
    color: var(--dark-gray);
    font-size: 14px;
}

.forgot-password a:hover {
    color: var(--light-blue);
}

.login-footer {
    padding: var(--spacing-lg);
    text-align: center;
    background-color: var(--light-gray);
    border-top: 1px solid var(--medium-gray);
}

.login-footer p {
    margin-bottom: 5px;
    font-size: 14px;
    color: var(--dark-gray);
}

/* ===== Admin Layout ===== */
.admin-layout {
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.admin-sidebar {
    width: var(--sidebar-width);
    background-color: var(--dark-blue);
    color: var(--light-text);
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1000;
    transition: all 0.3s ease;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
}

.admin-sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar-header {
    padding: var(--spacing-md) var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    height: var(--header-height);
}

.sidebar-logo {
    display: flex;
    align-items: center;
}

.sidebar-logo img {
    height: 40px;
    transition: all 0.3s ease;
}

.sidebar-logo .logo-text {
    margin-left: var(--spacing-md);
    font-weight: 700;
    font-size: 18px;
    color: var(--white);
    transition: all 0.3s ease;
}

.collapsed .sidebar-logo .logo-text {
    display: none;
}

.sidebar-toggle {
    background: transparent;
    border: none;
    color: var(--light-text);
    cursor: pointer;
    font-size: 18px;
    padding: 5px;
    transition: all 0.3s ease;
}

.sidebar-toggle:hover {
    color: var(--electric-cyan);
}

.sidebar-menu {
    padding: var(--spacing-md) 0;
}

.menu-section {
    margin-bottom: var(--spacing-md);
}

.menu-section-title {
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: 12px;
    text-transform: uppercase;
    color: rgba(255, 255, 255, 0.5);
    font-weight: 600;
    letter-spacing: 1px;
}

.collapsed .menu-section-title {
    display: none;
}

.menu-item {
    position: relative;
}

.menu-link {
    display: flex;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-lg);
    color: var(--light-text);
    transition: all 0.3s ease;
    position: relative;
}

.menu-link:hover, .menu-link.active {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--electric-cyan);
}

.menu-link.active:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 4px;
    background: linear-gradient(to bottom, var(--electric-cyan), var(--light-blue));
}

.menu-icon {
    width: 20px;
    text-align: center;
    margin-right: var(--spacing-md);
    font-size: 16px;
}

.collapsed .menu-icon {
    margin-right: 0;
    font-size: 18px;
}

.menu-text {
    flex: 1;
    transition: all 0.3s ease;
}

.collapsed .menu-text {
    display: none;
}

.menu-arrow {
    font-size: 12px;
    transition: all 0.3s ease;
}

.collapsed .menu-arrow {
    display: none;
}

.menu-link[aria-expanded="true"] .menu-arrow {
    transform: rotate(90deg);
}

.submenu {
    padding-left: var(--spacing-lg);
    background-color: rgba(0, 0, 0, 0.1);
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.submenu.show {
    max-height: 1000px;
}

.collapsed .submenu {
    display: none;
}

.submenu-link {
    display: block;
    padding: var(--spacing-sm) var(--spacing-lg);
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    transition: all 0.3s ease;
}

.submenu-link:hover, .submenu-link.active {
    color: var(--electric-cyan);
    background-color: rgba(255, 255, 255, 0.05);
}

.sidebar-footer {
    padding: var(--spacing-md) var(--spacing-lg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 12px;
    color: rgba(255, 255, 255, 0.5);
    text-align: center;
}

.collapsed .sidebar-footer {
    display: none;
}

/* Main Content */
.admin-main {
    flex: 1;
    margin-left: var(--sidebar-width);
    transition: all 0.3s ease;
}

.admin-main.expanded {
    margin-left: var(--sidebar-collapsed-width);
}

/* Header */
.admin-header {
    background-color: var(--white);
    height: var(--header-height);
    padding: 0 var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 999;
}

.header-search {
    position: relative;
    width: 300px;
}

.header-search input {
    padding-left: 40px;
    background-color: var(--light-gray);
    border: none;
    border-radius: 20px;
}

.header-search i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--dark-gray);
}

.header-actions {
    display: flex;
    align-items: center;
}

.header-action-item {
    position: relative;
    margin-left: var(--spacing-md);
}

.header-action-btn {
    background: transparent;
    border: none;
    color: var(--dark-gray);
    font-size: 18px;
    cursor: pointer;
    padding: 5px;
    transition: all 0.3s ease;
}

.header-action-btn:hover {
    color: var(--light-blue);
}

.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: var(--danger);
    color: var(--white);
    font-size: 10px;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-dropdown {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: var(--border-radius-md);
    transition: all 0.3s ease;
}

.user-dropdown:hover {
    background-color: var(--light-gray);
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: var(--spacing-sm);
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-info {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-weight: 600;
    color: var(--dark-blue);
    font-size: 14px;
}

.user-role {
    font-size: 12px;
    color: var(--dark-gray);
}

.dropdown-menu {
    border: none;
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    min-width: 200px;
}

.dropdown-item {
    padding: 8px 15px;
    font-weight: 500;
    display: flex;
    align-items: center;
}

.dropdown-item i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.dropdown-item:hover {
    background-color: var(--light-gray);
    color: var(--light-blue);
}

.dropdown-divider {
    margin: 5px 0;
    border-top: 1px solid var(--light-gray);
}

/* Content */
.admin-content {
    padding: var(--spacing-xl);
}

.page-title {
    margin-bottom: var(--spacing-xl);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.page-title h1 {
    margin-bottom: 0;
    font-size: 24px;
}

.breadcrumb {
    background-color: transparent;
    padding: 0;
    margin: 0;
    font-size: 14px;
}

.breadcrumb-item a {
    color: var(--dark-gray);
}

.breadcrumb-item.active {
    color: var(--dark-blue);
}

/* Cards */
.admin-card {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--box-shadow);
    margin-bottom: var(--spacing-xl);
    overflow: hidden;
}

.admin-card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--light-gray);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.admin-card-title {
    margin-bottom: 0;
    font-size: 18px;
    color: var(--dark-blue);
}

.admin-card-body {
    padding: var(--spacing-lg);
}

.admin-card-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--light-gray);
    background-color: var(--light-gray);
}

/* Dashboard Stats */
.stats-card {
    background-color: var(--white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--box-shadow);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-hover);
}

.stats-card:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    background: linear-gradient(to bottom, var(--light-blue), var(--electric-cyan));
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: var(--spacing-lg);
    color: var(--white);
    font-size: 24px;
    background: linear-gradient(135deg, var(--light-blue), var(--dark-blue));
}

.stats-info {
    flex: 1;
}

.stats-number {
    font-size: 24px;
    font-weight: 700;
    color: var(--dark-blue);
    margin-bottom: 5px;
}

.stats-label {
    color: var(--dark-gray);
    font-size: 14px;
    margin-bottom: 0;
}

.stats-change {
    font-size: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    margin-top: 5px;
}

.stats-change.positive {
    color: var(--success);
}

.stats-change.negative {
    color: var(--danger);
}

.stats-change i {
    margin-right: 5px;
}

/* Tables */
.admin-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.admin-table th {
    background-color: var(--light-gray);
    color: var(--dark-blue);
    font-weight: 600;
    padding: 12px 15px;
    text-align: left;
    border-bottom: 2px solid var(--medium-gray);
}

.admin-table td {
    padding: 12px 15px;
    border-bottom: 1px solid var(--light-gray);
    vertical-align: middle;
}

.admin-table tbody tr {
    transition: all 0.3s ease;
}

.admin-table tbody tr:hover {
    background-color: rgba(102, 178, 255, 0.05);
}

.admin-table .status-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-align: center;
}

.admin-table .status-active {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success);
}

.admin-table .status-inactive {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger);
}

.admin-table .status-pending {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--warning);
}

.admin-table .action-buttons {
    display: flex;
    gap: 5px;
}

.admin-table .btn-action {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    transition: all 0.3s ease;
}

.admin-table .btn-view {
    background-color: rgba(23, 162, 184, 0.1);
    color: var(--info);
}

.admin-table .btn-view:hover {
    background-color: var(--info);
    color: var(--white);
}

.admin-table .btn-edit {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--warning);
}

.admin-table .btn-edit:hover {
    background-color: var(--warning);
    color: var(--white);
}

.admin-table .btn-delete {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger);
}

.admin-table .btn-delete:hover {
    background-color: var(--danger);
    color: var(--white);
}

/* Forms */
.admin-form .form-label {
    font-weight: 600;
    color: var(--dark-blue);
    margin-bottom: 8px;
}

.admin-form .form-control {
    padding: 12px 15px;
    border-radius: var(--border-radius-md);
    border: 1px solid var(--medium-gray);
}

.admin-form .form-control:focus {
    border-color: var(--light-blue);
    box-shadow: 0 0 0 0.2rem rgba(102, 178, 255, 0.25);
}

.admin-form .form-select {
    padding: 12px 15px;
    border-radius: var(--border-radius-md);
    border: 1px solid var(--medium-gray);
}

.admin-form .form-select:focus {
    border-color: var(--light-blue);
    box-shadow: 0 0 0 0.2rem rgba(102, 178, 255, 0.25);
}

.admin-form .input-group-text {
    background-color: var(--light-gray);
    border-color: var(--medium-gray);
    color: var(--dark-blue);
}

.admin-form .form-check-input:checked {
    background-color: var(--light-blue);
    border-color: var(--light-blue);
}

.admin-form .btn-primary {
    background-color: var(--light-blue);
    border-color: var(--light-blue);
    padding: 12px 30px;
    font-weight: 600;
}

.admin-form .btn-primary:hover {
    background-color: var(--dark-blue);
    border-color: var(--dark-blue);
}

.admin-form .btn-secondary {
    background-color: var(--dark-gray);
    border-color: var(--dark-gray);
    padding: 12px 30px;
    font-weight: 600;
}

.admin-form .btn-secondary:hover {
    background-color: #222;
    border-color: #222;
}

/* File Upload */
.file-upload {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: var(--spacing-xl);
    border: 2px dashed var(--medium-gray);
    border-radius: var(--border-radius-md);
    background-color: var(--light-gray);
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-upload:hover {
    border-color: var(--light-blue);
    background-color: rgba(102, 178, 255, 0.05);
}

.file-upload input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.file-upload i {
    font-size: 40px;
    color: var(--dark-gray);
    margin-bottom: var(--spacing-md);
}

.file-upload p {
    margin-bottom: 0;
    color: var(--dark-gray);
    text-align: center;
}

.file-upload .file-name {
    margin-top: var(--spacing-md);
    font-weight: 600;
    color: var(--dark-blue);
}

/* Responsive */
@media (max-width: 991px) {
    .admin-sidebar {
        width: var(--sidebar-collapsed-width);
    }
    
    .admin-sidebar.expanded {
        width: var(--sidebar-width);
    }
    
    .admin-main {
        margin-left: var(--sidebar-collapsed-width);
    }
    
    .admin-main.collapsed {
        margin-left: var(--sidebar-width);
    }
    
    .collapsed .sidebar-logo .logo-text,
    .collapsed .menu-text,
    .collapsed .menu-arrow,
    .collapsed .menu-section-title,
    .collapsed .sidebar-footer {
        display: none;
    }
    
    .expanded .sidebar-logo .logo-text,
    .expanded .menu-text,
    .expanded .menu-arrow,
    .expanded .menu-section-title,
    .expanded .sidebar-footer {
        display: block;
    }
    
    .collapsed .menu-icon {
        margin-right: 0;
        font-size: 18px;
    }
    
    .expanded .menu-icon {
        margin-right: var(--spacing-md);
        font-size: 16px;
    }
    
    .header-search {
        width: 200px;
    }
}

@media (max-width: 767px) {
    .admin-header {
        padding: 0 var(--spacing-md);
    }
    
    .header-search {
        display: none;
    }
    
    .user-info {
        display: none;
    }
    
    .stats-card {
        margin-bottom: var(--spacing-lg);
    }
    
    .admin-content {
        padding: var(--spacing-lg);
    }
    
    .page-title {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .page-title h1 {
        margin-bottom: var(--spacing-sm);
    }
    
    .admin-table {
        display: block;
        overflow-x: auto;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.fade-in {
    animation: fadeIn 0.3s ease;
}

/* Utilities */
.hover-scale {
    transition: transform 0.3s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-glow:hover {
    box-shadow: 0 0 15px var(--electric-cyan);
}

.text-truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.cursor-pointer {
    cursor: pointer;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--light-blue), var(--dark-blue));
}

.bg-gradient-success {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.bg-gradient-danger {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
}

.bg-gradient-info {
    background: linear-gradient(135deg, #17a2b8, #0dcaf0);
}