<?php
/**
 * Admin FAQs
 */
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    require_once '../includes/autoload.php';
    
    // Check user authentication
    $user = new User();
    if (!$user->isLoggedIn() || !$user->isAdmin()) {
        header('Location: login.php');
        exit;
    }
    
    // Get admin info
    $adminInfo = $user->getUserData();
    
    // Initialize settings
    $settings = Settings::getInstance();
    
    // Initialize FAQ object
    $faqObj = new FAQ();
    
    // Handle actions
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    $faqId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    $message = '';
    
    if ($action === 'delete' && $faqId > 0) {
        if ($faqObj->delete($faqId)) {
            $message = '<div class="alert alert-success">FAQ deleted successfully.</div>';
        } else {
            $message = '<div class="alert alert-danger">Failed to delete FAQ.</div>';
        }
    } else if ($action === 'activate' && $faqId > 0) {
        if ($faqObj->update($faqId, ['status' => 'active'])) {
            $message = '<div class="alert alert-success">FAQ activated successfully.</div>';
        } else {
            $message = '<div class="alert alert-danger">Failed to activate FAQ.</div>';
        }
    } else if ($action === 'deactivate' && $faqId > 0) {
        if ($faqObj->update($faqId, ['status' => 'inactive'])) {
            $message = '<div class="alert alert-success">FAQ deactivated successfully.</div>';
        } else {
            $message = '<div class="alert alert-danger">Failed to deactivate FAQ.</div>';
        }
    }
    
    // Handle form submission for adding/editing FAQ
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $question = trim($_POST['question']);
        $answer = trim($_POST['answer']);
        $category = trim($_POST['category']);
        $status = $_POST['status'];
        $displayOrder = (int)$_POST['display_order'];
        
        if (empty($question) || empty($answer)) {
            $message = '<div class="alert alert-danger">Question and answer are required.</div>';
        } else {
            $faqData = [
                'question' => $question,
                'answer' => $answer,
                'category' => $category,
                'status' => $status,
                'display_order' => $displayOrder
            ];
            
            if (isset($_POST['edit_id']) && $_POST['edit_id'] > 0) {
                // Update existing FAQ
                $editId = (int)$_POST['edit_id'];
                if ($faqObj->update($editId, $faqData)) {
                    $message = '<div class="alert alert-success">FAQ updated successfully.</div>';
                } else {
                    $message = '<div class="alert alert-danger">Failed to update FAQ.</div>';
                }
            } else {
                // Add new FAQ
                $faqData['created_at'] = date('Y-m-d H:i:s');
                
                if ($faqObj->create($faqData)) {
                    $message = '<div class="alert alert-success">FAQ added successfully.</div>';
                } else {
                    $message = '<div class="alert alert-danger">Failed to add FAQ.</div>';
                }
            }
        }
    }
    
    // Get FAQ to edit
    $editFaq = null;
    if ($action === 'edit' && $faqId > 0) {
        $editFaq = $faqObj->getById($faqId);
    }
    
    // Get all FAQs
    $faqs = $faqObj->getAll();
    
    // Get unique categories
    $categories = $faqObj->getCategories();
    
} catch (Exception $e) {
    die('<div style="color:red;padding:20px;border:2px solid red;margin:20px;">'
        . '<h2>Error</h2>'
        . '<p><strong>Message:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>'
        . '<p><strong>File:</strong> ' . htmlspecialchars($e->getFile()) . '</p>'
        . '<p><strong>Line:</strong> ' . htmlspecialchars($e->getLine()) . '</p>'
        . '</div>');
}

// Page title
$pageTitle = 'FAQs';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Admin Panel | <?php echo $settings->getSiteName(); ?></title>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo getAssetUrl($settings->getSiteFavicon()); ?>" type="image/x-icon">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Admin CSS -->
    <link rel="stylesheet" href="assets/css/admin.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <?php include 'templates/sidebar.php'; ?>
        
        <!-- Main Content -->
        <div class="admin-main" id="adminMain">
            <!-- Header -->
            <?php include 'templates/header.php'; ?>
            
            <!-- Content -->
            <div class="admin-content">
                <div class="page-title">
                    <div>
                        <h1><?php echo $pageTitle; ?></h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="dashboard.php">Home</a></li>
                                <li class="breadcrumb-item">Content</li>
                                <li class="breadcrumb-item active" aria-current="page"><?php echo $pageTitle; ?></li>
                            </ol>
                        </nav>
                    </div>
                </div>
                
                <?php echo $message; ?>
                
                <div class="row">
                    <!-- Add/Edit FAQ Form -->
                    <div class="col-md-4">
                        <div class="admin-card">
                            <div class="admin-card-header">
                                <h2 class="admin-card-title">
                                    <i class="fas fa-question-circle me-2"></i> <?php echo $editFaq ? 'Edit FAQ' : 'Add New FAQ'; ?>
                                </h2>
                            </div>
                            <div class="admin-card-body">
                                <form action="" method="post">
                                    <?php if ($editFaq): ?>
                                        <input type="hidden" name="edit_id" value="<?php echo $editFaq['faq_id']; ?>">
                                    <?php endif; ?>
                                    
                                    <div class="mb-3">
                                        <label for="question" class="form-label">Question <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="question" name="question" value="<?php echo $editFaq ? htmlspecialchars($editFaq['question']) : ''; ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="answer" class="form-label">Answer <span class="text-danger">*</span></label>
                                        <textarea class="form-control" id="answer" name="answer" rows="5" required><?php echo $editFaq ? htmlspecialchars($editFaq['answer']) : ''; ?></textarea>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="category" class="form-label">Category</label>
                                        <input type="text" class="form-control" id="category" name="category" value="<?php echo $editFaq ? htmlspecialchars($editFaq['category']) : ''; ?>" list="category-list">
                                        <datalist id="category-list">
                                            <?php foreach ($categories as $category): ?>
                                                <option value="<?php echo htmlspecialchars($category); ?>">
                                            <?php endforeach; ?>
                                        </datalist>
                                        <small class="text-muted">Group FAQs by category (e.g., General, Pricing, Technical)</small>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="status" class="form-label">Status</label>
                                        <select class="form-select" id="status" name="status">
                                            <option value="active" <?php echo $editFaq && $editFaq['status'] === 'active' ? 'selected' : ''; ?>>Active</option>
                                            <option value="inactive" <?php echo $editFaq && $editFaq['status'] === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="display_order" class="form-label">Display Order</label>
                                        <input type="number" class="form-control" id="display_order" name="display_order" value="<?php echo $editFaq ? (int)$editFaq['display_order'] : 0; ?>" min="0">
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <?php if ($editFaq): ?>
                                            <button type="submit" class="btn btn-primary">Update FAQ</button>
                                            <a href="faqs.php" class="btn btn-secondary">Cancel</a>
                                        <?php else: ?>
                                            <button type="submit" class="btn btn-primary">Add FAQ</button>
                                        <?php endif; ?>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- FAQs List -->
                    <div class="col-md-8">
                        <div class="admin-card">
                            <div class="admin-card-header">
                                <h2 class="admin-card-title">
                                    <i class="fas fa-list me-2"></i> All FAQs
                                </h2>
                                <span class="badge bg-primary"><?php echo count($faqs); ?> Total</span>
                            </div>
                            <div class="admin-card-body p-0">
                                <div class="table-responsive">
                                    <table class="admin-table">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Question</th>
                                                <th>Category</th>
                                                <th>Status</th>
                                                <th>Order</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (!empty($faqs)): ?>
                                                <?php foreach ($faqs as $faq): ?>
                                                    <tr>
                                                        <td><?php echo $faq['faq_id']; ?></td>
                                                        <td>
                                                            <div class="faq-question">
                                                                <?php echo htmlspecialchars($faq['question']); ?>
                                                                <a href="#" class="text-primary ms-2" data-bs-toggle="modal" data-bs-target="#faqModal<?php echo $faq['faq_id']; ?>">
                                                                    <i class="fas fa-eye"></i>
                                                                </a>
                                                            </div>
                                                            
                                                            <!-- FAQ Modal -->
                                                            <div class="modal fade" id="faqModal<?php echo $faq['faq_id']; ?>" tabindex="-1" aria-labelledby="faqModalLabel<?php echo $faq['faq_id']; ?>" aria-hidden="true">
                                                                <div class="modal-dialog">
                                                                    <div class="modal-content">
                                                                        <div class="modal-header">
                                                                            <h5 class="modal-title" id="faqModalLabel<?php echo $faq['faq_id']; ?>">FAQ Details</h5>
                                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                        </div>
                                                                        <div class="modal-body">
                                                                            <h5>Question:</h5>
                                                                            <p><?php echo htmlspecialchars($faq['question']); ?></p>
                                                                            <h5>Answer:</h5>
                                                                            <div><?php echo nl2br(htmlspecialchars($faq['answer'])); ?></div>
                                                                            <hr>
                                                                            <p><strong>Category:</strong> <?php echo htmlspecialchars($faq['category'] ?: 'None'); ?></p>
                                                                            <p><strong>Status:</strong> <?php echo ucfirst($faq['status']); ?></p>
                                                                            <p><strong>Display Order:</strong> <?php echo (int)$faq['display_order']; ?></p>
                                                                        </div>
                                                                        <div class="modal-footer">
                                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                                            <a href="faqs.php?action=edit&id=<?php echo $faq['faq_id']; ?>" class="btn btn-primary">Edit</a>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td><?php echo htmlspecialchars($faq['category'] ?: 'None'); ?></td>
                                                        <td>
                                                            <?php if ($faq['status'] === 'active'): ?>
                                                                <span class="badge bg-success">Active</span>
                                                            <?php else: ?>
                                                                <span class="badge bg-danger">Inactive</span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td><?php echo (int)$faq['display_order']; ?></td>
                                                        <td>
                                                            <div class="btn-group">
                                                                <a href="faqs.php?action=edit&id=<?php echo $faq['faq_id']; ?>" class="btn btn-sm btn-primary" title="Edit">
                                                                    <i class="fas fa-edit"></i>
                                                                </a>
                                                                <?php if ($faq['status'] === 'active'): ?>
                                                                    <a href="faqs.php?action=deactivate&id=<?php echo $faq['faq_id']; ?>" class="btn btn-sm btn-warning" title="Deactivate">
                                                                        <i class="fas fa-ban"></i>
                                                                    </a>
                                                                <?php else: ?>
                                                                    <a href="faqs.php?action=activate&id=<?php echo $faq['faq_id']; ?>" class="btn btn-sm btn-success" title="Activate">
                                                                        <i class="fas fa-check"></i>
                                                                    </a>
                                                                <?php endif; ?>
                                                                <a href="faqs.php?action=delete&id=<?php echo $faq['faq_id']; ?>" class="btn btn-sm btn-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this FAQ?');">
                                                                    <i class="fas fa-trash"></i>
                                                                </a>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="6" class="text-center">No FAQs found.</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Footer -->
            <?php include 'templates/footer.php'; ?>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Admin JS -->
    <script src="assets/js/admin.js"></script>
</body>
</html>