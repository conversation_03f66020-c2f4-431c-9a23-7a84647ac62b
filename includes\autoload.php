<?php
/**
 * Autoload file
 * Loads all required files and classes
 */

// Load configuration
require_once __DIR__ . '/config/config.php';

// Autoload classes
spl_autoload_register(function ($className) {
    $classFile = __DIR__ . '/classes/' . $className . '.php';
    
    if (file_exists($classFile)) {
        require_once $classFile;
    }
});

// Load functions
require_once __DIR__ . '/functions/helpers.php';

// Initialize database connection
$db = Database::getInstance();

// Initialize settings
$settings = Settings::getInstance();

// Initialize user
$user = new User();

// Set error handler
set_error_handler(function ($errno, $errstr, $errfile, $errline) {
    if (!(error_reporting() & $errno)) {
        // This error code is not included in error_reporting
        return;
    }
    
    $error = [
        'type' => $errno,
        'message' => $errstr,
        'file' => $errfile,
        'line' => $errline
    ];
    
    // Log error
    error_log(date('Y-m-d H:i:s') . ' - ' . $error['message'] . ' in ' . $error['file'] . ' on line ' . $error['line'] . "\n", 3, BASE_PATH . '/logs/error.log');
    
    if (ini_get('display_errors')) {
        echo '<div style="color: red; border: 1px solid red; padding: 10px; margin: 10px 0; background-color: #ffeeee;">';
        echo '<strong>Error:</strong> ' . $error['message'] . '<br>';
        echo '<strong>File:</strong> ' . $error['file'] . '<br>';
        echo '<strong>Line:</strong> ' . $error['line'];
        echo '</div>';
    }
    
    return true;
});