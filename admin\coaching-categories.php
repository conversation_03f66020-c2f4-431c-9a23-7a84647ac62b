<?php
/**
 * Admin Coaching Categories
 */
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    require_once '../includes/autoload.php';
    
    // Check user authentication
    $user = new User();
    if (!$user->isLoggedIn() || !$user->isAdmin()) {
        header('Location: login.php');
        exit;
    }
    
    // Get admin info
    $adminInfo = $user->getUserData();
    
    // Initialize settings
    $settings = Settings::getInstance();
    
    // Initialize category object
    $categoryObj = new Category();
    
    // Handle actions
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    $categoryId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    $message = '';
    
    if ($action === 'delete' && $categoryId > 0) {
        if ($categoryObj->delete($categoryId)) {
            $message = '<div class="alert alert-success">Category deleted successfully.</div>';
        } else {
            $message = '<div class="alert alert-danger">Failed to delete category. It may be in use.</div>';
        }
    }
    
    // Handle form submission for adding/editing category
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $categoryName = trim($_POST['category_name']);
        $description = trim($_POST['description']);
        $status = $_POST['status'];
        $displayOrder = (int)$_POST['display_order'];
        
        if (empty($categoryName)) {
            $message = '<div class="alert alert-danger">Category name is required.</div>';
        } else {
            $categoryData = [
                'category_name' => $categoryName,
                'description' => $description,
                'status' => $status,
                'display_order' => $displayOrder
            ];
            
            if (isset($_POST['edit_id']) && $_POST['edit_id'] > 0) {
                // Update existing category
                $editId = (int)$_POST['edit_id'];
                if ($categoryObj->update($editId, $categoryData)) {
                    $message = '<div class="alert alert-success">Category updated successfully.</div>';
                } else {
                    $message = '<div class="alert alert-danger">Failed to update category.</div>';
                }
            } else {
                // Add new category
                $categoryData['slug'] = Utility::generateSlug($categoryName);
                $categoryData['created_at'] = date('Y-m-d H:i:s');
                
                if ($categoryObj->add($categoryData)) {
                    $message = '<div class="alert alert-success">Category added successfully.</div>';
                } else {
                    $message = '<div class="alert alert-danger">Failed to add category.</div>';
                }
            }
        }
    }
    
    // Get category to edit
    $editCategory = null;
    if ($action === 'edit' && $categoryId > 0) {
        $editCategory = $categoryObj->getById($categoryId);
    }
    
    // Get all categories
    $categories = $categoryObj->getAll();
    
} catch (Exception $e) {
    die('<div style="color:red;padding:20px;border:2px solid red;margin:20px;">'
        . '<h2>Error</h2>'
        . '<p><strong>Message:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>'
        . '<p><strong>File:</strong> ' . htmlspecialchars($e->getFile()) . '</p>'
        . '<p><strong>Line:</strong> ' . htmlspecialchars($e->getLine()) . '</p>'
        . '</div>');
}

// Page title
$pageTitle = 'Coaching Categories';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Admin Panel | <?php echo $settings->getSiteName(); ?></title>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo getAssetUrl($settings->getSiteFavicon()); ?>" type="image/x-icon">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Admin CSS -->
    <link rel="stylesheet" href="assets/css/admin.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <?php include 'templates/sidebar.php'; ?>
        
        <!-- Main Content -->
        <div class="admin-main" id="adminMain">
            <!-- Header -->
            <?php include 'templates/header.php'; ?>
            
            <!-- Content -->
            <div class="admin-content">
                <div class="page-title">
                    <div>
                        <h1><?php echo $pageTitle; ?></h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="dashboard.php">Home</a></li>
                                <li class="breadcrumb-item"><a href="coaching-centers.php">Coaching Centers</a></li>
                                <li class="breadcrumb-item active" aria-current="page"><?php echo $pageTitle; ?></li>
                            </ol>
                        </nav>
                    </div>
                </div>
                
                <?php echo $message; ?>
                
                <div class="row">
                    <!-- Add/Edit Category Form -->
                    <div class="col-md-4">
                        <div class="admin-card">
                            <div class="admin-card-header">
                                <h2 class="admin-card-title">
                                    <i class="fas fa-folder-plus me-2"></i> <?php echo $editCategory ? 'Edit Category' : 'Add New Category'; ?>
                                </h2>
                            </div>
                            <div class="admin-card-body">
                                <form action="" method="post">
                                    <?php if ($editCategory): ?>
                                        <input type="hidden" name="edit_id" value="<?php echo $editCategory['category_id']; ?>">
                                    <?php endif; ?>
                                    
                                    <div class="mb-3">
                                        <label for="category_name" class="form-label">Category Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="category_name" name="category_name" value="<?php echo $editCategory ? htmlspecialchars($editCategory['category_name']) : ''; ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="description" class="form-label">Description</label>
                                        <textarea class="form-control" id="description" name="description" rows="3"><?php echo $editCategory ? htmlspecialchars($editCategory['description']) : ''; ?></textarea>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="status" class="form-label">Status</label>
                                        <select class="form-select" id="status" name="status">
                                            <option value="active" <?php echo $editCategory && $editCategory['status'] === 'active' ? 'selected' : ''; ?>>Active</option>
                                            <option value="inactive" <?php echo $editCategory && $editCategory['status'] === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="display_order" class="form-label">Display Order</label>
                                        <input type="number" class="form-control" id="display_order" name="display_order" value="<?php echo $editCategory ? (int)$editCategory['display_order'] : 0; ?>" min="0">
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <?php if ($editCategory): ?>
                                            <button type="submit" class="btn btn-primary">Update Category</button>
                                            <a href="coaching-categories.php" class="btn btn-secondary">Cancel</a>
                                        <?php else: ?>
                                            <button type="submit" class="btn btn-primary">Add Category</button>
                                        <?php endif; ?>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Categories List -->
                    <div class="col-md-8">
                        <div class="admin-card">
                            <div class="admin-card-header">
                                <h2 class="admin-card-title">
                                    <i class="fas fa-folder me-2"></i> All Categories
                                </h2>
                                <span class="badge bg-primary"><?php echo count($categories); ?> Total</span>
                            </div>
                            <div class="admin-card-body p-0">
                                <div class="table-responsive">
                                    <table class="admin-table">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Name</th>
                                                <th>Slug</th>
                                                <th>Status</th>
                                                <th>Order</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (!empty($categories)): ?>
                                                <?php foreach ($categories as $category): ?>
                                                    <tr>
                                                        <td><?php echo $category['category_id']; ?></td>
                                                        <td><?php echo htmlspecialchars($category['category_name']); ?></td>
                                                        <td><?php echo htmlspecialchars($category['slug']); ?></td>
                                                        <td>
                                                            <?php if ($category['status'] === 'active'): ?>
                                                                <span class="badge bg-success">Active</span>
                                                            <?php else: ?>
                                                                <span class="badge bg-danger">Inactive</span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td><?php echo (int)$category['display_order']; ?></td>
                                                        <td>
                                                            <div class="btn-group">
                                                                <a href="coaching-categories.php?action=edit&id=<?php echo $category['category_id']; ?>" class="btn btn-sm btn-primary" title="Edit">
                                                                    <i class="fas fa-edit"></i>
                                                                </a>
                                                                <a href="coaching-categories.php?action=delete&id=<?php echo $category['category_id']; ?>" class="btn btn-sm btn-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this category? This may affect coaching centers using this category.');">
                                                                    <i class="fas fa-trash"></i>
                                                                </a>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="6" class="text-center">No categories found.</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Footer -->
            <?php include 'templates/footer.php'; ?>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Admin JS -->
    <script src="assets/js/admin.js"></script>
</body>
</html>