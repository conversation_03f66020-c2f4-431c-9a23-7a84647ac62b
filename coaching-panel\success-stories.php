<?php
require_once '../includes/autoload.php';
Auth::requireCoachingOwner();

$coachingId = $_SESSION['coaching_id'];
$coachingObj = new CoachingCenter();
$categoryObj = new Category();

// Handle add/edit/delete
$message = '';
$editStory = null;

if (isset($_GET['action'], $_GET['story_id']) && $_GET['action'] === 'delete') {
    $storyId = (int)$_GET['story_id'];
    $deleted = Database::getInstance()->delete('success_stories', 'story_id = ? AND coaching_id = ?', [$storyId, $coachingId]);
    $message = $deleted ? '<div class="alert alert-success">Success story deleted.</div>' : '<div class="alert alert-danger">Failed to delete success story.</div>';
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $data = [
        'coaching_id' => $coachingId,
        'student_name' => trim($_POST['student_name']),
        'achievement' => trim($_POST['achievement']),
        'year' => (int)$_POST['year'],
        'testimonial' => trim($_POST['testimonial']),
        'course_id' => (int)($_POST['course_id'] ?? 0),
        'status' => $_POST['status'] ?? 'active',
    ];
    // Handle image upload
    if (!empty($_FILES['student_image']['name'])) {
        $uploadDir = '../uploads/success_stories/';
        if (!is_dir($uploadDir)) mkdir($uploadDir, 0755, true);
        $fileName = 'story_' . time() . '_' . basename($_FILES['student_image']['name']);
        $targetFile = $uploadDir . $fileName;
        if (move_uploaded_file($_FILES['student_image']['tmp_name'], $targetFile)) {
            $data['student_image'] = 'uploads/success_stories/' . $fileName;
        }
    }
    if (!empty($_POST['edit_id'])) {
        $editId = (int)$_POST['edit_id'];
        $updated = Database::getInstance()->update('success_stories', $data, 'story_id = ? AND coaching_id = ?', [$editId, $coachingId]);
        $message = $updated ? '<div class="alert alert-success">Success story updated.</div>' : '<div class="alert alert-danger">Failed to update success story.</div>';
    } else {
        $inserted = Database::getInstance()->insert('success_stories', $data);
        $message = $inserted ? '<div class="alert alert-success">Success story added.</div>' : '<div class="alert alert-danger">Failed to add success story.</div>';
    }
}

if (isset($_GET['action'], $_GET['story_id']) && $_GET['action'] === 'edit') {
    $editStory = Database::getInstance()->fetchRow('SELECT * FROM success_stories WHERE story_id = ? AND coaching_id = ?', [(int)$_GET['story_id'], $coachingId]);
}

// Fetch all success stories for this coaching center
$stories = Database::getInstance()->fetchAll('SELECT ss.*, c.course_name FROM success_stories ss LEFT JOIN courses c ON ss.course_id = c.course_id WHERE ss.coaching_id = ? ORDER BY ss.year DESC, ss.story_id DESC', [$coachingId]);
// Fetch all courses for this coaching center
$courses = $coachingObj->getCourses($coachingId);
$pageTitle = 'Success Stories';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Coaching Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="<?php echo getAssetUrl('css/admin.css'); ?>">
</head>
<body>
    <div class="dashboard-container">
        <?php include 'templates/sidebar.php'; ?>
        <div class="main-content">
            <?php include 'templates/header.php'; ?>
            <div class="container-fluid">
                <div class="row mb-4">
                    <div class="col-12">
                        <h1 class="page-title">Success Stories</h1>
                        <p class="text-muted">Manage your coaching center's success stories here.</p>
                    </div>
                </div>
                <?php echo $message; ?>
                <div class="row">
                    <div class="col-lg-5 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><?php echo $editStory ? 'Edit Success Story' : 'Add New Success Story'; ?></h5>
                            </div>
                            <div class="card-body">
                                <form method="post" enctype="multipart/form-data">
                                    <?php if ($editStory): ?>
                                        <input type="hidden" name="edit_id" value="<?php echo $editStory['story_id']; ?>">
                                    <?php endif; ?>
                                    <div class="mb-3">
                                        <label class="form-label">Student Name</label>
                                        <input type="text" name="student_name" class="form-control" value="<?php echo htmlspecialchars($editStory['student_name'] ?? ''); ?>" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Achievement</label>
                                        <input type="text" name="achievement" class="form-control" value="<?php echo htmlspecialchars($editStory['achievement'] ?? ''); ?>" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Year</label>
                                        <input type="number" name="year" class="form-control" value="<?php echo htmlspecialchars($editStory['year'] ?? date('Y')); ?>" min="2000" max="<?php echo date('Y'); ?>" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Course</label>
                                        <select name="course_id" class="form-select">
                                            <option value="">Select Course</option>
                                            <?php foreach ($courses as $course): ?>
                                                <option value="<?php echo $course['course_id']; ?>" <?php echo (isset($editStory['course_id']) && $editStory['course_id'] == $course['course_id']) ? 'selected' : ''; ?>><?php echo htmlspecialchars($course['course_name']); ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Testimonial</label>
                                        <textarea name="testimonial" class="form-control" rows="3"><?php echo htmlspecialchars($editStory['testimonial'] ?? ''); ?></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Student Image</label>
                                        <?php if (!empty($editStory['student_image'])): ?>
                                            <div class="mb-2"><img src="<?php echo getUploadUrl($editStory['student_image']); ?>" alt="Student Image" class="img-thumbnail" style="max-width: 100px;"></div>
                                        <?php endif; ?>
                                        <input type="file" name="student_image" class="form-control" accept="image/*">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Status</label>
                                        <select name="status" class="form-select">
                                            <option value="active" <?php echo (isset($editStory['status']) && $editStory['status'] == 'active') ? 'selected' : ''; ?>>Active</option>
                                            <option value="inactive" <?php echo (isset($editStory['status']) && $editStory['status'] == 'inactive') ? 'selected' : ''; ?>>Inactive</option>
                                        </select>
                                    </div>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary"><?php echo $editStory ? 'Update Story' : 'Add Story'; ?></button>
                                        <?php if ($editStory): ?>
                                            <a href="success-stories.php" class="btn btn-secondary mt-2">Cancel</a>
                                        <?php endif; ?>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-7">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Your Success Stories</h5>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-striped mb-0">
                                        <thead>
                                            <tr>
                                                <th>Student</th>
                                                <th>Achievement</th>
                                                <th>Year</th>
                                                <th>Course</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (!empty($stories)): ?>
                                                <?php foreach ($stories as $story): ?>
                                                    <tr>
                                                        <td>
                                                            <?php if (!empty($story['student_image'])): ?>
                                                                <img src="<?php echo getUploadUrl($story['student_image']); ?>" alt="Student Image" class="rounded me-2" style="width: 40px; height: 40px; object-fit: cover;">
                                                            <?php endif; ?>
                                                            <?php echo htmlspecialchars($story['student_name']); ?>
                                                        </td>
                                                        <td><?php echo htmlspecialchars($story['achievement']); ?></td>
                                                        <td><?php echo htmlspecialchars($story['year']); ?></td>
                                                        <td><?php echo htmlspecialchars($story['course_name'] ?? ''); ?></td>
                                                        <td>
                                                            <span class="badge bg-<?php echo $story['status'] === 'active' ? 'success' : 'danger'; ?>"><?php echo ucfirst($story['status']); ?></span>
                                                        </td>
                                                        <td>
                                                            <a href="success-stories.php?action=edit&story_id=<?php echo $story['story_id']; ?>" class="btn btn-sm btn-primary">Edit</a>
                                                            <a href="success-stories.php?action=delete&story_id=<?php echo $story['story_id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this story?');">Delete</a>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php else: ?>
                                                <tr><td colspan="6" class="text-center">No success stories found.</td></tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
