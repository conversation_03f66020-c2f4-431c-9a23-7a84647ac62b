<?php
/**
 * Page Class
 * Handles operations related to pages
 */
class Page {
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Get all pages
     * @param array $filters Optional filters
     * @param int $page Page number
     * @param int $limit Records per page
     * @return array Pages and pagination info
     */
    public function getAll($filters = [], $page = 1, $limit = 10) {
        $where = "1=1";
        $params = [];
        
        // Apply filters
        if (isset($filters['status']) && !empty($filters['status'])) {
            $where .= " AND status = ?";
            $params[] = $filters['status'];
        }
        
        if (isset($filters['search']) && !empty($filters['search'])) {
            $where .= " AND (title LIKE ? OR slug LIKE ? OR content LIKE ?)";
            $params[] = "%{$filters['search']}%";
            $params[] = "%{$filters['search']}%";
            $params[] = "%{$filters['search']}%";
        }
        
        // Count total records
        $totalCount = $this->db->count('pages', $where, $params);
        $totalPages = ceil($totalCount / $limit);
        
        // Ensure page is within valid range
        $page = max(1, min($page, $totalPages));
        $offset = ($page - 1) * $limit;
        
        // Get pages
        $sql = "SELECT * FROM pages WHERE {$where} ORDER BY display_order ASC, title ASC LIMIT ?, ?";
        $params[] = $offset;
        $params[] = $limit;
        
        $pages = $this->db->fetchAll($sql, $params);
        
        return [
            'pages' => $pages,
            'total_count' => $totalCount,
            'total_pages' => $totalPages,
            'current_page' => $page
        ];
    }
    
    /**
     * Get page by ID
     * @param int $pageId Page ID
     * @return array|null Page data
     */
    public function getById($pageId) {
        $sql = "SELECT * FROM pages WHERE page_id = ?";
        return $this->db->fetchRow($sql, [$pageId]);
    }
    
    /**
     * Get page by slug
     * @param string $slug Page slug
     * @return array|null Page data
     */
    public function getBySlug($slug) {
        $sql = "SELECT * FROM pages WHERE slug = ? AND status = 'active'";
        return $this->db->fetchRow($sql, [$slug]);
    }
    
    /**
     * Create new page
     * @param array $data Page data
     * @return int|bool New page ID or false on failure
     */
    public function create($data) {
        // Generate slug if not provided
        if (!isset($data['slug']) || empty($data['slug'])) {
            $data['slug'] = Utility::generateSlug($data['title']);
        }
        
        // Ensure slug is unique
        $data['slug'] = Utility::generateUniqueSlug($data['slug'], 'pages', 'slug');
        
        // Set created_at
        $data['created_at'] = date('Y-m-d H:i:s');
        
        return $this->db->insert('pages', $data);
    }
    
    /**
     * Update page
     * @param int $pageId Page ID
     * @param array $data Page data
     * @return bool Success or failure
     */
    public function update($pageId, $data) {
        // Generate slug if not provided
        if (isset($data['title']) && (!isset($data['slug']) || empty($data['slug']))) {
            $data['slug'] = Utility::generateSlug($data['title']);
        }
        
        // Ensure slug is unique
        if (isset($data['slug'])) {
            $data['slug'] = Utility::generateUniqueSlug($data['slug'], 'pages', 'slug', $pageId);
        }
        
        // Set updated_at
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        return $this->db->update('pages', $data, 'page_id = ?', [$pageId]);
    }
    
    /**
     * Delete page
     * @param int $pageId Page ID
     * @return bool Success or failure
     */
    public function delete($pageId) {
        return $this->db->delete('pages', 'page_id = ?', [$pageId]);
    }
    
    /**
     * Get navigation pages
     * @return array Navigation pages
     */
    public function getNavigation() {
        $sql = "SELECT * FROM pages WHERE status = 'active' AND show_in_menu = 1 ORDER BY display_order ASC, title ASC";
        return $this->db->fetchAll($sql);
    }
    
    /**
     * Get count by status
     * @param string $status Status
     * @return int Count
     */
    public function getCountByStatus($status) {
        return $this->db->count('pages', 'status = ?', [$status]);
    }
}