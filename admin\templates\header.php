<?php
// Initialize notification counters
$pendingReviews = 0;
$pendingCoachings = 0;
$newEnquiries = 0;
$recentEnquiries = [];

// Get pending reviews count if Review class exists
if (class_exists('Review')) {
    try {
        $reviewObj = new Review();
        $pendingReviews = $reviewObj->getCountByStatus('pending');
    } catch (Exception $e) {
        // Silently handle the error
    }
}

// Get pending coaching centers count if CoachingCenter class exists
if (class_exists('CoachingCenter')) {
    try {
        $coachingObj = new CoachingCenter();
        $pendingCoachings = $coachingObj->getCountByStatus('pending');
    } catch (Exception $e) {
        // Silently handle the error
    }
}

// Get new enquiries count if Enquiry class exists
if (class_exists('Enquiry')) {
    try {
        $enquiryObj = new Enquiry();
        $newEnquiries = $enquiryObj->getCountByStatus('unread');
        $recentEnquiries = $enquiryObj->getRecent(3);
    } catch (Exception $e) {
        // Silently handle the error
    }
}

// Ensure adminInfo has required fields
if (!isset($adminInfo['first_name']) && !isset($adminInfo['last_name'])) {
    $adminInfo['name'] = isset($adminInfo['username']) ? $adminInfo['username'] : 'Admin';
} else {
    $adminInfo['name'] = trim((isset($adminInfo['first_name']) ? $adminInfo['first_name'] : '') . ' ' . 
                             (isset($adminInfo['last_name']) ? $adminInfo['last_name'] : ''));
}

if (!isset($adminInfo['role'])) {
    $adminInfo['role'] = 'Administrator';
}
?>
<div class="admin-header">
    <div class="header-search">
        <i class="fas fa-search"></i>
        <input type="text" class="form-control" placeholder="Search...">
    </div>
    
    <div class="header-actions">
        <div class="header-action-item">
            <button class="header-action-btn" id="notificationsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-bell"></i>
                <?php if ($pendingReviews > 0 || $pendingCoachings > 0): ?>
                    <span class="notification-badge"><?php echo $pendingReviews + $pendingCoachings; ?></span>
                <?php endif; ?>
            </button>
            <div class="dropdown-menu dropdown-menu-end" aria-labelledby="notificationsDropdown">
                <h6 class="dropdown-header">Notifications</h6>
                <?php if ($pendingReviews > 0): ?>
                    <a class="dropdown-item" href="coaching-reviews.php?status=pending">
                        <i class="fas fa-star text-warning"></i>
                        <?php echo $pendingReviews; ?> reviews pending approval
                    </a>
                <?php endif; ?>
                <?php if ($pendingCoachings > 0): ?>
                    <a class="dropdown-item" href="coaching-centers.php?status=pending">
                        <i class="fas fa-building text-primary"></i>
                        <?php echo $pendingCoachings; ?> coaching centers pending approval
                    </a>
                <?php endif; ?>
                <?php if ($newEnquiries > 0): ?>
                    <a class="dropdown-item" href="enquiries.php?status=new">
                        <i class="fas fa-envelope text-info"></i>
                        <?php echo $newEnquiries; ?> new enquiries this week
                    </a>
                <?php endif; ?>
                <?php if ($pendingReviews == 0 && $pendingCoachings == 0 && $newEnquiries == 0): ?>
                    <div class="dropdown-item text-center">
                        <i class="fas fa-check-circle text-success"></i>
                        No new notifications
                    </div>
                <?php endif; ?>
                <div class="dropdown-divider"></div>
                <a class="dropdown-item text-center" href="notifications.php">
                    View all notifications
                </a>
            </div>
        </div>
        
        <div class="header-action-item">
            <button class="header-action-btn" id="messagesDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-envelope"></i>
                <?php if ($newEnquiries > 0): ?>
                    <span class="notification-badge"><?php echo $newEnquiries; ?></span>
                <?php endif; ?>
            </button>
            <div class="dropdown-menu dropdown-menu-end" aria-labelledby="messagesDropdown">
                <h6 class="dropdown-header">Messages</h6>
                <?php if (!empty($recentEnquiries)): ?>
                    <?php foreach (array_slice($recentEnquiries, 0, 3) as $enquiry): ?>
                        <a class="dropdown-item" href="enquiry-view.php?id=<?php echo $enquiry['enquiry_id']; ?>">
                            <div class="d-flex align-items-center">
                                <div class="me-2">
                                    <img src="<?php echo isset($enquiry['profile_image']) ? getUploadUrl($enquiry['profile_image']) : getAssetUrl('images/dummy/user.jpg'); ?>" alt="<?php echo $enquiry['name']; ?>" width="40" height="40" class="rounded-circle">
                                </div>
                                <div>
                                    <div class="fw-bold"><?php echo $enquiry['name']; ?></div>
                                    <div class="small text-truncate" style="max-width: 200px;"><?php echo substr($enquiry['message'], 0, 30); ?>...</div>
                                    <div class="small text-muted"><?php echo timeAgo($enquiry['created_at']); ?></div>
                                </div>
                            </div>
                        </a>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="dropdown-item text-center">
                        <i class="fas fa-inbox text-muted"></i>
                        No new messages
                    </div>
                <?php endif; ?>
                <div class="dropdown-divider"></div>
                <a class="dropdown-item text-center" href="enquiries.php">
                    View all messages
                </a>
            </div>
        </div>
        
        <div class="header-action-item">
            <div class="user-dropdown" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <div class="user-avatar">
                    <img src="<?php echo isset($adminInfo['profile_image']) ? getUploadUrl($adminInfo['profile_image']) : getAssetUrl('images/dummy/admin.jpg'); ?>" alt="<?php echo $adminInfo['name']; ?>">
                </div>
                <div class="user-info d-none d-md-block">
                    <div class="user-name"><?php echo $adminInfo['name']; ?></div>
                    <div class="user-role"><?php echo $adminInfo['role']; ?></div>
                </div>
            </div>
            <div class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                <h6 class="dropdown-header">Hello, <?php echo $adminInfo['name']; ?>!</h6>
                <a class="dropdown-item" href="profile.php">
                    <i class="fas fa-user"></i> My Profile
                </a>
                <a class="dropdown-item" href="settings.php">
                    <i class="fas fa-cog"></i> Account Settings
                </a>
                <div class="dropdown-divider"></div>
                <a class="dropdown-item" href="<?php echo getBaseUrl(); ?>" target="_blank">
                    <i class="fas fa-external-link-alt"></i> View Website
                </a>
                <a class="dropdown-item" href="logout.php">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </div>
        </div>
    </div>
</div>