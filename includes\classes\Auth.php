<?php
/**
 * Auth Class
 * Handles user authentication and authorization
 */
class Auth {
    /**
     * Check if user is logged in
     * @return bool True if logged in
     */
    public static function isLoggedIn() {
        return isset($_SESSION['user_id']);
    }
    
    /**
     * Check if user is logged in as admin
     * @return bool True if logged in as admin
     */
    public static function isAdminLoggedIn() {
        return self::isLoggedIn() && isset($_SESSION['user_type']) && $_SESSION['user_type'] === 'admin';
    }
    
    /**
     * Check if user is logged in as coaching owner
     * @return bool True if logged in as coaching owner
     */
    public static function isCoachingOwnerLoggedIn() {
        return self::isLoggedIn() && isset($_SESSION['user_type']) && $_SESSION['user_type'] === 'coaching_owner';
    }
    
    /**
     * Require user to be logged in
     * @param string $redirect URL to redirect to if not logged in
     */
    public static function requireLogin($redirect = 'login.php') {
        if (!self::isLoggedIn()) {
            redirect($redirect);
        }
    }
    
    /**
     * Require user to be logged in as admin
     * @param string $redirect URL to redirect to if not logged in as admin
     */
    public static function requireAdmin($redirect = 'login.php') {
        if (!self::isAdminLoggedIn()) {
            redirect($redirect);
        }
    }
    
    /**
     * Require user to be logged in as coaching owner
     * @param string $redirect URL to redirect to if not logged in as coaching owner
     */
    public static function requireCoachingOwner($redirect = 'login.php') {
        if (!self::isCoachingOwnerLoggedIn()) {
            redirect($redirect);
        }
    }
    
    /**
     * Logout the user
     */
    public static function logout() {
        // Unset all session variables
        $_SESSION = [];
        
        // Delete the session cookie
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(
                session_name(),
                '',
                time() - 42000,
                $params["path"],
                $params["domain"],
                $params["secure"],
                $params["httponly"]
            );
        }
        
        // Destroy the session
        session_destroy();
    }
    
    /**
     * Generate CSRF token
     * @return string CSRF token
     */
    public static function generateCsrfToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        
        return $_SESSION['csrf_token'];
    }
    
    /**
     * Verify CSRF token
     * @param string $token CSRF token to verify
     * @return bool True if token is valid
     */
    public static function verifyCsrfToken($token) {
        if (!isset($_SESSION['csrf_token']) || empty($token)) {
            return false;
        }
        
        return hash_equals($_SESSION['csrf_token'], $token);
    }
    
    /**
     * Get the current user ID
     * @return int|null User ID if logged in, null otherwise
     */
    public static function getUserId() {
        return isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;
    }
}