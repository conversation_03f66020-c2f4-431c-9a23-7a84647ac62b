<?php
/**
 * Location Class
 * Handles states, cities, and locations
 */
class Location {
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = Database::getInstance();
    }

    /**
     * Get city by slug (SEO-friendly)
     * @param string $slug City slug
     * @return array|null City data
     */
    public function getCityBySlug($slug) {
        return $this->db->fetchRow(
            "SELECT c.*, s.state_name
             FROM cities c
             LEFT JOIN states s ON c.state_id = s.state_id
             WHERE c.slug = ?",
            [$slug]
        );
    }
    
    /**
     * Add a new state
     * @param array $data State data
     * @return int|bool State ID or false on failure
     */
    public function addState($data) {
        return $this->db->insert('states', $data);
    }
    
    /**
     * Update a state
     * @param int $stateId State ID
     * @param array $data State data
     * @return bool True if update successful
     */
    public function updateState($stateId, $data) {
        return $this->db->update('states', $data, 'state_id = ?', [$stateId]);
    }
    
    /**
     * Delete a state
     * @param int $stateId State ID
     * @return bool True if deletion successful
     */
    public function deleteState($stateId) {
        // Check if state has cities
        $cityCount = $this->db->count('cities', 'state_id = ?', [$stateId]);
        
        if ($cityCount > 0) {
            return false;
        }
        
        // Check if state has coaching centers
        $coachingCount = $this->db->count('coaching_centers', 'state_id = ?', [$stateId]);
        
        if ($coachingCount > 0) {
            return false;
        }
        
        return $this->db->delete('states', 'state_id = ?', [$stateId]);
    }
    
    /**
     * Get state by ID
     * @param int $stateId State ID
     * @return array|null State data
     */
    public function getStateById($stateId) {
        return $this->db->fetchRow(
            "SELECT * FROM states WHERE state_id = ?",
            [$stateId]
        );
    }
    
    /**
     * Get all states
     * @param bool $activeOnly Get only active states
     * @return array States
     */
    public function getAllStates($activeOnly = true) {
        $where = $activeOnly ? "WHERE status = 'active'" : "";
        
        return $this->db->fetchAll(
            "SELECT * FROM states
             {$where}
             ORDER BY state_name ASC"
        );
    }
    
    /**
     * Add a new city
     * @param array $data City data
     * @return int|bool City ID or false on failure
     */
    public function addCity($data) {
        return $this->db->insert('cities', $data);
    }
    
    /**
     * Update a city
     * @param int $cityId City ID
     * @param array $data City data
     * @return bool True if update successful
     */
    public function updateCity($cityId, $data) {
        return $this->db->update('cities', $data, 'city_id = ?', [$cityId]);
    }
    
    /**
     * Delete a city
     * @param int $cityId City ID
     * @return bool True if deletion successful
     */
    public function deleteCity($cityId) {
        // Check if city has locations
        $locationCount = $this->db->count('locations', 'city_id = ?', [$cityId]);
        
        if ($locationCount > 0) {
            return false;
        }
        
        // Check if city has coaching centers
        $coachingCount = $this->db->count('coaching_centers', 'city_id = ?', [$cityId]);
        
        if ($coachingCount > 0) {
            return false;
        }
        
        return $this->db->delete('cities', 'city_id = ?', [$cityId]);
    }
    
    /**
     * Get city by ID
     * @param int $cityId City ID
     * @return array|null City data
     */
    public function getCityById($cityId) {
        return $this->db->fetchRow(
            "SELECT c.*, s.state_name
             FROM cities c
             LEFT JOIN states s ON c.state_id = s.state_id
             WHERE c.city_id = ?",
            [$cityId]
        );
    }
    
    /**
     * Get all cities
     * @param bool $activeOnly Get only active cities
     * @return array Cities
     */
    public function getAllCities($activeOnly = true) {
        $where = $activeOnly ? "WHERE c.status = 'active'" : "";
        
        return $this->db->fetchAll(
            "SELECT c.*, s.state_name
             FROM cities c
             LEFT JOIN states s ON c.state_id = s.state_id
             {$where}
             ORDER BY c.city_name ASC"
        );
    }
    
    /**
     * Get cities by state
     * @param int $stateId State ID
     * @param bool $activeOnly Get only active cities
     * @return array Cities
     */
    public function getCitiesByState($stateId, $activeOnly = true) {
        $where = $activeOnly ? "WHERE c.state_id = ? AND c.status = 'active'" : "WHERE c.state_id = ?";
        
        return $this->db->fetchAll(
            "SELECT c.*, s.state_name
             FROM cities c
             LEFT JOIN states s ON c.state_id = s.state_id
             {$where}
             ORDER BY c.city_name ASC",
            [$stateId]
        );
    }
    
    /**
     * Add a new location
     * @param array $data Location data
     * @return int|bool Location ID or false on failure
     */
    public function addLocation($data) {
        return $this->db->insert('locations', $data);
    }
    
    /**
     * Update a location
     * @param int $locationId Location ID
     * @param array $data Location data
     * @return bool True if update successful
     */
    public function updateLocation($locationId, $data) {
        return $this->db->update('locations', $data, 'location_id = ?', [$locationId]);
    }
    
    /**
     * Delete a location
     * @param int $locationId Location ID
     * @return bool True if deletion successful
     */
    public function deleteLocation($locationId) {
        // Check if location has coaching centers
        $coachingCount = $this->db->count('coaching_centers', 'location_id = ?', [$locationId]);
        
        if ($coachingCount > 0) {
            return false;
        }
        
        return $this->db->delete('locations', 'location_id = ?', [$locationId]);
    }
    
    /**
     * Delete a location (alias for deleteLocation)
     * @param int $locationId Location ID
     * @return bool True if deletion successful
     */
    public function delete($locationId) {
        return $this->deleteLocation($locationId);
    }
    
    /**
     * Get location by ID
     * @param int $locationId Location ID
     * @return array|null Location data
     */
    public function getLocationById($locationId) {
        return $this->db->fetchRow(
            "SELECT l.*, c.city_name, s.state_name
             FROM locations l
             LEFT JOIN cities c ON l.city_id = c.city_id
             LEFT JOIN states s ON c.state_id = s.state_id
             WHERE l.location_id = ?",
            [$locationId]
        );
    }
    
    /**
     * Get location by ID (alias for getLocationById)
     * @param int $locationId Location ID
     * @return array|null Location data
     */
    public function getById($locationId) {
        return $this->getLocationById($locationId);
    }
    
    /**
     * Get all locations
     * @param bool $activeOnly Get only active locations
     * @return array Locations
     */
    public function getAllLocations($activeOnly = true) {
        $where = $activeOnly ? "WHERE l.status = 'active'" : "";
        
        return $this->db->fetchAll(
            "SELECT l.*, c.city_name, s.state_name
             FROM locations l
             LEFT JOIN cities c ON l.city_id = c.city_id
             LEFT JOIN states s ON c.state_id = s.state_id
             {$where}
             ORDER BY l.location_name ASC"
        );
    }
    
    /**
     * Get all locations with filters
     * @param array $filters Filters to apply
     * @param int $page Page number
     * @param int $limit Records per page
     * @return array Locations and pagination info
     */
    public function getAll($filters = [], $page = 1, $limit = 10) {
        $where = [];
        $params = [];
        
        // Apply filters
        if (isset($filters['status']) && !empty($filters['status'])) {
            $where[] = "l.status = ?";
            $params[] = $filters['status'];
        } else {
            $where[] = "l.status = 'active'";
        }
        
        if (isset($filters['city_id']) && !empty($filters['city_id'])) {
            $where[] = "l.city_id = ?";
            $params[] = $filters['city_id'];
        }
        
        if (isset($filters['search']) && !empty($filters['search'])) {
            $where[] = "(l.location_name LIKE ? OR c.city_name LIKE ? OR s.state_name LIKE ?)";
            $params[] = "%{$filters['search']}%";
            $params[] = "%{$filters['search']}%";
            $params[] = "%{$filters['search']}%";
        }
        
        $whereClause = !empty($where) ? "WHERE " . implode(' AND ', $where) : "";
        
        // Count total records
        $countSql = "SELECT COUNT(*) as count 
                     FROM locations l 
                     LEFT JOIN cities c ON l.city_id = c.city_id 
                     LEFT JOIN states s ON c.state_id = s.state_id 
                     {$whereClause}";
        $countResult = $this->db->fetchRow($countSql, $params);
        $totalCount = $countResult['count'] ?? 0;
        $totalPages = ceil($totalCount / $limit);
        
        // Ensure page is within valid range
        $page = max(1, min($page, $totalPages > 0 ? $totalPages : 1));
        $offset = ($page - 1) * $limit;
        
        // Get locations
        $sql = "SELECT l.*, c.city_name, s.state_name 
                FROM locations l 
                LEFT JOIN cities c ON l.city_id = c.city_id 
                LEFT JOIN states s ON c.state_id = s.state_id 
                {$whereClause} 
                ORDER BY l.location_name ASC 
                LIMIT ?, ?";
        $params[] = $offset;
        $params[] = $limit;
        
        $locations = $this->db->fetchAll($sql, $params);
        
        return [
            'locations' => $locations,
            'total_count' => $totalCount,
            'total_pages' => $totalPages,
            'current_page' => $page
        ];
    }
    
    /**
     * Get locations by city
     * @param int $cityId City ID
     * @param bool $activeOnly Get only active locations
     * @return array Locations
     */
    public function getLocationsByCity($cityId, $activeOnly = true) {
        $where = $activeOnly ? "WHERE l.city_id = ? AND l.status = 'active'" : "WHERE l.city_id = ?";
        
        return $this->db->fetchAll(
            "SELECT l.*, c.city_name, s.state_name
             FROM locations l
             LEFT JOIN cities c ON l.city_id = c.city_id
             LEFT JOIN states s ON c.state_id = s.state_id
             {$where}
             ORDER BY l.location_name ASC",
            [$cityId]
        );
    }
    
    /**
     * Get states with coaching count
     * @return array States with coaching count
     */
    public function getStatesWithCount() {
        return $this->db->fetchAll(
            "SELECT s.*, COUNT(c.coaching_id) as coaching_count
             FROM states s
             LEFT JOIN coaching_centers c ON s.state_id = c.state_id AND c.status = 'active'
             WHERE s.status = 'active'
             GROUP BY s.state_id
             ORDER BY coaching_count DESC, s.state_name ASC"
        );
    }
    
    /**
     * Get cities with coaching count
     * @param int $stateId State ID (optional)
     * @return array Cities with coaching count
     */
    public function getCitiesWithCount($stateId = null) {
        $where = "WHERE c.status = 'active'";
        $params = [];
        
        if ($stateId !== null) {
            $where .= " AND c.state_id = ?";
            $params[] = $stateId;
        }
        
        return $this->db->fetchAll(
            "SELECT c.*, s.state_name, COUNT(cc.coaching_id) as coaching_count
             FROM cities c
             LEFT JOIN states s ON c.state_id = s.state_id
             LEFT JOIN coaching_centers cc ON c.city_id = cc.city_id AND cc.status = 'active'
             {$where}
             GROUP BY c.city_id
             ORDER BY coaching_count DESC, c.city_name ASC",
            $params
        );
    }
    
    /**
     * Get popular cities
     * @param int $limit Number of cities to get
     * @return array Popular cities
     */
    public function getPopularCities($limit = 6) {
        return $this->db->fetchAll(
            "SELECT c.*, s.state_name, COUNT(cc.coaching_id) as coaching_count
             FROM cities c
             LEFT JOIN states s ON c.state_id = s.state_id
             LEFT JOIN coaching_centers cc ON c.city_id = cc.city_id AND cc.status = 'active'
             WHERE c.status = 'active'
             GROUP BY c.city_id
             ORDER BY coaching_count DESC, c.city_name ASC
             LIMIT ?",
            [$limit]
        );
    }
    
    /**
     * Get state dropdown options
     * @param bool $includeEmpty Include empty option
     * @param bool $activeOnly Get only active states
     * @return array State dropdown options
     */
    public function getStateOptions($includeEmpty = true, $activeOnly = true) {
        $states = $this->getAllStates($activeOnly);
        $options = [];
        
        if ($includeEmpty) {
            $options[] = [
                'value' => '',
                'text' => '-- Select State --'
            ];
        }
        
        foreach ($states as $state) {
            $options[] = [
                'value' => $state['state_id'],
                'text' => $state['state_name']
            ];
        }
        
        return $options;
    }
    
    /**
     * Get city dropdown options
     * @param int $stateId State ID (optional)
     * @param bool $includeEmpty Include empty option
     * @param bool $activeOnly Get only active cities
     * @return array City dropdown options
     */
    public function getCityOptions($stateId = null, $includeEmpty = true, $activeOnly = true) {
        $cities = $stateId !== null ? $this->getCitiesByState($stateId, $activeOnly) : $this->getAllCities($activeOnly);
        $options = [];
        
        if ($includeEmpty) {
            $options[] = [
                'value' => '',
                'text' => '-- Select City --'
            ];
        }
        
        foreach ($cities as $city) {
            $options[] = [
                'value' => $city['city_id'],
                'text' => $city['city_name']
            ];
        }
        
        return $options;
    }
    
    /**
     * Get location dropdown options
     * @param int $cityId City ID (optional)
     * @param bool $includeEmpty Include empty option
     * @param bool $activeOnly Get only active locations
     * @return array Location dropdown options
     */
    public function getLocationOptions($cityId = null, $includeEmpty = true, $activeOnly = true) {
        $locations = $cityId !== null ? $this->getLocationsByCity($cityId, $activeOnly) : $this->getAllLocations($activeOnly);
        $options = [];
        
        if ($includeEmpty) {
            $options[] = [
                'value' => '',
                'text' => '-- Select Location --'
            ];
        }
        
        foreach ($locations as $location) {
            $options[] = [
                'value' => $location['location_id'],
                'text' => $location['location_name']
            ];
        }
        
        return $options;
    }
}