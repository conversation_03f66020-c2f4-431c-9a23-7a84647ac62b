&lt;?php
/**
 * Connection Test Script
 */
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../logs/test_connection.log')

require_once __DIR__ . '/../includes/autoload.php';

try {
    echo "&lt;h1&gt;Connection Test&lt;/h1&gt;";
    
    // Test database connection
    $db = Database::getInstance()->getConnection();
    echo "&lt;p style='color:green'&gt;✓ Database connection successful&lt;/p&gt;";
    
    // Test settings
    $settings = Settings::getInstance();
    echo "&lt;p style='color:green'&gt;✓ Settings loaded successfully&lt;/p&gt;";
    
    // Test user class
    $user = new User();
    echo "&lt;p style='color:green'&gt;✓ User class loaded successfully&lt;/p&gt;";
    
    echo "&lt;p&gt;All tests passed successfully!&lt;/p&gt;";
    
} catch (Exception $e) {
    die("&lt;div style='color:red;border:2px solid red;padding:20px;'&gt;" .
        "&lt;h2&gt;Connection Error&lt;/h2&gt;" .
        "&lt;p&gt;&lt;strong&gt;Error:&lt;/strong&gt; " . htmlspecialchars($e->getMessage()) . "&lt;/p&gt;" .
        "&lt;p&gt;&lt;strong&gt;File:&lt;/strong&gt; " . htmlspecialchars($e->getFile()) . "&lt;/p&gt;" .
        "&lt;p&gt;&lt;strong&gt;Line:&lt;/strong&gt; " . htmlspecialchars($e->getLine()) . "&lt;/p&gt;" .
        "&lt;/div&gt;");
}
