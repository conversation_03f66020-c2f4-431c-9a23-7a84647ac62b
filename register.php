<?php
/**
 * Registration Page
 */
require_once 'includes/autoload.php';

// Get settings
$settings = Settings::getInstance();

// Check if user is already logged in
if (isLoggedIn()) {
    redirect('index.php');
}

// Get redirect URL and user type
$redirect = isset($_GET['redirect']) ? $_GET['redirect'] : '';
$userType = isset($_GET['type']) && in_array($_GET['type'], ['user', 'coaching_owner']) ? $_GET['type'] : 'user';

// Process registration form
if (isPostRequest()) {
    if (checkCSRFToken()) {
        // Get form data
        $username = $_POST['username'];
        $email = $_POST['email'];
        $password = $_POST['password'];
        $confirmPassword = $_POST['confirm_password'];
        $firstName = $_POST['first_name'];
        $lastName = $_POST['last_name'];
        $phone = $_POST['phone'];
        $userType = $_POST['user_type'];
        $agreeTerms = isset($_POST['agree_terms']) ? true : false;
        
        // Validate form data
        $errors = [];
        
        if (empty($username)) {
            $errors['username'] = 'Username is required';
        } elseif (strlen($username) < 3 || strlen($username) > 20) {
            $errors['username'] = 'Username must be between 3 and 20 characters';
        } elseif (!preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
            $errors['username'] = 'Username can only contain letters, numbers, and underscores';
        }
        
        if (empty($email)) {
            $errors['email'] = 'Email is required';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = 'Please enter a valid email address';
        }
        
        if (empty($password)) {
            $errors['password'] = 'Password is required';
        } elseif (strlen($password) < 6) {
            $errors['password'] = 'Password must be at least 6 characters';
        }
        
        if ($password !== $confirmPassword) {
            $errors['confirm_password'] = 'Passwords do not match';
        }
        
        if (empty($firstName)) {
            $errors['first_name'] = 'First name is required';
        }
        
        if (empty($lastName)) {
            $errors['last_name'] = 'Last name is required';
        }
        
        if (!$agreeTerms) {
            $errors['agree_terms'] = 'You must agree to the terms and conditions';
        }
        
        if (empty($errors)) {
            // Register user
            $userData = [
                'username' => $username,
                'email' => $email,
                'password' => $password,
                'first_name' => $firstName,
                'last_name' => $lastName,
                'phone' => $phone,
                'user_type' => $userType,
                'is_verified' => 0,
                'verification_token' => bin2hex(random_bytes(32)),
                'status' => 'active'
            ];
            
            $userId = $user->register($userData);
            
            if ($userId) {
                setFlashMessage('Registration successful! Please check your email to verify your account.', 'success');
                redirect('login.php' . (!empty($redirect) ? '?redirect=' . urlencode($redirect) : ''));
            } else {
                setFlashMessage('Username or email already exists. Please try again with different credentials.', 'danger');
            }
        } else {
            setValidationErrors($errors);
            setOldFormValues($_POST);
        }
    } else {
        setFlashMessage('Invalid form submission. Please try again.', 'danger');
    }
}

// Page title and meta
$pageTitle = 'Register';
$pageDescription = 'Create an account to access your dashboard, save favorite coaching centers, write reviews, and more.';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?php echo getMetaTags($pageTitle . ' - ' . $settings->getSiteName(), $pageDescription); ?>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo getAssetUrl($settings->getSiteFavicon()); ?>" type="image/x-icon">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo getAssetUrl('css/style.css'); ?>">
</head>
<body>
    <!-- Header -->
    <?php include 'templates/header.php'; ?>
    
    <!-- Breadcrumb -->
    <section class="breadcrumb-section">
        <div class="container">
            <?php
            $breadcrumbItems = [
                ['title' => 'Home', 'url' => getBaseUrl()],
                ['title' => 'Register', 'url' => '']
            ];
            echo getBreadcrumb($breadcrumbItems);
            ?>
        </div>
    </section>
    
    <!-- Register Section -->
    <section class="register-section section-padding">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8 col-md-10">
                    <div class="auth-form-container">
                        <div class="auth-header text-center">
                            <h2>Create an Account</h2>
                            <p>Join our community and get access to all features</p>
                            
                            <!-- User Type Tabs -->
                            <div class="user-type-tabs">
                                <a href="register.php<?php echo !empty($redirect) ? '?redirect=' . urlencode($redirect) : ''; ?>" class="user-type-tab <?php echo $userType === 'user' ? 'active' : ''; ?>">
                                    <i class="fas fa-user"></i> Student/Parent
                                </a>
                                <a href="register.php?type=coaching_owner<?php echo !empty($redirect) ? '&redirect=' . urlencode($redirect) : ''; ?>" class="user-type-tab <?php echo $userType === 'coaching_owner' ? 'active' : ''; ?>">
                                    <i class="fas fa-building"></i> Coaching Owner
                                </a>
                            </div>
                        </div>
                        
                        <form action="register.php<?php echo !empty($redirect) ? '?redirect=' . urlencode($redirect) : ''; ?>" method="POST" class="auth-form">
                            <?php echo getCSRFTokenField(); ?>
                            <input type="hidden" name="user_type" value="<?php echo $userType; ?>">
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="first_name">First Name</label>
                                        <input type="text" id="first_name" name="first_name" class="form-control <?php echo hasValidationError('first_name') ? 'is-invalid' : ''; ?>" value="<?php echo getOldFormValue('first_name'); ?>" required>
                                        <?php if (hasValidationError('first_name')): ?>
                                            <div class="invalid-feedback"><?php echo getValidationError('first_name'); ?></div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="last_name">Last Name</label>
                                        <input type="text" id="last_name" name="last_name" class="form-control <?php echo hasValidationError('last_name') ? 'is-invalid' : ''; ?>" value="<?php echo getOldFormValue('last_name'); ?>" required>
                                        <?php if (hasValidationError('last_name')): ?>
                                            <div class="invalid-feedback"><?php echo getValidationError('last_name'); ?></div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group mb-3">
                                <label for="username">Username</label>
                                <input type="text" id="username" name="username" class="form-control <?php echo hasValidationError('username') ? 'is-invalid' : ''; ?>" value="<?php echo getOldFormValue('username'); ?>" required>
                                <?php if (hasValidationError('username')): ?>
                                    <div class="invalid-feedback"><?php echo getValidationError('username'); ?></div>
                                <?php endif; ?>
                                <small class="form-text text-muted">Username can only contain letters, numbers, and underscores.</small>
                            </div>
                            
                            <div class="form-group mb-3">
                                <label for="email">Email Address</label>
                                <input type="email" id="email" name="email" class="form-control <?php echo hasValidationError('email') ? 'is-invalid' : ''; ?>" value="<?php echo getOldFormValue('email'); ?>" required>
                                <?php if (hasValidationError('email')): ?>
                                    <div class="invalid-feedback"><?php echo getValidationError('email'); ?></div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="form-group mb-3">
                                <label for="phone">Phone Number (Optional)</label>
                                <input type="tel" id="phone" name="phone" class="form-control" value="<?php echo getOldFormValue('phone'); ?>">
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="password">Password</label>
                                        <div class="password-field">
                                            <input type="password" id="password" name="password" class="form-control <?php echo hasValidationError('password') ? 'is-invalid' : ''; ?>" required>
                                            <span class="password-toggle"><i class="fas fa-eye"></i></span>
                                            <?php if (hasValidationError('password')): ?>
                                                <div class="invalid-feedback"><?php echo getValidationError('password'); ?></div>
                                            <?php endif; ?>
                                        </div>
                                        <small class="form-text text-muted">Password must be at least 6 characters long.</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="confirm_password">Confirm Password</label>
                                        <div class="password-field">
                                            <input type="password" id="confirm_password" name="confirm_password" class="form-control <?php echo hasValidationError('confirm_password') ? 'is-invalid' : ''; ?>" required>
                                            <span class="password-toggle"><i class="fas fa-eye"></i></span>
                                            <?php if (hasValidationError('confirm_password')): ?>
                                                <div class="invalid-feedback"><?php echo getValidationError('confirm_password'); ?></div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group mb-3">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input <?php echo hasValidationError('agree_terms') ? 'is-invalid' : ''; ?>" id="agree_terms" name="agree_terms" required>
                                    <label class="form-check-label" for="agree_terms">I agree to the <a href="terms.php" target="_blank">Terms and Conditions</a> and <a href="privacy.php" target="_blank">Privacy Policy</a></label>
                                    <?php if (hasValidationError('agree_terms')): ?>
                                        <div class="invalid-feedback"><?php echo getValidationError('agree_terms'); ?></div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <div class="form-group mb-3">
                                <button type="submit" class="btn btn-primary w-100">Register</button>
                            </div>
                            
                            <div class="auth-footer text-center">
                                <p>Already have an account? <a href="login.php<?php echo !empty($redirect) ? '?redirect=' . urlencode($redirect) : ''; ?>">Login</a></p>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Footer -->
    <?php include 'templates/footer.php'; ?>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="<?php echo getAssetUrl('js/main.js'); ?>"></script>
</body>
</html>