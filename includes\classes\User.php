<?php
/**
 * User Class
 * Handles user authentication and management
 */
class User {
    private $db;
    private $userData = null;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = Database::getInstance();
        $this->initSession();
    }
    
    /**
     * Initialize user session
     */
    private function initSession() {
        if (isset($_SESSION['user_id'])) {
            $this->userData = $this->db->fetchRow(
                "SELECT * FROM users WHERE user_id = ? AND status = 'active'",
                [$_SESSION['user_id']]
            );
            
            if (!$this->userData) {
                $this->logout();
            }
        }
    }
    
    /**
     * Check if user is logged in
     * @return bool True if user is logged in
     */
    public function isLoggedIn() {
        return $this->userData !== null;
    }
    
    /**
     * Check if user is admin
     * @return bool True if user is admin
     */
    public function isAdmin() {
        return $this->isLoggedIn() && $this->userData['user_type'] === 'admin';
    }
    
    /**
     * Check if user is coaching owner
     * @return bool True if user is coaching owner
     */
    public function isCoachingOwner() {
        return $this->isLoggedIn() && $this->userData['user_type'] === 'coaching_owner';
    }
    
    /**
     * Get user data
     * @param string $field Field name (optional)
     * @return mixed User data or specific field value
     */
    public function getUserData($field = null) {
        if ($field !== null) {
            return $this->userData[$field] ?? null;
        }
        
        return $this->userData;
    }
    
    /**
     * Register a new user
     * @param array $data User data
     * @return int|bool User ID or false on failure
     */
    public function register($data) {
        // Check if username or email already exists
        $existingUser = $this->db->fetchRow(
            "SELECT user_id FROM users WHERE username = ? OR email = ?",
            [$data['username'], $data['email']]
        );
        
        if ($existingUser) {
            return false;
        }
        
        // Hash password
        $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        
        // Generate verification token
        $data['verification_token'] = bin2hex(random_bytes(32));
        
        // Insert user
        $userId = $this->db->insert('users', $data);
        
        if ($userId) {
            // Send verification email
            $this->sendVerificationEmail($data['email'], $data['verification_token']);
            return $userId;
        }
        
        return false;
    }
    
    /**
     * Login user
     * @param string $username Username or email
     * @param string $password Password
     * @param bool $remember Remember me
     * @return bool True if login successful
     */
    public function login($username, $password, $remember = false, $user_type = null) {
        // Log login attempt with more details
        error_log(date('Y-m-d H:i:s') . " - Login attempt for username/email: $username, type: $user_type\n", 3, BASE_PATH . '/logs/login.log');
        
        // Get user by username or email
        $user = $this->db->fetchRow(
            "SELECT * FROM users WHERE (username = ? OR email = ?) AND status = 'active'",
            [$username, $username]
        );
        
        if (!$user) {
            error_log(date('Y-m-d H:i:s') . " - User not found or not active: $username\n", 3, BASE_PATH . '/logs/login.log');
            return false;
        }
        
        // Verify password
        if (!password_verify($password, $user['password'])) {
            error_log(date('Y-m-d H:i:s') . " - Password verification failed for user: $username\n", 3, BASE_PATH . '/logs/login.log');
            return false;
        }
        
        // Check if user is verified
        if (!$user['is_verified']) {
            error_log(date('Y-m-d H:i:s') . " - User not verified: $username\n", 3, BASE_PATH . '/logs/login.log');
            return false;
        }
        
        // Set session
        $_SESSION['user_id'] = $user['user_id'];
        $_SESSION['user_type'] = $user_type ?: $user['user_type'];
        $this->userData = $user;
        
        // Update last login
        $this->db->update('users', ['last_login' => date('Y-m-d H:i:s')], 'user_id = ?', [$user['user_id']]);
        
        // Set remember me cookie
        if ($remember) {
            $token = bin2hex(random_bytes(32));
            $expiry = time() + (30 * 24 * 60 * 60); // 30 days
            
            $this->db->insert('user_tokens', [
                'user_id' => $user['user_id'],
                'token' => $token,
                'expires_at' => date('Y-m-d H:i:s', $expiry)
            ]);
            
            setcookie('remember_token', $token, $expiry, '/', '', true, true);
        }
        
        error_log(date('Y-m-d H:i:s') . " - Login successful for user: $username (ID: {$user['user_id']})\n", 3, BASE_PATH . '/logs/login.log');
        return true;
    }
    
    /**
     * Logout user
     */
    public function logout() {
        // Clear session
        unset($_SESSION['user_id']);
        $this->userData = null;
        
        // Clear remember me cookie
        if (isset($_COOKIE['remember_token'])) {
            $this->db->delete('user_tokens', 'token = ?', [$_COOKIE['remember_token']]);
            setcookie('remember_token', '', time() - 3600, '/', '', true, true);
        }
    }
    
    /**
     * Verify user email
     * @param string $token Verification token
     * @return bool True if verification successful
     */
    public function verifyEmail($token) {
        $user = $this->db->fetchRow(
            "SELECT user_id FROM users WHERE verification_token = ? AND is_verified = 0",
            [$token]
        );
        
        if (!$user) {
            return false;
        }
        
        return $this->db->update('users', [
            'is_verified' => 1,
            'verification_token' => null
        ], 'user_id = ?', [$user['user_id']]);
    }
    
    /**
     * Send verification email
     * @param string $email User email
     * @param string $token Verification token
     * @return bool True if email sent
     */
    private function sendVerificationEmail($email, $token) {
        $verificationUrl = BASE_URL . 'verify.php?token=' . $token;
        $subject = 'Verify Your Email Address';
        
        $message = "
            <html>
            <head>
                <title>Verify Your Email Address</title>
            </head>
            <body>
                <h2>Thank you for registering!</h2>
                <p>Please click the link below to verify your email address:</p>
                <p><a href='{$verificationUrl}'>{$verificationUrl}</a></p>
                <p>If you did not register on our website, please ignore this email.</p>
            </body>
            </html>
        ";
        
        $headers = "MIME-Version: 1.0" . "\r\n";
        $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
        $headers .= "From: " . SITE_NAME . " <" . SITE_EMAIL . ">" . "\r\n";
        
        return mail($email, $subject, $message, $headers);
    }
    
    /**
     * Request password reset
     * @param string $email User email
     * @return bool True if request successful
     */
    public function requestPasswordReset($email) {
        $user = $this->db->fetchRow(
            "SELECT user_id, email FROM users WHERE email = ? AND status = 'active'",
            [$email]
        );
        
        if (!$user) {
            return false;
        }
        
        $token = bin2hex(random_bytes(32));
        $expiry = date('Y-m-d H:i:s', time() + PASSWORD_RESET_EXPIRY);
        
        $updated = $this->db->update('users', [
            'reset_token' => $token,
            'reset_token_expiry' => $expiry
        ], 'user_id = ?', [$user['user_id']]);
        
        if ($updated) {
            return $this->sendPasswordResetEmail($user['email'], $token);
        }
        
        return false;
    }
    
    /**
     * Send password reset email
     * @param string $email User email
     * @param string $token Reset token
     * @return bool True if email sent
     */
    private function sendPasswordResetEmail($email, $token) {
        $resetUrl = BASE_URL . 'reset-password.php?token=' . $token;
        $subject = 'Reset Your Password';
        
        $message = "
            <html>
            <head>
                <title>Reset Your Password</title>
            </head>
            <body>
                <h2>Password Reset Request</h2>
                <p>You have requested to reset your password. Please click the link below to reset your password:</p>
                <p><a href='{$resetUrl}'>{$resetUrl}</a></p>
                <p>This link will expire in 24 hours.</p>
                <p>If you did not request a password reset, please ignore this email.</p>
            </body>
            </html>
        ";
        
        $headers = "MIME-Version: 1.0" . "\r\n";
        $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
        $headers .= "From: " . SITE_NAME . " <" . SITE_EMAIL . ">" . "\r\n";
        
        return mail($email, $subject, $message, $headers);
    }
    
    /**
     * Reset password
     * @param string $token Reset token
     * @param string $password New password
     * @return bool True if password reset successful
     */
    public function resetPassword($token, $password) {
        $user = $this->db->fetchRow(
            "SELECT user_id FROM users WHERE reset_token = ? AND reset_token_expiry > NOW()",
            [$token]
        );
        
        if (!$user) {
            return false;
        }
        
        return $this->db->update('users', [
            'password' => password_hash($password, PASSWORD_DEFAULT),
            'reset_token' => null,
            'reset_token_expiry' => null
        ], 'user_id = ?', [$user['user_id']]);
    }
    
    /**
     * Update user profile
     * @param array $data User data
     * @return bool True if update successful
     */
    public function updateProfile($data) {
        if (!$this->isLoggedIn()) {
            return false;
        }
        
        // Check if email is being changed and if it's already in use
        if (isset($data['email']) && $data['email'] !== $this->userData['email']) {
            $existingUser = $this->db->fetchRow(
                "SELECT user_id FROM users WHERE email = ? AND user_id != ?",
                [$data['email'], $this->userData['user_id']]
            );
            
            if ($existingUser) {
                return false;
            }
        }
        
        // Update user
        $updated = $this->db->update('users', $data, 'user_id = ?', [$this->userData['user_id']]);
        
        if ($updated) {
            // Refresh user data
            $this->userData = $this->db->fetchRow(
                "SELECT * FROM users WHERE user_id = ?",
                [$this->userData['user_id']]
            );
            
            return true;
        }
        
        return false;
    }
    
    /**
     * Change password
     * @param string $currentPassword Current password
     * @param string $newPassword New password
     * @return bool True if password change successful
     */
    public function changePassword($currentPassword, $newPassword) {
        if (!$this->isLoggedIn()) {
            return false;
        }
        
        // Verify current password
        if (!password_verify($currentPassword, $this->userData['password'])) {
            return false;
        }
        
        // Update password
        return $this->db->update('users', [
            'password' => password_hash($newPassword, PASSWORD_DEFAULT)
        ], 'user_id = ?', [$this->userData['user_id']]);
    }
    
    /**
     * Get user by ID
     * @param int $userId User ID
     * @return array|null User data
     */
    public function getUserById($userId) {
        return $this->db->fetchRow(
            "SELECT * FROM users WHERE user_id = ?",
            [$userId]
        );
    }
    
    /**
     * Get users
     * @param array $filters Filters (optional)
     * @param int $page Page number (optional)
     * @param int $limit Records per page (optional)
     * @return array Users
     */
    public function getUsers($filters = [], $page = 1, $limit = RECORDS_PER_PAGE) {
        $where = "1=1";
        $params = [];
        
        // Apply filters
        if (isset($filters['user_type']) && !empty($filters['user_type'])) {
            $where .= " AND user_type = ?";
            $params[] = $filters['user_type'];
        }
        
        if (isset($filters['status']) && !empty($filters['status'])) {
            $where .= " AND status = ?";
            $params[] = $filters['status'];
        }
        
        if (isset($filters['search']) && !empty($filters['search'])) {
            $where .= " AND (username LIKE ? OR email LIKE ? OR first_name LIKE ? OR last_name LIKE ?)";
            $searchTerm = '%' . $filters['search'] . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }
        
        // Get total count
        $totalCount = $this->db->count('users', $where, $params);
        
        // Calculate offset
        $offset = ($page - 1) * $limit;
        
        // Get users
        $sql = "SELECT * FROM users WHERE {$where} ORDER BY created_at DESC LIMIT ?, ?";
        $params[] = $offset;
        $params[] = $limit;
        
        $users = $this->db->fetchAll($sql, $params);
        
        return [
            'users' => $users,
            'total' => $totalCount,
            'page' => $page,
            'limit' => $limit,
            'total_pages' => ceil($totalCount / $limit)
        ];
    }
    
    /**
     * Update user status
     * @param int $userId User ID
     * @param string $status New status
     * @return bool True if status update successful
     */
    public function updateUserStatus($userId, $status) {
        if (!$this->isAdmin()) {
            return false;
        }
        
        return $this->db->update('users', ['status' => $status], 'user_id = ?', [$userId]);
    }
    
    /**
     * Delete user
     * @param int $userId User ID
     * @return bool True if user deleted
     */
    public function deleteUser($userId) {
        if (!$this->isAdmin()) {
            return false;
        }
        
        return $this->db->delete('users', 'user_id = ?', [$userId]);
    }
    
    /**
     * Get total count of users
     * @param string $status Optional status filter (active, inactive)
     * @return int Total count
     */
    public function getTotalCount($status = '') {
        // Log the method call to our new log file
        error_log(date('Y-m-d H:i:s') . " - User::getTotalCount method called with status: " . ($status ?: 'all') . "\n", 3, BASE_PATH . '/logs/admin_dashboard.log');
        
        $where = '';
        $params = [];
        
        if (!empty($status)) {
            $where = 'status = ?';
            $params = [$status];
        }
        
        // Log the SQL query
        $sql = "SELECT COUNT(*) as count FROM users" . (!empty($where) ? " WHERE $where" : "");
        error_log(date('Y-m-d H:i:s') . " - SQL Query: " . $sql . "\n", 3, BASE_PATH . '/logs/admin_dashboard.log');
        
        try {
            return $this->db->count('users', $where, $params);
        } catch (Exception $e) {
            // Log any errors
            error_log(date('Y-m-d H:i:s') . " - Error in User::getTotalCount: " . $e->getMessage() . "\n", 3, BASE_PATH . '/logs/admin_dashboard.log');
            return 0;
        }
    }
    
    /**
     * Get all users
     * @param array $filters Optional filters
     * @param int $page Page number
     * @param int $limit Records per page
     * @return array Users and pagination info
     */
    public function getAll($filters = [], $page = 1, $limit = 10) {
        $where = "1=1";
        $params = [];
        
        // Apply filters
        if (isset($filters['status']) && !empty($filters['status'])) {
            $where .= " AND status = ?";
            $params[] = $filters['status'];
        }
        
        if (isset($filters['role']) && !empty($filters['role'])) {
            $where .= " AND role = ?";
            $params[] = $filters['role'];
        }
        
        if (isset($filters['search']) && !empty($filters['search'])) {
            $where .= " AND (username LIKE ? OR email LIKE ? OR first_name LIKE ? OR last_name LIKE ?)";
            $params[] = "%{$filters['search']}%";
            $params[] = "%{$filters['search']}%";
            $params[] = "%{$filters['search']}%";
            $params[] = "%{$filters['search']}%";
        }
        
        // Count total records
        $totalCount = $this->db->count('users', $where, $params);
        $totalPages = ceil($totalCount / $limit);
        
        // Ensure page is within valid range
        $page = max(1, min($page, $totalPages));
        $offset = ($page - 1) * $limit;
        
        // Get users
        $sql = "SELECT * FROM users WHERE {$where} ORDER BY created_at DESC LIMIT ?, ?";
        $params[] = $offset;
        $params[] = $limit;
        
        $users = $this->db->fetchAll($sql, $params);
        
        return [
            'users' => $users,
            'total_count' => $totalCount,
            'total_pages' => $totalPages,
            'current_page' => $page
        ];
    }
    
    /**
     * Get count of new users in the last X days
     * @param int $days Number of days
     * @return int Count of new users
     */
    public function getNewUsersCount($days = 7) {
        // Log the method call
        error_log(date('Y-m-d H:i:s') . " - User::getNewUsersCount method called with days: " . $days . "\n", 3, BASE_PATH . '/logs/admin_dashboard.log');
        
        $sql = "SELECT COUNT(*) as count FROM users WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)";
        
        try {
            $result = $this->db->fetchRow($sql, [$days]);
            return $result['count'] ?? 0;
        } catch (Exception $e) {
            // Log any errors
            error_log(date('Y-m-d H:i:s') . " - Error in User::getNewUsersCount: " . $e->getMessage() . "\n", 3, BASE_PATH . '/logs/admin_dashboard.log');
            return 0;
        }
    }
}
