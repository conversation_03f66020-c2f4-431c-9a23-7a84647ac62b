<?php
/**
 * Database Class
 * Handles database connections and operations
 */
class Database {
    private $connection;
    private static $instance;
    private $host = DB_HOST;
    private $username = DB_USER;
    private $password = DB_PASS;
    private $database = DB_NAME;
    
    /**
     * Constructor - Creates database connection
     */
    private function __construct() {
        // Log connection details (without password)
        error_log("Database connection: Host={$this->host}, User={$this->username}, DB={$this->database}");
        
        $this->connection = new mysqli($this->host, $this->username, $this->password, $this->database);
        
        if ($this->connection->connect_error) {
            error_log("Database Connection Error: " . $this->connection->connect_error);
            die('Database Connection Error: ' . $this->connection->connect_error);
        }
        
        error_log("Database connection successful");
        $this->connection->set_charset('utf8mb4');
    }
    
    /**
     * Get singleton instance
     * @return Database
     */
    public static function getInstance() {
        if (!self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Get the database connection
     * @return mysqli
     */
    public function getConnection() {
        return $this->connection;
    }
    
    /**
     * Execute a query
     * @param string $sql SQL query
     * @param array $params Parameters for prepared statement
     * @return mysqli_stmt|bool
     */
    public function query($sql, $params = []) {
        // Log the SQL query and parameters
        error_log("SQL Query: " . $sql);
        error_log("Parameters: " . print_r($params, true));
        
        $stmt = $this->connection->prepare($sql);
        
        if (!$stmt) {
            $error = "Query Preparation Error: " . $this->connection->error;
            error_log($error);
            error_log("Failed SQL: " . $sql);
            return false;
        }
        
        if (!empty($params)) {
            $types = '';
            $bindParams = [];
            
            foreach ($params as $param) {
                if (is_int($param)) {
                    $types .= 'i';
                } elseif (is_float($param)) {
                    $types .= 'd';
                } elseif (is_string($param)) {
                    $types .= 's';
                } else {
                    $types .= 'b';
                }
                $bindParams[] = $param;
            }
            
            array_unshift($bindParams, $types);
            
            try {
                call_user_func_array([$stmt, 'bind_param'], $this->refValues($bindParams));
            } catch (Exception $e) {
                $error = "Parameter Binding Error: " . $e->getMessage();
                error_log($error);
                error_log("Failed SQL: " . $sql);
                error_log("Parameters: " . print_r($params, true));
                $stmt->close();
                return false;
            }
        }
        
        try {
            $result = $stmt->execute();
            
            if (!$result) {
                $error = "Query Execution Error: " . $stmt->error;
                error_log($error);
                error_log("Failed SQL: " . $sql);
                error_log("Parameters: " . print_r($params, true));
                $stmt->close();
                return false;
            }
            
            return $stmt;
        } catch (Exception $e) {
            $error = "Query Execution Exception: " . $e->getMessage();
            error_log($error);
            error_log("Failed SQL: " . $sql);
            error_log("Parameters: " . print_r($params, true));
            $stmt->close();
            return false;
        }
    }
    
    /**
     * Helper function for binding parameters by reference
     * @param array $arr
     * @return array
     */
    private function refValues($arr) {
        $refs = [];
        foreach ($arr as $key => $value) {
            $refs[$key] = &$arr[$key];
        }
        return $refs;
    }
    
    /**
     * Fetch a single row
     * @param string $sql SQL query
     * @param array $params Parameters for prepared statement
     * @return array|null
     */
    public function fetchRow($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        
        if (!$stmt) {
            error_log("fetchRow: Query failed: " . $sql);
            return null;
        }
        
        $result = $stmt->get_result();
        
        if (!$result) {
            error_log("fetchRow: get_result failed for query: " . $sql);
            $stmt->close();
            return null;
        }
        
        $row = $result->fetch_assoc();
        $stmt->close();
        return $row;
    }
    
    /**
     * Fetch all rows
     * @param string $sql SQL query
     * @param array $params Parameters for prepared statement
     * @return array
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        
        if (!$stmt) {
            error_log("fetchAll: Query failed: " . $sql);
            return [];
        }
        
        $result = $stmt->get_result();

        if (!$result) {
            error_log("fetchAll: get_result failed for query: " . $sql);
            $stmt->close();
            return [];
        }

        $rows = [];
        while ($row = $result->fetch_assoc()) {
            $rows[] = $row;
        }

        $stmt->close();
        return $rows;
    }
    
    /**
     * Fetch a single value from the first column of the first row
     * @param string $sql SQL query
     * @param array $params Parameters for prepared statement
     * @return mixed|null The value or null if no result
     */
    public function fetchOne($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        
        if (!$stmt) {
            error_log("fetchOne: Query failed: " . $sql);
            return null;
        }
        
        $result = $stmt->get_result();
        
        if ($result && $result->num_rows > 0) {
            $row = $result->fetch_row();
            $value = $row[0];
            $stmt->close();
            return $value;
        }
        
        if ($stmt) {
            $stmt->close();
        }
        return null;
    }
    
    /**
     * Insert data into a table
     * @param string $table Table name
     * @param array $data Associative array of column => value
     * @return int|bool Last insert ID or false on failure
     */
    public function insert($table, $data) {
        $columns = implode(', ', array_keys($data));
        $placeholders = implode(', ', array_fill(0, count($data), '?'));
        
        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        $stmt = $this->query($sql, array_values($data));
        
        if ($stmt) {
            $insertId = $this->connection->insert_id;
            $stmt->close();
            return $insertId;
        }
        
        return false;
    }
    
    /**
     * Update data in a table
     * @param string $table Table name
     * @param array $data Associative array of column => value
     * @param string $where WHERE clause
     * @param array $whereParams Parameters for WHERE clause
     * @return bool Success or failure
     */
    public function update($table, $data, $where, $whereParams = []) {
        if (empty($data)) {
            error_log("Error: Empty data array provided for update to table {$table}");
            return false;
        }
        
        // We'll skip the table existence check for now as it might be causing issues
        // Instead, we'll just log what table we're trying to update
        error_log("Attempting to update table: {$table}");
        
        // Skip column validation for now to simplify the process
        // We'll rely on MySQL to validate the columns
        
        $setClauses = [];
        $params = [];
        
        foreach ($data as $column => $value) {
            $setClauses[] = "{$column} = ?";
            $params[] = $value;
        }
        
        $setClause = implode(', ', $setClauses);
        $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";
        
        // Log the full SQL query with parameter values for debugging
        $debugSql = $sql;
        $debugParams = array_merge($params, $whereParams);
        $debugValues = [];
        
        foreach ($debugParams as $param) {
            if (is_null($param)) {
                $debugValues[] = 'NULL';
            } elseif (is_string($param)) {
                $debugValues[] = "'" . $this->escape($param) . "'";
            } elseif (is_bool($param)) {
                $debugValues[] = $param ? '1' : '0';
            } else {
                $debugValues[] = $param;
            }
        }
        
        // Replace ? with actual values for debugging
        $questionMarkCount = substr_count($debugSql, '?');
        for ($i = 0; $i < $questionMarkCount && $i < count($debugValues); $i++) {
            $pos = strpos($debugSql, '?');
            if ($pos !== false) {
                $debugSql = substr_replace($debugSql, $debugValues[$i], $pos, 1);
            }
        }
        
        error_log("Executing SQL: " . $debugSql);
        
        try {
            $stmt = $this->query($sql, array_merge($params, $whereParams));
            
            if ($stmt) {
                $affectedRows = $stmt->affected_rows;
                $stmt->close();
                
                if ($affectedRows > 0) {
                    error_log("Update successful: {$affectedRows} rows affected in table {$table}");
                    return true;
                } else {
                    error_log("Update had no effect: No rows affected in table {$table}. This could be normal if no data changed.");
                    // Return true even if no rows were affected, as this might be a valid case
                    // where the data didn't change
                    return true;
                }
            } else {
                $error = $this->getLastError();
                error_log("Update failed for table {$table}: " . $error);
                error_log("Failed SQL: " . $debugSql);
                return false;
            }
        } catch (Exception $e) {
            error_log("Exception during update for table {$table}: " . $e->getMessage());
            error_log("Failed SQL: " . $debugSql);
            return false;
        }
    }
    
    /**
     * Delete data from a table
     * @param string $table Table name
     * @param string $where WHERE clause
     * @param array $params Parameters for WHERE clause
     * @return bool Success or failure
     */
    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        $stmt = $this->query($sql, $params);
        
        if ($stmt) {
            $affectedRows = $stmt->affected_rows;
            $stmt->close();
            return $affectedRows > 0;
        }
        
        return false;
    }
    
    /**
     * Count rows in a table
     * @param string $table Table name
     * @param string $where WHERE clause (optional)
     * @param array $params Parameters for WHERE clause (optional)
     * @return int Row count
     */
    public function count($table, $where = '', $params = []) {
        $sql = "SELECT COUNT(*) as count FROM {$table}";
        
        if (!empty($where)) {
            $sql .= " WHERE {$where}";
        }
        
        $row = $this->fetchRow($sql, $params);
        return $row['count'] ?? 0;
    }
    
    /**
     * Begin a transaction
     */
    public function beginTransaction() {
        $this->connection->begin_transaction();
    }
    
    /**
     * Commit a transaction
     */
    public function commit() {
        $this->connection->commit();
    }
    
    /**
     * Rollback a transaction
     */
    public function rollback() {
        $this->connection->rollback();
    }
    
    /**
     * Escape a string
     * @param string $string String to escape
     * @return string Escaped string
     */
    public function escape($string) {
        return $this->connection->real_escape_string($string);
    }
    
    /**
     * Get the last error
     * @return string Last error
     */
    public function getLastError() {
        return $this->connection->error;
    }
    
    /**
     * Close the database connection
     */
    public function close() {
        $this->connection->close();
    }
    
    /**
     * Prevent cloning of the instance
     */
    private function __clone() {}
    
    /**
     * Prevent unserializing of the instance
     */
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}