<?php
/**
 * Admin Dashboard
 */
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    require_once '../includes/autoload.php';
    echo '<div style="background:#ffc;color:#900;padding:8px;margin:8px 0;font-size:14px;">[DEBUG-DASH] session_id=' . session_id() . ' | session_name=' . session_name() . ' | admin_id=' . (isset($_SESSION['admin_id']) ? $_SESSION['admin_id'] : 'NOT SET') . '</div>';
    
    // Debug: Check database connection
    $db = Database::getInstance()->getConnection();
    if (!$db) {
        throw new Exception("Failed to connect to database");
    }
    
    // Debug: Check settings
    $settings = Settings::getInstance();
    if (!$settings) {
        throw new Exception("Failed to initialize settings");
    }

    // Debug: Check user authentication
    $user = new User();
    if (!$user->isLoggedIn() || !$user->isAdmin()) {
        header('Location: index.php?login_required=1');
        exit;
    }

    // Debug: Get admin info
    $adminInfo = $user->getUserData();
    if (!$adminInfo) {
        throw new Exception("Failed to get admin info");
    }

// Get dashboard stats
$coachingObj = new CoachingCenter();
$totalCoachings = $coachingObj->getTotalCount();
$pendingCoachings = $coachingObj->getTotalCount('pending');

$userObj = new User();
$totalUsers = $userObj->getTotalCount();
$newUsers = $userObj->getNewUsersCount(7); // New users in last 7 days

$reviewObj = new Review();
$totalReviews = $reviewObj->getTotalCount();
$pendingReviews = $reviewObj->getTotalCount('pending');

$enquiryObj = new Enquiry();
$totalEnquiries = $enquiryObj->getTotalCount();
$newEnquiries = $enquiryObj->getNewEnquiriesCount(7); // New enquiries in last 7 days

    // Debug: Get recent activities
    $adminObj = new Admin();
    $recentActivities = $adminObj->getRecentActivities(10);
    if (!is_array($recentActivities)) {
        throw new Exception("Failed to get recent activities");
    }

    // Get recent coaching centers
    $recentCoachings = $coachingObj->getRecent(5);

    // Get recent reviews
    $recentReviews = $reviewObj->getRecent(5);

    // Get recent enquiries
    $recentEnquiries = $enquiryObj->getRecent(5);

} catch (Exception $e) {
    die('<div style="color:red;padding:20px;border:2px solid red;margin:20px;">'
        . '<h2>Dashboard Error</h2>'
        . '<p><strong>Error:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>'
        . '<p><strong>File:</strong> ' . htmlspecialchars($e->getFile()) . '</p>'
        . '<p><strong>Line:</strong> ' . htmlspecialchars($e->getLine()) . '</p>'
        . '</div>');
}

// Page title
$pageTitle = 'Dashboard';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Admin Panel | <?php echo $settings->getSiteName(); ?></title>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo getAssetUrl($settings->getSiteFavicon()); ?>" type="image/x-icon">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Chart.js -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.css">
    
    <!-- Admin CSS -->
    <link rel="stylesheet" href="assets/css/admin.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <?php include 'templates/sidebar.php'; ?>
        
        <!-- Main Content -->
        <div class="admin-main" id="adminMain">
            <!-- Header -->
            <?php include 'templates/header.php'; ?>
            
            <!-- Content -->
            <div class="admin-content">
                <div class="page-title">
                    <div>
                        <h1><?php echo $pageTitle; ?></h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="dashboard.php">Home</a></li>
                                <li class="breadcrumb-item active" aria-current="page"><?php echo $pageTitle; ?></li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <button class="btn btn-primary" id="refreshStats">
                            <i class="fas fa-sync-alt"></i> Refresh Stats
                        </button>
                    </div>
                </div>
                
                <!-- Stats Cards -->
                <div class="row">
                    <div class="col-xl-3 col-md-6">
                        <div class="stats-card">
                            <div class="stats-icon bg-gradient-primary">
                                <i class="fas fa-building"></i>
                            </div>
                            <div class="stats-info">
                                <div class="stats-number"><?php echo $totalCoachings; ?></div>
                                <div class="stats-label">Total Coaching Centers</div>
                                <div class="stats-change positive">
                                    <i class="fas fa-arrow-up"></i> 12% from last month
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6">
                        <div class="stats-card">
                            <div class="stats-icon bg-gradient-success">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stats-info">
                                <div class="stats-number"><?php echo $totalUsers; ?></div>
                                <div class="stats-label">Registered Users</div>
                                <div class="stats-change positive">
                                    <i class="fas fa-arrow-up"></i> <?php echo $newUsers; ?> new this week
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6">
                        <div class="stats-card">
                            <div class="stats-icon bg-gradient-warning">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="stats-info">
                                <div class="stats-number"><?php echo $totalReviews; ?></div>
                                <div class="stats-label">Total Reviews</div>
                                <div class="stats-change <?php echo $pendingReviews > 0 ? 'negative' : 'positive'; ?>">
                                    <i class="fas <?php echo $pendingReviews > 0 ? 'fa-exclamation-circle' : 'fa-check-circle'; ?>"></i> 
                                    <?php echo $pendingReviews; ?> pending approval
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6">
                        <div class="stats-card">
                            <div class="stats-icon bg-gradient-info">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="stats-info">
                                <div class="stats-number"><?php echo $totalEnquiries; ?></div>
                                <div class="stats-label">Total Enquiries</div>
                                <div class="stats-change positive">
                                    <i class="fas fa-arrow-up"></i> <?php echo $newEnquiries; ?> new this week
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Charts -->
                <div class="row">
                    <div class="col-lg-8">
                        <div class="admin-card">
                            <div class="admin-card-header">
                                <h2 class="admin-card-title">
                                    <i class="fas fa-chart-line me-2"></i> Website Traffic
                                </h2>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="trafficTimeRange" data-bs-toggle="dropdown" aria-expanded="false">
                                        Last 7 Days
                                    </button>
                                    <ul class="dropdown-menu" aria-labelledby="trafficTimeRange">
                                        <li><a class="dropdown-item active" href="#">Last 7 Days</a></li>
                                        <li><a class="dropdown-item" href="#">Last 30 Days</a></li>
                                        <li><a class="dropdown-item" href="#">Last 90 Days</a></li>
                                        <li><a class="dropdown-item" href="#">This Year</a></li>
                                    </ul>
                                </div>
                            </div>
                            <div class="admin-card-body">
                                <canvas id="trafficChart" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4">
                        <div class="admin-card">
                            <div class="admin-card-header">
                                <h2 class="admin-card-title">
                                    <i class="fas fa-chart-pie me-2"></i> User Distribution
                                </h2>
                            </div>
                            <div class="admin-card-body">
                                <canvas id="userDistributionChart" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Activities and Coaching Centers -->
                <div class="row">
                    <div class="col-lg-6">
                        <div class="admin-card">
                            <div class="admin-card-header">
                                <h2 class="admin-card-title">
                                    <i class="fas fa-history me-2"></i> Recent Activities
                                </h2>
                                <a href="activities.php" class="btn btn-sm btn-outline-primary">View All</a>
                            </div>
                            <div class="admin-card-body p-0">
                                <div class="list-group list-group-flush">
                                    <?php if (!empty($recentActivities)): ?>
                                        <?php foreach ($recentActivities as $activity): ?>
                                            <div class="list-group-item">
                                                <div class="d-flex w-100 justify-content-between">
                                                    <h6 class="mb-1"><?php echo $activity['activity_type'] ?? 'Unknown'; ?></h6>
                                                    <small><?php echo isset($activity['created_at']) ? timeAgo($activity['created_at']) : 'Unknown'; ?></small>
                                                </div>
                                                <p class="mb-1"><?php echo $activity['description'] ?? 'No description'; ?></p>
                                                <small>By: <?php echo $activity['user_name'] ?? 'Unknown'; ?></small>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <div class="list-group-item text-center">
                                            <p class="mb-0">No recent activities found.</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-6">
                        <div class="admin-card">
                            <div class="admin-card-header">
                                <h2 class="admin-card-title">
                                    <i class="fas fa-building me-2"></i> Recent Coaching Centers
                                </h2>
                                <a href="coaching-centers.php" class="btn btn-sm btn-outline-primary">View All</a>
                            </div>
                            <div class="admin-card-body p-0">
                                <div class="table-responsive">
                                    <table class="admin-table">
                                        <thead>
                                            <tr>
                                                <th>Name</th>
                                                <th>Location</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (!empty($recentCoachings)): ?>
                                                <?php foreach ($recentCoachings as $coaching): ?>
                                                    <tr>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                <div class="me-2">
                                                                    <img src="<?php echo getCoachingLogo($coaching['logo']); ?>" alt="<?php echo $coaching['coaching_name']; ?>" width="40" height="40" class="rounded">
                                                                </div>
                                                                <div>
                                                                    <?php echo $coaching['coaching_name']; ?>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td><?php echo $coaching['city_name']; ?>, <?php echo $coaching['state_name']; ?></td>
                                                        <td>
                                                            <span class="status-badge status-<?php echo $coaching['status']; ?>">
                                                                <?php echo ucfirst($coaching['status']); ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <div class="action-buttons">
                                                                <a href="coaching-view.php?id=<?php echo $coaching['coaching_id']; ?>" class="btn btn-action btn-view" title="View">
                                                                    <i class="fas fa-eye"></i>
                                                                </a>
                                                                <a href="coaching-edit.php?id=<?php echo $coaching['coaching_id']; ?>" class="btn btn-action btn-edit" title="Edit">
                                                                    <i class="fas fa-edit"></i>
                                                                </a>
                                                                <a href="coaching-delete.php?id=<?php echo $coaching['coaching_id']; ?>" class="btn btn-action btn-delete" title="Delete" onclick="return confirm('Are you sure you want to delete this coaching center?');">
                                                                    <i class="fas fa-trash"></i>
                                                                </a>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="4" class="text-center">No coaching centers found.</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Reviews and Enquiries -->
                <div class="row">
                    <div class="col-lg-6">
                        <div class="admin-card">
                            <div class="admin-card-header">
                                <h2 class="admin-card-title">
                                    <i class="fas fa-star me-2"></i> Recent Reviews
                                </h2>
                                <a href="reviews.php" class="btn btn-sm btn-outline-primary">View All</a>
                            </div>
                            <div class="admin-card-body p-0">
                                <div class="list-group list-group-flush">
                                    <?php if (!empty($recentReviews)): ?>
                                        <?php foreach ($recentReviews as $review): ?>
                                            <div class="list-group-item">
                                                <div class="d-flex w-100 justify-content-between">
                                                    <h6 class="mb-1"><?php echo $review['coaching_name']; ?></h6>
                                                    <small>
                                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                                            <i class="fas fa-star <?php echo $i <= $review['rating'] ? 'text-warning' : 'text-muted'; ?>"></i>
                                                        <?php endfor; ?>
                                                    </small>
                                                </div>
                                                <p class="mb-1"><?php echo substr($review['review'], 0, 100); ?><?php echo strlen($review['review']) > 100 ? '...' : ''; ?></p>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <small>By: <?php echo $review['user_name']; ?></small>
                                                    <small><?php echo timeAgo($review['created_at']); ?></small>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <div class="list-group-item text-center">
                                            <p class="mb-0">No recent reviews found.</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-6">
                        <div class="admin-card">
                            <div class="admin-card-header">
                                <h2 class="admin-card-title">
                                    <i class="fas fa-envelope me-2"></i> Recent Enquiries
                                </h2>
                                <a href="enquiries.php" class="btn btn-sm btn-outline-primary">View All</a>
                            </div>
                            <div class="admin-card-body p-0">
                                <div class="list-group list-group-flush">
                                    <?php if (!empty($recentEnquiries)): ?>
                                        <?php foreach ($recentEnquiries as $enquiry): ?>
                                            <div class="list-group-item">
                                                <div class="d-flex w-100 justify-content-between">
                                                    <h6 class="mb-1"><?php echo $enquiry['coaching_name']; ?></h6>
                                                    <small><?php echo timeAgo($enquiry['created_at']); ?></small>
                                                </div>
                                                <p class="mb-1"><?php echo substr($enquiry['message'], 0, 100); ?><?php echo strlen($enquiry['message']) > 100 ? '...' : ''; ?></p>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <small>From: <?php echo $enquiry['name']; ?> (<?php echo $enquiry['email']; ?>)</small>
                                                    <span class="status-badge status-<?php echo $enquiry['status']; ?>">
                                                        <?php echo ucfirst($enquiry['status']); ?>
                                                    </span>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <div class="list-group-item text-center">
                                            <p class="mb-0">No recent enquiries found.</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
    
    <!-- Admin JS -->
    <script src="assets/js/admin.js"></script>
    
    <script>
        // Traffic Chart
        const trafficCtx = document.getElementById('trafficChart').getContext('2d');
        const trafficChart = new Chart(trafficCtx, {
            type: 'line',
            data: {
                labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                datasets: [
                    {
                        label: 'Visitors',
                        data: [1500, 1800, 2200, 1900, 2400, 2800, 3200],
                        borderColor: '#66B2FF',
                        backgroundColor: 'rgba(102, 178, 255, 0.1)',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: 'Page Views',
                        data: [3500, 4200, 4800, 4100, 5200, 5800, 6500],
                        borderColor: '#00FFFF',
                        backgroundColor: 'rgba(0, 255, 255, 0.1)',
                        tension: 0.4,
                        fill: true
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    }
                }
            }
        });
        
        // User Distribution Chart
        const userDistributionCtx = document.getElementById('userDistributionChart').getContext('2d');
        const userDistributionChart = new Chart(userDistributionCtx, {
            type: 'doughnut',
            data: {
                labels: ['Students', 'Parents', 'Coaching Centers', 'Others'],
                datasets: [{
                    data: [45, 25, 20, 10],
                    backgroundColor: ['#66B2FF', '#00FFFF', '#FF00FF', '#333333'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                },
                cutout: '70%'
            }
        });
        
        // Refresh Stats Button
        document.getElementById('refreshStats').addEventListener('click', function() {
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
            this.disabled = true;
            
            // Simulate refreshing data
            setTimeout(() => {
                this.innerHTML = '<i class="fas fa-sync-alt"></i> Refresh Stats';
                this.disabled = false;
                
                // Show a success message
                const alertHtml = `
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i> Statistics refreshed successfully!
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `;
                
                const pageTitle = document.querySelector('.page-title');
                pageTitle.insertAdjacentHTML('afterend', alertHtml);
                
                // Auto dismiss after 5 seconds
                setTimeout(() => {
                    const alert = document.querySelector('.alert');
                    if (alert) {
                        alert.remove();
                    }
                }, 5000);
            }, 1500);
        });
    </script>
</body>
</html>
