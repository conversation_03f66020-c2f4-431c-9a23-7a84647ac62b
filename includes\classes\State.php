<?php
/**
 * State Class
 * Handles operations related to states
 */
class State {
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Get all states
     * @param array $filters Optional filters
     * @param int $page Page number
     * @param int $limit Records per page
     * @return array States and pagination info
     */
    public function getAll($filters = [], $page = 1, $limit = 10) {
        $where = "1=1";
        $params = [];
        
        // Apply filters
        if (isset($filters['status']) && !empty($filters['status'])) {
            $where .= " AND status = ?";
            $params[] = $filters['status'];
        }
        
        if (isset($filters['search']) && !empty($filters['search'])) {
            $where .= " AND (state_name LIKE ? OR state_code LIKE ?)";
            $params[] = "%{$filters['search']}%";
            $params[] = "%{$filters['search']}%";
        }
        
        // Count total records
        $totalCount = $this->db->count('states', $where, $params);
        $totalPages = ceil($totalCount / $limit);
        
        // Ensure page is within valid range
        $page = max(1, min($page, $totalPages));
        $offset = ($page - 1) * $limit;
        
        // Get states
        $sql = "SELECT * FROM states WHERE {$where} ORDER BY state_name ASC LIMIT ?, ?";
        $params[] = $offset;
        $params[] = $limit;
        
        $states = $this->db->fetchAll($sql, $params);
        
        return [
            'states' => $states,
            'total_count' => $totalCount,
            'total_pages' => $totalPages,
            'current_page' => $page
        ];
    }
    
    /**
     * Get all states for dropdown
     * @param string $status Status filter (optional)
     * @return array States
     */
    public function getAllForDropdown($status = 'active') {
        $sql = "SELECT state_id, state_name FROM states";
        $params = [];
        
        if (!empty($status)) {
            $sql .= " WHERE status = ?";
            $params[] = $status;
        }
        
        $sql .= " ORDER BY state_name ASC";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Get state by ID
     * @param int $stateId State ID
     * @return array|null State data
     */
    public function getById($stateId) {
        $sql = "SELECT * FROM states WHERE state_id = ?";
        return $this->db->fetchRow($sql, [$stateId]);
    }
    
    /**
     * Create new state
     * @param array $data State data
     * @return int|bool New state ID or false on failure
     */
    public function create($data) {
        return $this->db->insert('states', $data);
    }
    
    /**
     * Update state
     * @param int $stateId State ID
     * @param array $data State data
     * @return bool Success or failure
     */
    public function update($stateId, $data) {
        return $this->db->update('states', $data, 'state_id = ?', [$stateId]);
    }
    
    /**
     * Delete state
     * @param int $stateId State ID
     * @return bool Success or failure
     */
    public function delete($stateId) {
        return $this->db->delete('states', 'state_id = ?', [$stateId]);
    }
    
    /**
     * Get count by status
     * @param string $status Status
     * @return int Count
     */
    public function getCountByStatus($status) {
        return $this->db->count('states', 'status = ?', [$status]);
    }
}