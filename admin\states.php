<?php
/**
 * Admin States
 */
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    require_once '../includes/autoload.php';
    
    // Check user authentication
    $user = new User();
    if (!$user->isLoggedIn() || !$user->isAdmin()) {
        header('Location: login.php');
        exit;
    }
    
    // Get admin info
    $adminInfo = $user->getUserData();
    
    // Initialize settings
    $settings = Settings::getInstance();
    
    // Initialize state object
    $stateObj = new State();
    
    // Handle actions
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    $stateId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    $message = '';
    
    if ($action === 'delete' && $stateId > 0) {
        if ($stateObj->delete($stateId)) {
            $message = '<div class="alert alert-success">State deleted successfully.</div>';
        } else {
            $message = '<div class="alert alert-danger">Failed to delete state. It may be in use.</div>';
        }
    }
    
    // Handle form submission for adding/editing state
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $stateName = trim($_POST['state_name']);
        $status = $_POST['status'];
        
        if (empty($stateName)) {
            $message = '<div class="alert alert-danger">State name is required.</div>';
        } else {
            $stateData = [
                'state_name' => $stateName,
                'slug' => Utility::generateSlug($stateName),
                'status' => $status
            ];
            
            if (isset($_POST['edit_id']) && $_POST['edit_id'] > 0) {
                // Update existing state
                $editId = (int)$_POST['edit_id'];
                if ($stateObj->update($editId, $stateData)) {
                    $message = '<div class="alert alert-success">State updated successfully.</div>';
                } else {
                    $message = '<div class="alert alert-danger">Failed to update state.</div>';
                }
            } else {
                // Add new state
                $stateData['created_at'] = date('Y-m-d H:i:s');
                
                if ($stateObj->create($stateData)) {
                    $message = '<div class="alert alert-success">State added successfully.</div>';
                } else {
                    $message = '<div class="alert alert-danger">Failed to add state.</div>';
                }
            }
        }
    }
    
    // Get state to edit
    $editState = null;
    if ($action === 'edit' && $stateId > 0) {
        $editState = $stateObj->getById($stateId);
    }
    
    // Get all states
    $statesData = $stateObj->getAll();
    $states = $statesData['states'] ?? [];
    
} catch (Exception $e) {
    die('<div style="color:red;padding:20px;border:2px solid red;margin:20px;">'
        . '<h2>Error</h2>'
        . '<p><strong>Message:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>'
        . '<p><strong>File:</strong> ' . htmlspecialchars($e->getFile()) . '</p>'
        . '<p><strong>Line:</strong> ' . htmlspecialchars($e->getLine()) . '</p>'
        . '</div>');
}

// Page title
$pageTitle = 'States';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Admin Panel | <?php echo $settings->getSiteName(); ?></title>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo getAssetUrl($settings->getSiteFavicon()); ?>" type="image/x-icon">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Admin CSS -->
    <link rel="stylesheet" href="assets/css/admin.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <?php include 'templates/sidebar.php'; ?>
        
        <!-- Main Content -->
        <div class="admin-main" id="adminMain">
            <!-- Header -->
            <?php include 'templates/header.php'; ?>
            
            <!-- Content -->
            <div class="admin-content">
                <div class="page-title">
                    <div>
                        <h1><?php echo $pageTitle; ?></h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="dashboard.php">Home</a></li>
                                <li class="breadcrumb-item">Locations</li>
                                <li class="breadcrumb-item active" aria-current="page"><?php echo $pageTitle; ?></li>
                            </ol>
                        </nav>
                    </div>
                </div>
                
                <?php echo $message; ?>
                
                <div class="row">
                    <!-- Add/Edit State Form -->
                    <div class="col-md-4">
                        <div class="admin-card">
                            <div class="admin-card-header">
                                <h2 class="admin-card-title">
                                    <i class="fas fa-map-marker-alt me-2"></i> <?php echo $editState ? 'Edit State' : 'Add New State'; ?>
                                </h2>
                            </div>
                            <div class="admin-card-body">
                                <form action="" method="post">
                                    <?php if ($editState): ?>
                                        <input type="hidden" name="edit_id" value="<?php echo $editState['state_id']; ?>">
                                    <?php endif; ?>
                                    
                                    <div class="mb-3">
                                        <label for="state_name" class="form-label">State Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="state_name" name="state_name" value="<?php echo $editState ? htmlspecialchars($editState['state_name']) : ''; ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="status" class="form-label">Status</label>
                                        <select class="form-select" id="status" name="status">
                                            <option value="active" <?php echo $editState && $editState['status'] === 'active' ? 'selected' : ''; ?>>Active</option>
                                            <option value="inactive" <?php echo $editState && $editState['status'] === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                                        </select>
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <?php if ($editState): ?>
                                            <button type="submit" class="btn btn-primary">Update State</button>
                                            <a href="states.php" class="btn btn-secondary">Cancel</a>
                                        <?php else: ?>
                                            <button type="submit" class="btn btn-primary">Add State</button>
                                        <?php endif; ?>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- States List -->
                    <div class="col-md-8">
                        <div class="admin-card">
                            <div class="admin-card-header">
                                <h2 class="admin-card-title">
                                    <i class="fas fa-list me-2"></i> All States
                                </h2>
                                <span class="badge bg-primary"><?php echo count($states); ?> Total</span>
                            </div>
                            <div class="admin-card-body p-0">
                                <div class="table-responsive">
                                    <table class="admin-table">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Name</th>
                                                <th>Slug</th>
                                                <th>Status</th>
                                                <th>Cities</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (!empty($states)): ?>
                                                <?php foreach ($states as $state): ?>
                                                    <tr>
                                                        <td><?php echo $state['state_id']; ?></td>
                                                        <td><?php echo htmlspecialchars($state['state_name']); ?></td>
                                                        <td><?php echo htmlspecialchars($state['slug']); ?></td>
                                                        <td>
                                                            <?php if ($state['status'] === 'active'): ?>
                                                                <span class="badge bg-success">Active</span>
                                                            <?php else: ?>
                                                                <span class="badge bg-danger">Inactive</span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <?php echo isset($state['city_count']) ? $state['city_count'] : 0; ?>
                                                            <a href="cities.php?state_id=<?php echo $state['state_id']; ?>" class="ms-1 text-primary">
                                                                <i class="fas fa-external-link-alt"></i>
                                                            </a>
                                                        </td>
                                                        <td>
                                                            <div class="btn-group">
                                                                <a href="states.php?action=edit&id=<?php echo $state['state_id']; ?>" class="btn btn-sm btn-primary" title="Edit">
                                                                    <i class="fas fa-edit"></i>
                                                                </a>
                                                                <a href="states.php?action=delete&id=<?php echo $state['state_id']; ?>" class="btn btn-sm btn-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this state? This will also delete all cities and locations in this state.');">
                                                                    <i class="fas fa-trash"></i>
                                                                </a>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="6" class="text-center">No states found.</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Footer -->
            <?php include 'templates/footer.php'; ?>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Admin JS -->
    <script src="assets/js/admin.js"></script>
</body>
</html>