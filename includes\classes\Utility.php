<?php
/**
 * Utility Class
 * Contains helper methods used throughout the application
 */
class Utility {
    /**
     * Generate a slug from a string
     * @param string $string String to convert to slug
     * @return string Slug
     */
    public static function generateSlug($string) {
        // Replace non-alphanumeric characters with hyphens
        $slug = preg_replace('/[^a-z0-9]+/i', '-', strtolower(trim($string)));
        // Remove duplicate hyphens
        $slug = preg_replace('/-+/', '-', $slug);
        // Remove leading and trailing hyphens
        $slug = trim($slug, '-');
        return $slug;
    }
    
    /**
     * Generate a unique slug
     * @param string $string Base string for slug
     * @param string $table Table to check for existing slug
     * @param string $field Field name for slug in the table
     * @param int $id ID to exclude (for updates)
     * @return string Unique slug
     */
    public static function generateUniqueSlug($string, $table, $field = 'slug', $id = null) {
        $db = Database::getInstance();
        $slug = self::generateSlug($string);
        $originalSlug = $slug;
        $i = 1;
        
        $where = "{$field} = ?";
        $params = [$slug];
        
        if ($id !== null) {
            // Get the primary key column name based on the table
            $primaryKey = '';
            switch ($table) {
                case 'coaching_centers':
                    $primaryKey = 'coaching_id';
                    break;
                case 'courses':
                    $primaryKey = 'course_id';
                    break;
                case 'blog_posts':
                    $primaryKey = 'post_id';
                    break;
                case 'course_categories':
                    $primaryKey = 'category_id';
                    break;
                default:
                    $primaryKey = 'id';
            }
            
            $where .= " AND {$primaryKey} != ?";
            $params[] = $id;
        }
        
        while ($db->count($table, $where, $params) > 0) {
            $slug = $originalSlug . '-' . $i++;
            $params[0] = $slug;
        }
        
        return $slug;
    }
    
    /**
     * Format a date
     * @param string $date Date string
     * @param string $format Format string (default: 'M d, Y')
     * @return string Formatted date
     */
    public static function formatDate($date, $format = 'M d, Y') {
        return date($format, strtotime($date));
    }
    
    /**
     * Format a datetime
     * @param string $datetime Datetime string
     * @param string $format Format string (default: 'M d, Y h:i A')
     * @return string Formatted datetime
     */
    public static function formatDateTime($datetime, $format = 'M d, Y h:i A') {
        return date($format, strtotime($datetime));
    }
    
    /**
     * Get time ago string
     * @param string $datetime Datetime string
     * @return string Time ago string
     */
    public static function timeAgo($datetime) {
        $time = strtotime($datetime);
        $now = time();
        $diff = $now - $time;
        
        if ($diff < 60) {
            return 'just now';
        } elseif ($diff < 3600) {
            $mins = floor($diff / 60);
            return $mins . ' minute' . ($mins > 1 ? 's' : '') . ' ago';
        } elseif ($diff < 86400) {
            $hours = floor($diff / 3600);
            return $hours . ' hour' . ($hours > 1 ? 's' : '') . ' ago';
        } elseif ($diff < 604800) {
            $days = floor($diff / 86400);
            return $days . ' day' . ($days > 1 ? 's' : '') . ' ago';
        } elseif ($diff < 2592000) {
            $weeks = floor($diff / 604800);
            return $weeks . ' week' . ($weeks > 1 ? 's' : '') . ' ago';
        } elseif ($diff < 31536000) {
            $months = floor($diff / 2592000);
            return $months . ' month' . ($months > 1 ? 's' : '') . ' ago';
        } else {
            $years = floor($diff / 31536000);
            return $years . ' year' . ($years > 1 ? 's' : '') . ' ago';
        }
    }
    
    /**
     * Truncate a string to a specified length
     * @param string $string String to truncate
     * @param int $length Maximum length
     * @param string $append String to append if truncated (default: '...')
     * @return string Truncated string
     */
    public static function truncate($string, $length = 100, $append = '...') {
        if (strlen($string) <= $length) {
            return $string;
        }
        
        $string = substr($string, 0, $length);
        $pos = strrpos($string, ' ');
        
        if ($pos !== false) {
            $string = substr($string, 0, $pos);
        }
        
        return $string . $append;
    }
    
    /**
     * Generate a random string
     * @param int $length Length of the string (default: 10)
     * @return string Random string
     */
    public static function generateRandomString($length = 10) {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $randomString = '';
        
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, strlen($characters) - 1)];
        }
        
        return $randomString;
    }
    
    /**
     * Generate a CSRF token
     * @return string CSRF token
     */
    public static function generateCSRFToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        
        return $_SESSION['csrf_token'];
    }
    
    /**
     * Verify CSRF token
     * @param string $token Token to verify
     * @return bool True if token is valid
     */
    public static function verifyCSRFToken($token) {
        if (!isset($_SESSION['csrf_token']) || empty($token)) {
            return false;
        }
        
        return hash_equals($_SESSION['csrf_token'], $token);
    }
    
    /**
     * Sanitize input
     * @param string $input Input to sanitize
     * @return string Sanitized input
     */
    public static function sanitize($input) {
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Validate email
     * @param string $email Email to validate
     * @return bool True if email is valid
     */
    public static function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * Validate URL
     * @param string $url URL to validate
     * @return bool True if URL is valid
     */
    public static function validateURL($url) {
        return filter_var($url, FILTER_VALIDATE_URL) !== false;
    }
    
    /**
     * Get client IP address
     * @return string IP address
     */
    public static function getClientIP() {
        $ipAddress = '';
        
        if (isset($_SERVER['HTTP_CLIENT_IP'])) {
            $ipAddress = $_SERVER['HTTP_CLIENT_IP'];
        } elseif (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ipAddress = $_SERVER['HTTP_X_FORWARDED_FOR'];
        } elseif (isset($_SERVER['HTTP_X_FORWARDED'])) {
            $ipAddress = $_SERVER['HTTP_X_FORWARDED'];
        } elseif (isset($_SERVER['HTTP_FORWARDED_FOR'])) {
            $ipAddress = $_SERVER['HTTP_FORWARDED_FOR'];
        } elseif (isset($_SERVER['HTTP_FORWARDED'])) {
            $ipAddress = $_SERVER['HTTP_FORWARDED'];
        } elseif (isset($_SERVER['REMOTE_ADDR'])) {
            $ipAddress = $_SERVER['REMOTE_ADDR'];
        } else {
            $ipAddress = 'UNKNOWN';
        }
        
        return $ipAddress;
    }
    
    /**
     * Upload a file
     * @param array $file File from $_FILES
     * @param string $destination Destination directory
     * @param array $allowedTypes Allowed MIME types
     * @param int $maxSize Maximum file size in bytes
     * @return array|bool Array with file info or false on failure
     */
    public static function uploadFile($file, $destination, $allowedTypes = [], $maxSize = 0) {
        // Check if file was uploaded
        if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
            return false;
        }
        
        // Check for upload errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            return false;
        }
        
        // Check file size
        if ($maxSize > 0 && $file['size'] > $maxSize) {
            return false;
        }
        
        // Check file type
        if (!empty($allowedTypes) && !in_array($file['type'], $allowedTypes)) {
            return false;
        }
        
        // Create destination directory if it doesn't exist
        if (!is_dir($destination)) {
            mkdir($destination, 0755, true);
        }
        
        // Generate unique filename
        $filename = pathinfo($file['name'], PATHINFO_FILENAME);
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $uniqueName = self::generateSlug($filename) . '-' . uniqid() . '.' . $extension;
        $targetPath = $destination . '/' . $uniqueName;
        
        // Move uploaded file
        if (move_uploaded_file($file['tmp_name'], $targetPath)) {
            return [
                'name' => $uniqueName,
                'original_name' => $file['name'],
                'type' => $file['type'],
                'size' => $file['size'],
                'path' => $targetPath,
                'url' => str_replace(BASE_PATH, BASE_URL, $targetPath)
            ];
        }
        
        return false;
    }
    
    /**
     * Delete a file
     * @param string $path File path
     * @return bool True if file was deleted
     */
    public static function deleteFile($path) {
        if (file_exists($path)) {
            return unlink($path);
        }
        
        return false;
    }
    
    /**
     * Get settings from database
     * @param string $key Setting key
     * @param mixed $default Default value if setting not found
     * @return mixed Setting value
     */
    public static function getSetting($key, $default = null) {
        $db = Database::getInstance();
        $row = $db->fetchRow("SELECT setting_value FROM settings WHERE setting_key = ?", [$key]);
        
        return $row ? $row['setting_value'] : $default;
    }
    
    /**
     * Update setting in database
     * @param string $key Setting key
     * @param mixed $value Setting value
     * @return bool True if setting was updated
     */
    public static function updateSetting($key, $value) {
        $db = Database::getInstance();
        $row = $db->fetchRow("SELECT setting_id FROM settings WHERE setting_key = ?", [$key]);
        
        if ($row) {
            return $db->update('settings', ['setting_value' => $value], 'setting_id = ?', [$row['setting_id']]);
        } else {
            return $db->insert('settings', [
                'setting_key' => $key,
                'setting_value' => $value
            ]) !== false;
        }
    }
    
    /**
     * Get pagination HTML
     * @param int $totalRecords Total number of records
     * @param int $currentPage Current page number
     * @param int $recordsPerPage Records per page
     * @param string $url Base URL for pagination links
     * @return string Pagination HTML
     */
    public static function getPagination($totalRecords, $currentPage, $recordsPerPage, $url) {
        $totalPages = ceil($totalRecords / $recordsPerPage);
        
        if ($totalPages <= 1) {
            return '';
        }
        
        $html = '<nav aria-label="Page navigation"><ul class="pagination">';
        
        // Previous button
        if ($currentPage > 1) {
            $html .= '<li class="page-item"><a class="page-link" href="' . $url . '?page=' . ($currentPage - 1) . '" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a></li>';
        } else {
            $html .= '<li class="page-item disabled"><a class="page-link" href="#" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a></li>';
        }
        
        // Page numbers
        $startPage = max(1, $currentPage - 2);
        $endPage = min($totalPages, $currentPage + 2);
        
        if ($startPage > 1) {
            $html .= '<li class="page-item"><a class="page-link" href="' . $url . '?page=1">1</a></li>';
            
            if ($startPage > 2) {
                $html .= '<li class="page-item disabled"><a class="page-link" href="#">...</a></li>';
            }
        }
        
        for ($i = $startPage; $i <= $endPage; $i++) {
            if ($i == $currentPage) {
                $html .= '<li class="page-item active"><a class="page-link" href="#">' . $i . '</a></li>';
            } else {
                $html .= '<li class="page-item"><a class="page-link" href="' . $url . '?page=' . $i . '">' . $i . '</a></li>';
            }
        }
        
        if ($endPage < $totalPages) {
            if ($endPage < $totalPages - 1) {
                $html .= '<li class="page-item disabled"><a class="page-link" href="#">...</a></li>';
            }
            
            $html .= '<li class="page-item"><a class="page-link" href="' . $url . '?page=' . $totalPages . '">' . $totalPages . '</a></li>';
        }
        
        // Next button
        if ($currentPage < $totalPages) {
            $html .= '<li class="page-item"><a class="page-link" href="' . $url . '?page=' . ($currentPage + 1) . '" aria-label="Next"><span aria-hidden="true">&raquo;</span></a></li>';
        } else {
            $html .= '<li class="page-item disabled"><a class="page-link" href="#" aria-label="Next"><span aria-hidden="true">&raquo;</span></a></li>';
        }
        
        $html .= '</ul></nav>';
        
        return $html;
    }
    
    /**
     * Get star rating HTML
     * @param float $rating Rating value
     * @param int $maxRating Maximum rating (default: 5)
     * @return string Star rating HTML
     */
    public static function getStarRating($rating, $maxRating = 5) {
        $html = '<div class="star-rating">';
        $fullStars = floor($rating);
        $halfStar = $rating - $fullStars >= 0.5;
        $emptyStars = $maxRating - $fullStars - ($halfStar ? 1 : 0);
        
        // Full stars
        for ($i = 0; $i < $fullStars; $i++) {
            $html .= '<i class="fas fa-star"></i>';
        }
        
        // Half star
        if ($halfStar) {
            $html .= '<i class="fas fa-star-half-alt"></i>';
        }
        
        // Empty stars
        for ($i = 0; $i < $emptyStars; $i++) {
            $html .= '<i class="far fa-star"></i>';
        }
        
        $html .= '<span class="rating-value">' . number_format($rating, 1) . '</span>';
        $html .= '</div>';
        
        return $html;
    }
    
    /**
     * Get meta tags HTML
     * @param string $title Meta title
     * @param string $description Meta description
     * @param string $keywords Meta keywords
     * @param string $ogImage Open Graph image URL
     * @return string Meta tags HTML
     */
    public static function getMetaTags($title = '', $description = '', $keywords = '', $ogImage = '') {
        $title = !empty($title) ? $title : DEFAULT_META_TITLE;
        $description = !empty($description) ? $description : DEFAULT_META_DESCRIPTION;
        $keywords = !empty($keywords) ? $keywords : DEFAULT_META_KEYWORDS;
        
        $html = '<title>' . $title . '</title>' . PHP_EOL;
        $html .= '<meta name="description" content="' . $description . '">' . PHP_EOL;
        $html .= '<meta name="keywords" content="' . $keywords . '">' . PHP_EOL;
        
        // Open Graph tags
        $html .= '<meta property="og:title" content="' . $title . '">' . PHP_EOL;
        $html .= '<meta property="og:description" content="' . $description . '">' . PHP_EOL;
        $html .= '<meta property="og:type" content="website">' . PHP_EOL;
        $html .= '<meta property="og:url" content="' . (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'] . '">' . PHP_EOL;
        
        if (!empty($ogImage)) {
            $html .= '<meta property="og:image" content="' . $ogImage . '">' . PHP_EOL;
        }
        
        // Twitter Card tags
        $html .= '<meta name="twitter:card" content="summary_large_image">' . PHP_EOL;
        $html .= '<meta name="twitter:title" content="' . $title . '">' . PHP_EOL;
        $html .= '<meta name="twitter:description" content="' . $description . '">' . PHP_EOL;
        
        if (!empty($ogImage)) {
            $html .= '<meta name="twitter:image" content="' . $ogImage . '">' . PHP_EOL;
        }
        
        return $html;
    }
    
    /**
     * Get breadcrumb HTML
     * @param array $items Breadcrumb items (array of ['title' => string, 'url' => string])
     * @return string Breadcrumb HTML
     */
    public static function getBreadcrumb($items) {
        $html = '<nav aria-label="breadcrumb"><ol class="breadcrumb">';
        
        foreach ($items as $index => $item) {
            if ($index === count($items) - 1) {
                $html .= '<li class="breadcrumb-item active" aria-current="page">' . $item['title'] . '</li>';
            } else {
                $html .= '<li class="breadcrumb-item"><a href="' . $item['url'] . '">' . $item['title'] . '</a></li>';
            }
        }
        
        $html .= '</ol></nav>';
        
        return $html;
    }
}