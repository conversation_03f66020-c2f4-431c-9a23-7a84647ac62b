<?php
/**
 * Verify Category Page
 * This file verifies that the category pages are working correctly
 */
require_once 'includes/autoload.php';
require_once 'includes/dummy-data.php';

// Get all dummy categories
$dummyCategories = getDummyCategories(20);

echo "<h1>Category Verification</h1>";

echo "<p>This page will help you verify that the category pages are working correctly.</p>";

echo "<h2>Test All Categories:</h2>";
echo "<table border='1' cellpadding='5'>";
echo "<tr><th>ID</th><th>Name</th><th>Slug</th><th>Direct Link</th><th>Pretty URL</th></tr>";

foreach ($dummyCategories as $cat) {
    $directUrl = "category.php?slug=" . $cat['slug'];
    $prettyUrl = getCategoryUrl($cat['slug']);
    
    echo "<tr>";
    echo "<td>{$cat['category_id']}</td>";
    echo "<td>{$cat['category_name']}</td>";
    echo "<td>{$cat['slug']}</td>";
    echo "<td><a href='{$directUrl}' target='_blank'>Test Direct</a></td>";
    echo "<td><a href='{$prettyUrl}' target='_blank'>Test Pretty URL</a></td>";
    echo "</tr>";
}

echo "</table>";

echo "<h2>Instructions:</h2>";
echo "<ol>";
echo "<li>Click on each link above to open the category page.</li>";
echo "<li>Verify that the page title and content match the category name.</li>";
echo "<li>Verify that the coaching centers shown are relevant to the category.</li>";
echo "</ol>";

echo "<h2>Troubleshooting:</h2>";
echo "<p>If you're still seeing the same content for all categories, try the following:</p>";
echo "<ol>";
echo "<li>Clear your browser cache.</li>";
echo "<li>Try accessing the direct link (Test Direct) instead of the pretty URL.</li>";
echo "<li>Check the browser's developer tools to see if there are any redirects happening.</li>";
echo "</ol>";

// Add a form to test a specific category
echo "<h2>Test Specific Category:</h2>";
echo "<form method='get' action='category.php'>";
echo "<select name='slug'>";
foreach ($dummyCategories as $cat) {
    echo "<option value='{$cat['slug']}'>{$cat['category_name']}</option>";
}
echo "</select>";
echo "<button type='submit'>Test</button>";
echo "</form>";