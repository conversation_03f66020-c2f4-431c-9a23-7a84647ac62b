<?php
/**
 * Debug Users - Check what users exist in the database
 */
require_once 'includes/autoload.php';

echo "<h1>Users Debug</h1>";

try {
    $db = Database::getInstance();
    
    echo "<h2>All Users:</h2>";
    $users = $db->fetchAll("SELECT user_id, email, user_type, status, created_at FROM users ORDER BY user_id");
    
    if (empty($users)) {
        echo "No users found in database.";
    } else {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Email</th><th>Type</th><th>Status</th><th>Created</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . $user['user_id'] . "</td>";
            echo "<td>" . $user['email'] . "</td>";
            echo "<td>" . $user['user_type'] . "</td>";
            echo "<td>" . $user['status'] . "</td>";
            echo "<td>" . $user['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h2>Coaching Centers:</h2>";
    $coachings = $db->fetchAll("SELECT coaching_id, coaching_name, user_id, status FROM coaching_centers ORDER BY coaching_id");
    
    if (empty($coachings)) {
        echo "No coaching centers found in database.";
    } else {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Name</th><th>User ID</th><th>Status</th></tr>";
        foreach ($coachings as $coaching) {
            echo "<tr>";
            echo "<td>" . $coaching['coaching_id'] . "</td>";
            echo "<td>" . $coaching['coaching_name'] . "</td>";
            echo "<td>" . $coaching['user_id'] . "</td>";
            echo "<td>" . $coaching['status'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
?>