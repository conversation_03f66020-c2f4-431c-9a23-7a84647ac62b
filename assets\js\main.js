/**
 * Main JavaScript File
 */

document.addEventListener('DOMContentLoaded', function() {
    // Back to Top Button
    const backToTopButton = document.getElementById('backToTop');
    
    if (backToTopButton) {
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                backToTopButton.classList.add('active');
            } else {
                backToTopButton.classList.remove('active');
            }
        });
        
        backToTopButton.addEventListener('click', function(e) {
            e.preventDefault();
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
    }
    
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialize popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function(popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // Auto-dismiss alerts
    const autoDismissAlerts = document.querySelectorAll('.alert-auto-dismiss');
    autoDismissAlerts.forEach(function(alert) {
        setTimeout(function() {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });
    
    // City and Location Dropdowns
    const stateDropdown = document.getElementById('state_id');
    const cityDropdown = document.getElementById('city_id');
    const locationDropdown = document.getElementById('location_id');
    
    if (stateDropdown && cityDropdown) {
        stateDropdown.addEventListener('change', function() {
            const stateId = this.value;
            
            if (stateId) {
                // Clear city dropdown
                cityDropdown.innerHTML = '<option value="">-- Select City --</option>';
                
                // Clear location dropdown if exists
                if (locationDropdown) {
                    locationDropdown.innerHTML = '<option value="">-- Select Location --</option>';
                }
                
                // Fetch cities by state
                fetch(`get_cities.php?state_id=${stateId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.cities && data.cities.length > 0) {
                            data.cities.forEach(city => {
                                const option = document.createElement('option');
                                option.value = city.city_id;
                                option.textContent = city.city_name;
                                cityDropdown.appendChild(option);
                            });
                        }
                    })
                    .catch(error => console.error('Error fetching cities:', error));
            }
        });
    }
    
    if (cityDropdown && locationDropdown) {
        cityDropdown.addEventListener('change', function() {
            const cityId = this.value;
            
            if (cityId) {
                // Clear location dropdown
                locationDropdown.innerHTML = '<option value="">-- Select Location --</option>';
                
                // Fetch locations by city
                fetch(`get_locations.php?city_id=${cityId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.locations && data.locations.length > 0) {
                            data.locations.forEach(location => {
                                const option = document.createElement('option');
                                option.value = location.location_id;
                                option.textContent = location.location_name;
                                locationDropdown.appendChild(option);
                            });
                        }
                    })
                    .catch(error => console.error('Error fetching locations:', error));
            }
        });
    }
    
    // Rating Stars
    const ratingInputs = document.querySelectorAll('.rating-input');
    
    ratingInputs.forEach(function(input) {
        const stars = input.querySelectorAll('.star');
        const ratingValue = input.querySelector('input[type="hidden"]');
        
        stars.forEach(function(star, index) {
            star.addEventListener('click', function() {
                const value = index + 1;
                ratingValue.value = value;
                
                // Update stars
                stars.forEach(function(s, i) {
                    if (i < value) {
                        s.classList.add('active');
                    } else {
                        s.classList.remove('active');
                    }
                });
            });
            
            star.addEventListener('mouseover', function() {
                const value = index + 1;
                
                // Update stars on hover
                stars.forEach(function(s, i) {
                    if (i < value) {
                        s.classList.add('hover');
                    } else {
                        s.classList.remove('hover');
                    }
                });
            });
            
            star.addEventListener('mouseout', function() {
                stars.forEach(function(s) {
                    s.classList.remove('hover');
                });
            });
        });
    });
    
    // Image Preview
    const imageInputs = document.querySelectorAll('.image-input');
    
    imageInputs.forEach(function(input) {
        const preview = input.nextElementSibling;
        
        if (preview && preview.classList.contains('image-preview')) {
            input.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const reader = new FileReader();
                    
                    reader.onload = function(e) {
                        preview.src = e.target.result;
                        preview.style.display = 'block';
                    };
                    
                    reader.readAsDataURL(this.files[0]);
                }
            });
        }
    });
    
    // Toggle Password Visibility
    const passwordToggles = document.querySelectorAll('.password-toggle');
    
    passwordToggles.forEach(function(toggle) {
        toggle.addEventListener('click', function() {
            const passwordInput = this.previousElementSibling;
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                this.innerHTML = '<i class="fas fa-eye-slash"></i>';
            } else {
                passwordInput.type = 'password';
                this.innerHTML = '<i class="fas fa-eye"></i>';
            }
        });
    });
    
    // Add to Favorites
    const favoriteButtons = document.querySelectorAll('.favorite-button');
    
    favoriteButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const coachingId = this.dataset.id;
            const isFavorite = this.classList.contains('active');
            
            fetch('add_favorite.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `coaching_id=${coachingId}&action=${isFavorite ? 'remove' : 'add'}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (isFavorite) {
                        this.classList.remove('active');
                        this.innerHTML = '<i class="far fa-heart"></i>';
                    } else {
                        this.classList.add('active');
                        this.innerHTML = '<i class="fas fa-heart"></i>';
                    }
                } else if (data.redirect) {
                    window.location.href = data.redirect;
                }
            })
            .catch(error => console.error('Error updating favorite:', error));
        });
    });
    
    // Review Helpful Buttons
    const helpfulButtons = document.querySelectorAll('.helpful-button');
    
    helpfulButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const reviewId = this.dataset.id;
            const voteType = this.dataset.type;
            const isActive = this.classList.contains('active');
            
            fetch('review_vote.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `review_id=${reviewId}&vote_type=${voteType}&action=${isActive ? 'remove' : 'add'}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (isActive) {
                        this.classList.remove('active');
                    } else {
                        this.classList.add('active');
                    }
                    
                    // Update count
                    const countElement = this.querySelector('.count');
                    if (countElement) {
                        countElement.textContent = data.count;
                    }
                } else if (data.redirect) {
                    window.location.href = data.redirect;
                }
            })
            .catch(error => console.error('Error updating vote:', error));
        });
    });
    
    // Filter Toggle on Mobile
    const filterToggle = document.getElementById('filterToggle');
    const filterSidebar = document.getElementById('filterSidebar');
    
    if (filterToggle && filterSidebar) {
        filterToggle.addEventListener('click', function() {
            filterSidebar.classList.toggle('show');
        });
    }
    
    // Initialize Lightbox for Gallery
    const galleryItems = document.querySelectorAll('.gallery-item');
    
    if (galleryItems.length > 0 && typeof GLightbox === 'function') {
        const lightbox = GLightbox({
            selector: '.gallery-item',
            touchNavigation: true,
            loop: true
        });
    }
    
    // Initialize Sliders
    const sliders = document.querySelectorAll('.swiper-container');
    
    if (sliders.length > 0 && typeof Swiper === 'function') {
        sliders.forEach(function(slider) {
            const sliderId = slider.id;
            
            if (sliderId === 'featuredSlider') {
                new Swiper(`#${sliderId}`, {
                    slidesPerView: 1,
                    spaceBetween: 20,
                    pagination: {
                        el: '.swiper-pagination',
                        clickable: true
                    },
                    navigation: {
                        nextEl: '.swiper-button-next',
                        prevEl: '.swiper-button-prev'
                    },
                    breakpoints: {
                        576: {
                            slidesPerView: 2
                        },
                        992: {
                            slidesPerView: 3
                        }
                    }
                });
            } else if (sliderId === 'testimonialSlider') {
                new Swiper(`#${sliderId}`, {
                    slidesPerView: 1,
                    spaceBetween: 30,
                    pagination: {
                        el: '.swiper-pagination',
                        clickable: true
                    },
                    autoplay: {
                        delay: 5000
                    }
                });
            }
        });
    }
    
    // Form Validation
    const forms = document.querySelectorAll('.needs-validation');
    
    forms.forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        }, false);
    });
    
    // Character Counter for Textarea
    const textareas = document.querySelectorAll('textarea[maxlength]');
    
    textareas.forEach(function(textarea) {
        const maxLength = textarea.getAttribute('maxlength');
        const counter = document.createElement('div');
        counter.className = 'char-counter';
        counter.innerHTML = `<span>${textarea.value.length}</span>/${maxLength}`;
        
        textarea.parentNode.insertBefore(counter, textarea.nextSibling);
        
        textarea.addEventListener('input', function() {
            counter.innerHTML = `<span>${this.value.length}</span>/${maxLength}`;
        });
    });
    
    // Sticky Sidebar
    const stickySidebar = document.querySelector('.sticky-sidebar');
    
    if (stickySidebar && window.innerWidth >= 992) {
        const sidebarTop = stickySidebar.getBoundingClientRect().top + window.pageYOffset;
        
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > sidebarTop - 20) {
                stickySidebar.classList.add('sticky');
            } else {
                stickySidebar.classList.remove('sticky');
            }
        });
    }
    
    // Smooth Scroll to Anchor
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            
            if (href !== '#') {
                e.preventDefault();
                
                const target = document.querySelector(href);
                
                if (target) {
                    window.scrollTo({
                        top: target.offsetTop - 100,
                        behavior: 'smooth'
                    });
                }
            }
        });
    });
});