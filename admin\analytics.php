<?php
/**
 * Analytics Dashboard
 */
session_start();
require_once '../includes/autoload.php';

// Check if logged in
if (!isset($_SESSION['admin_id'])) {
    redirect('index.php');
}

// Get settings
$settings = Settings::getInstance();

// Get admin info
$admin = new Admin();
$adminInfo = $admin->getById($_SESSION['admin_id']);

// Check permission
if (!$admin->hasPermission('view_analytics')) {
    setFlashMessage('error', 'You do not have permission to access this page.');
    redirect('dashboard.php');
}

// Get date range
$startDate = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-30 days'));
$endDate = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');

// Get analytics data
$analyticsObj = new Analytics();
$visitorStats = $analyticsObj->getVisitorStats($startDate, $endDate);
$pageViews = $analyticsObj->getPageViews($startDate, $endDate);
$topPages = $analyticsObj->getTopPages($startDate, $endDate, 10);
$topReferrers = $analyticsObj->getTopReferrers($startDate, $endDate, 10);
$deviceStats = $analyticsObj->getDeviceStats($startDate, $endDate);
$browserStats = $analyticsObj->getBrowserStats($startDate, $endDate);
$countryStats = $analyticsObj->getCountryStats($startDate, $endDate);

// Get dashboard stats for header
$coachingObj = new CoachingCenter();
$pendingCoachings = $coachingObj->getTotalCount('pending');

$reviewObj = new Review();
$pendingReviews = $reviewObj->getTotalCount('pending');

$enquiryObj = new Enquiry();
$newEnquiries = $enquiryObj->getNewEnquiriesCount(7); // New enquiries in last 7 days

// Page title
$pageTitle = 'Analytics Dashboard';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Admin Panel | <?php echo $settings->getSiteName(); ?></title>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo getAssetUrl($settings->getSiteFavicon()); ?>" type="image/x-icon">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Chart.js -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.css">
    
    <!-- Datepicker -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/vanillajs-datepicker@1.2.0/dist/css/datepicker.min.css">
    
    <!-- Admin CSS -->
    <link rel="stylesheet" href="assets/css/admin.css">
    
    <style>
        .analytics-card {
            height: 100%;
        }
        
        .analytics-card .admin-card-body {
            padding: 0;
        }
        
        .analytics-card canvas {
            padding: 20px;
        }
        
        .analytics-summary {
            display: flex;
            justify-content: space-between;
            padding: 20px;
            background-color: var(--light-gray);
            border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
            margin-bottom: 20px;
        }
        
        .analytics-summary-item {
            text-align: center;
        }
        
        .analytics-summary-number {
            font-size: 24px;
            font-weight: 700;
            color: var(--dark-blue);
        }
        
        .analytics-summary-label {
            font-size: 14px;
            color: var(--dark-gray);
        }
        
        .analytics-table {
            width: 100%;
        }
        
        .analytics-table th {
            background-color: var(--light-gray);
            color: var(--dark-blue);
            font-weight: 600;
            padding: 12px 15px;
            text-align: left;
            border-bottom: 2px solid var(--medium-gray);
        }
        
        .analytics-table td {
            padding: 12px 15px;
            border-bottom: 1px solid var(--light-gray);
        }
        
        .analytics-table .progress {
            height: 8px;
            width: 100px;
        }
        
        .map-container {
            height: 300px;
            position: relative;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <?php include 'templates/sidebar.php'; ?>
        
        <!-- Main Content -->
        <div class="admin-main" id="adminMain">
            <!-- Header -->
            <?php include 'templates/header.php'; ?>
            
            <!-- Content -->
            <div class="admin-content">
                <div class="page-title">
                    <div>
                        <h1><?php echo $pageTitle; ?></h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="dashboard.php">Home</a></li>
                                <li class="breadcrumb-item active" aria-current="page"><?php echo $pageTitle; ?></li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <form action="analytics.php" method="get" class="d-flex align-items-center">
                            <div class="input-group me-2">
                                <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                <input type="text" class="form-control datepicker" id="start_date" name="start_date" value="<?php echo $startDate; ?>" placeholder="Start Date">
                            </div>
                            <div class="input-group me-2">
                                <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                <input type="text" class="form-control datepicker" id="end_date" name="end_date" value="<?php echo $endDate; ?>" placeholder="End Date">
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter me-2"></i> Filter
                            </button>
                        </form>
                    </div>
                </div>
                
                <!-- Analytics Summary -->
                <div class="admin-card mb-4">
                    <div class="admin-card-body">
                        <div class="analytics-summary">
                            <div class="analytics-summary-item">
                                <div class="analytics-summary-number"><?php echo number_format($visitorStats['total_visitors']); ?></div>
                                <div class="analytics-summary-label">Total Visitors</div>
                            </div>
                            <div class="analytics-summary-item">
                                <div class="analytics-summary-number"><?php echo number_format($visitorStats['unique_visitors']); ?></div>
                                <div class="analytics-summary-label">Unique Visitors</div>
                            </div>
                            <div class="analytics-summary-item">
                                <div class="analytics-summary-number"><?php echo number_format($pageViews['total_page_views']); ?></div>
                                <div class="analytics-summary-label">Page Views</div>
                            </div>
                            <div class="analytics-summary-item">
                                <div class="analytics-summary-number"><?php echo number_format($pageViews['avg_pages_per_session'], 1); ?></div>
                                <div class="analytics-summary-label">Pages per Session</div>
                            </div>
                            <div class="analytics-summary-item">
                                <div class="analytics-summary-number"><?php echo number_format($visitorStats['bounce_rate'], 1); ?>%</div>
                                <div class="analytics-summary-label">Bounce Rate</div>
                            </div>
                            <div class="analytics-summary-item">
                                <div class="analytics-summary-number"><?php echo gmdate('i:s', $visitorStats['avg_session_duration']); ?></div>
                                <div class="analytics-summary-label">Avg. Session Duration</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Visitors & Page Views Chart -->
                <div class="row">
                    <div class="col-lg-8">
                        <div class="admin-card analytics-card mb-4">
                            <div class="admin-card-header">
                                <h2 class="admin-card-title">
                                    <i class="fas fa-chart-line me-2"></i> Visitors & Page Views
                                </h2>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="visitorChartOptions" data-bs-toggle="dropdown" aria-expanded="false">
                                        Daily
                                    </button>
                                    <ul class="dropdown-menu" aria-labelledby="visitorChartOptions">
                                        <li><a class="dropdown-item active" href="#">Daily</a></li>
                                        <li><a class="dropdown-item" href="#">Weekly</a></li>
                                        <li><a class="dropdown-item" href="#">Monthly</a></li>
                                    </ul>
                                </div>
                            </div>
                            <div class="admin-card-body">
                                <canvas id="visitorsChart" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4">
                        <div class="admin-card analytics-card mb-4">
                            <div class="admin-card-header">
                                <h2 class="admin-card-title">
                                    <i class="fas fa-chart-pie me-2"></i> Device Distribution
                                </h2>
                            </div>
                            <div class="admin-card-body">
                                <canvas id="deviceChart" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Top Pages & Referrers -->
                <div class="row">
                    <div class="col-lg-6">
                        <div class="admin-card analytics-card mb-4">
                            <div class="admin-card-header">
                                <h2 class="admin-card-title">
                                    <i class="fas fa-file-alt me-2"></i> Top Pages
                                </h2>
                                <a href="analytics-pages.php" class="btn btn-sm btn-outline-primary">View All</a>
                            </div>
                            <div class="admin-card-body">
                                <div class="table-responsive">
                                    <table class="analytics-table">
                                        <thead>
                                            <tr>
                                                <th>Page</th>
                                                <th>Views</th>
                                                <th>% of Total</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($topPages as $page): ?>
                                                <tr>
                                                    <td>
                                                        <div class="text-truncate" style="max-width: 250px;" title="<?php echo htmlspecialchars($page['page_url']); ?>">
                                                            <?php echo htmlspecialchars($page['page_title'] ? $page['page_title'] : $page['page_url']); ?>
                                                        </div>
                                                    </td>
                                                    <td><?php echo number_format($page['page_views']); ?></td>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="progress me-2">
                                                                <div class="progress-bar bg-primary" role="progressbar" style="width: <?php echo $page['percentage']; ?>%" aria-valuenow="<?php echo $page['percentage']; ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                                            </div>
                                                            <span><?php echo number_format($page['percentage'], 1); ?>%</span>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-6">
                        <div class="admin-card analytics-card mb-4">
                            <div class="admin-card-header">
                                <h2 class="admin-card-title">
                                    <i class="fas fa-link me-2"></i> Top Referrers
                                </h2>
                                <a href="analytics-referrers.php" class="btn btn-sm btn-outline-primary">View All</a>
                            </div>
                            <div class="admin-card-body">
                                <div class="table-responsive">
                                    <table class="analytics-table">
                                        <thead>
                                            <tr>
                                                <th>Source</th>
                                                <th>Visitors</th>
                                                <th>% of Total</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($topReferrers as $referrer): ?>
                                                <tr>
                                                    <td>
                                                        <div class="text-truncate" style="max-width: 250px;" title="<?php echo htmlspecialchars($referrer['referrer']); ?>">
                                                            <?php echo htmlspecialchars($referrer['referrer'] ? $referrer['referrer'] : 'Direct / None'); ?>
                                                        </div>
                                                    </td>
                                                    <td><?php echo number_format($referrer['visitors']); ?></td>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="progress me-2">
                                                                <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $referrer['percentage']; ?>%" aria-valuenow="<?php echo $referrer['percentage']; ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                                            </div>
                                                            <span><?php echo number_format($referrer['percentage'], 1); ?>%</span>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Browser & Country Stats -->
                <div class="row">
                    <div class="col-lg-6">
                        <div class="admin-card analytics-card mb-4">
                            <div class="admin-card-header">
                                <h2 class="admin-card-title">
                                    <i class="fas fa-globe me-2"></i> Browser Statistics
                                </h2>
                            </div>
                            <div class="admin-card-body">
                                <canvas id="browserChart" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-6">
                        <div class="admin-card analytics-card mb-4">
                            <div class="admin-card-header">
                                <h2 class="admin-card-title">
                                    <i class="fas fa-map-marker-alt me-2"></i> Visitor Locations
                                </h2>
                            </div>
                            <div class="admin-card-body">
                                <div class="map-container" id="visitorMap"></div>
                                <div class="table-responsive mt-3">
                                    <table class="analytics-table">
                                        <thead>
                                            <tr>
                                                <th>Country</th>
                                                <th>Visitors</th>
                                                <th>% of Total</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach (array_slice($countryStats, 0, 5) as $country): ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($country['country']); ?></td>
                                                    <td><?php echo number_format($country['visitors']); ?></td>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="progress me-2">
                                                                <div class="progress-bar bg-info" role="progressbar" style="width: <?php echo $country['percentage']; ?>%" aria-valuenow="<?php echo $country['percentage']; ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                                            </div>
                                                            <span><?php echo number_format($country['percentage'], 1); ?>%</span>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Export Options -->
                <div class="admin-card mb-4">
                    <div class="admin-card-header">
                        <h2 class="admin-card-title">
                            <i class="fas fa-download me-2"></i> Export Analytics Data
                        </h2>
                    </div>
                    <div class="admin-card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p>Export analytics data for the selected date range in various formats.</p>
                            </div>
                            <div class="col-md-6 text-end">
                                <div class="btn-group">
                                    <a href="analytics-export.php?format=csv&start_date=<?php echo $startDate; ?>&end_date=<?php echo $endDate; ?>" class="btn btn-outline-primary">
                                        <i class="fas fa-file-csv me-2"></i> CSV
                                    </a>
                                    <a href="analytics-export.php?format=excel&start_date=<?php echo $startDate; ?>&end_date=<?php echo $endDate; ?>" class="btn btn-outline-success">
                                        <i class="fas fa-file-excel me-2"></i> Excel
                                    </a>
                                    <a href="analytics-export.php?format=pdf&start_date=<?php echo $startDate; ?>&end_date=<?php echo $endDate; ?>" class="btn btn-outline-danger">
                                        <i class="fas fa-file-pdf me-2"></i> PDF
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
    
    <!-- Datepicker -->
    <script src="https://cdn.jsdelivr.net/npm/vanillajs-datepicker@1.2.0/dist/js/datepicker.min.js"></script>
    
    <!-- Admin JS -->
    <script src="assets/js/admin.js"></script>
    
    <script>
        // Initialize datepickers
        const datepickers = document.querySelectorAll('.datepicker');
        datepickers.forEach(datepicker => {
            new Datepicker(datepicker, {
                format: 'yyyy-mm-dd',
                autohide: true
            });
        });
        
        // Visitors Chart
        const visitorsCtx = document.getElementById('visitorsChart').getContext('2d');
        const visitorsChart = new Chart(visitorsCtx, {
            type: 'line',
            data: {
                labels: <?php echo json_encode(array_column($visitorStats['daily_stats'], 'date')); ?>,
                datasets: [
                    {
                        label: 'Visitors',
                        data: <?php echo json_encode(array_column($visitorStats['daily_stats'], 'visitors')); ?>,
                        borderColor: '#66B2FF',
                        backgroundColor: 'rgba(102, 178, 255, 0.1)',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: 'Page Views',
                        data: <?php echo json_encode(array_column($pageViews['daily_stats'], 'page_views')); ?>,
                        borderColor: '#00FFFF',
                        backgroundColor: 'rgba(0, 255, 255, 0.1)',
                        tension: 0.4,
                        fill: true
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    }
                }
            }
        });
        
        // Device Chart
        const deviceCtx = document.getElementById('deviceChart').getContext('2d');
        const deviceChart = new Chart(deviceCtx, {
            type: 'doughnut',
            data: {
                labels: <?php echo json_encode(array_column($deviceStats, 'device')); ?>,
                datasets: [{
                    data: <?php echo json_encode(array_column($deviceStats, 'visitors')); ?>,
                    backgroundColor: ['#66B2FF', '#00FFFF', '#FF00FF', '#333333'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                },
                cutout: '70%'
            }
        });
        
        // Browser Chart
        const browserCtx = document.getElementById('browserChart').getContext('2d');
        const browserChart = new Chart(browserCtx, {
            type: 'bar',
            data: {
                labels: <?php echo json_encode(array_column($browserStats, 'browser')); ?>,
                datasets: [{
                    label: 'Visitors',
                    data: <?php echo json_encode(array_column($browserStats, 'visitors')); ?>,
                    backgroundColor: [
                        'rgba(102, 178, 255, 0.7)',
                        'rgba(0, 255, 255, 0.7)',
                        'rgba(255, 0, 255, 0.7)',
                        'rgba(51, 51, 51, 0.7)',
                        'rgba(40, 167, 69, 0.7)'
                    ],
                    borderColor: [
                        'rgba(102, 178, 255, 1)',
                        'rgba(0, 255, 255, 1)',
                        'rgba(255, 0, 255, 1)',
                        'rgba(51, 51, 51, 1)',
                        'rgba(40, 167, 69, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
        
        // Visitor Map (placeholder - would use a real mapping library in production)
        const visitorMap = document.getElementById('visitorMap');
        visitorMap.innerHTML = '<div class="d-flex align-items-center justify-content-center h-100"><p class="text-muted">World map visualization would be displayed here using a mapping library like Leaflet or Google Maps.</p></div>';
    </script>
</body>
</html>