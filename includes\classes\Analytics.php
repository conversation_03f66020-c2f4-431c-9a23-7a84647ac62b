<?php
/**
 * Analytics Class
 * Handles website analytics data
 */
class Analytics {
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Get visitor statistics
     * @param string $startDate Start date (YYYY-MM-DD)
     * @param string $endDate End date (YYYY-MM-DD)
     * @return array Visitor statistics
     */
    public function getVisitorStats($startDate, $endDate) {
        // In a real implementation, this would query a table of visitor data
        // For now, we'll return dummy data
        
        $totalVisitors = rand(5000, 15000);
        $uniqueVisitors = rand(3000, $totalVisitors);
        $bounceRate = rand(30, 70);
        $avgSessionDuration = rand(60, 300);
        
        // Generate daily stats
        $dailyStats = [];
        $startTimestamp = strtotime($startDate);
        $endTimestamp = strtotime($endDate);
        $dayDiff = ($endTimestamp - $startTimestamp) / (60 * 60 * 24);
        
        for ($i = 0; $i <= $dayDiff; $i++) {
            $date = date('Y-m-d', strtotime("+{$i} days", $startTimestamp));
            $dailyStats[] = [
                'date' => $date,
                'visitors' => rand(100, 500),
                'unique_visitors' => rand(50, 300)
            ];
        }
        
        return [
            'total_visitors' => $totalVisitors,
            'unique_visitors' => $uniqueVisitors,
            'bounce_rate' => $bounceRate,
            'avg_session_duration' => $avgSessionDuration,
            'daily_stats' => $dailyStats
        ];
    }
    
    /**
     * Get page view statistics
     * @param string $startDate Start date (YYYY-MM-DD)
     * @param string $endDate End date (YYYY-MM-DD)
     * @return array Page view statistics
     */
    public function getPageViews($startDate, $endDate) {
        // In a real implementation, this would query a table of page view data
        // For now, we'll return dummy data
        
        $totalPageViews = rand(15000, 50000);
        $avgPagesPerSession = rand(2, 6);
        
        // Generate daily stats
        $dailyStats = [];
        $startTimestamp = strtotime($startDate);
        $endTimestamp = strtotime($endDate);
        $dayDiff = ($endTimestamp - $startTimestamp) / (60 * 60 * 24);
        
        for ($i = 0; $i <= $dayDiff; $i++) {
            $date = date('Y-m-d', strtotime("+{$i} days", $startTimestamp));
            $dailyStats[] = [
                'date' => $date,
                'page_views' => rand(300, 1500)
            ];
        }
        
        return [
            'total_page_views' => $totalPageViews,
            'avg_pages_per_session' => $avgPagesPerSession,
            'daily_stats' => $dailyStats
        ];
    }
    
    /**
     * Get top pages
     * @param string $startDate Start date (YYYY-MM-DD)
     * @param string $endDate End date (YYYY-MM-DD)
     * @param int $limit Number of pages to return
     * @return array Top pages
     */
    public function getTopPages($startDate, $endDate, $limit = 10) {
        // In a real implementation, this would query a table of page view data
        // For now, we'll return dummy data
        
        $pages = [
            ['page_url' => '/', 'page_title' => 'Home', 'page_views' => rand(1000, 5000)],
            ['page_url' => '/coaching-centers', 'page_title' => 'Coaching Centers', 'page_views' => rand(800, 3000)],
            ['page_url' => '/courses', 'page_title' => 'Courses', 'page_views' => rand(600, 2000)],
            ['page_url' => '/about-us', 'page_title' => 'About Us', 'page_views' => rand(400, 1500)],
            ['page_url' => '/contact-us', 'page_title' => 'Contact Us', 'page_views' => rand(300, 1000)],
            ['page_url' => '/blog', 'page_title' => 'Blog', 'page_views' => rand(200, 800)],
            ['page_url' => '/register', 'page_title' => 'Register', 'page_views' => rand(150, 600)],
            ['page_url' => '/login', 'page_title' => 'Login', 'page_views' => rand(100, 500)],
            ['page_url' => '/faq', 'page_title' => 'FAQ', 'page_views' => rand(80, 400)],
            ['page_url' => '/terms-of-service', 'page_title' => 'Terms of Service', 'page_views' => rand(50, 300)],
            ['page_url' => '/privacy-policy', 'page_title' => 'Privacy Policy', 'page_views' => rand(40, 200)],
            ['page_url' => '/coaching-centers/jee-coaching', 'page_title' => 'JEE Coaching Centers', 'page_views' => rand(30, 150)]
        ];
        
        // Sort by page views (descending)
        usort($pages, function($a, $b) {
            return $b['page_views'] - $a['page_views'];
        });
        
        // Calculate total page views
        $totalPageViews = array_sum(array_column($pages, 'page_views'));
        
        // Calculate percentage
        foreach ($pages as &$page) {
            $page['percentage'] = ($page['page_views'] / $totalPageViews) * 100;
        }
        
        // Limit results
        $pages = array_slice($pages, 0, $limit);
        
        return $pages;
    }
    
    /**
     * Get top referrers
     * @param string $startDate Start date (YYYY-MM-DD)
     * @param string $endDate End date (YYYY-MM-DD)
     * @param int $limit Number of referrers to return
     * @return array Top referrers
     */
    public function getTopReferrers($startDate, $endDate, $limit = 10) {
        // In a real implementation, this would query a table of referrer data
        // For now, we'll return dummy data
        
        $referrers = [
            ['referrer' => 'google.com', 'visitors' => rand(1000, 5000)],
            ['referrer' => 'facebook.com', 'visitors' => rand(800, 3000)],
            ['referrer' => 'bing.com', 'visitors' => rand(600, 2000)],
            ['referrer' => 'twitter.com', 'visitors' => rand(400, 1500)],
            ['referrer' => 'instagram.com', 'visitors' => rand(300, 1000)],
            ['referrer' => 'linkedin.com', 'visitors' => rand(200, 800)],
            ['referrer' => 'youtube.com', 'visitors' => rand(150, 600)],
            ['referrer' => '', 'visitors' => rand(1000, 4000)], // Direct traffic
            ['referrer' => 'quora.com', 'visitors' => rand(80, 400)],
            ['referrer' => 'reddit.com', 'visitors' => rand(50, 300)],
            ['referrer' => 'yahoo.com', 'visitors' => rand(40, 200)],
            ['referrer' => 'duckduckgo.com', 'visitors' => rand(30, 150)]
        ];
        
        // Sort by visitors (descending)
        usort($referrers, function($a, $b) {
            return $b['visitors'] - $a['visitors'];
        });
        
        // Calculate total visitors
        $totalVisitors = array_sum(array_column($referrers, 'visitors'));
        
        // Calculate percentage
        foreach ($referrers as &$referrer) {
            $referrer['percentage'] = ($referrer['visitors'] / $totalVisitors) * 100;
        }
        
        // Limit results
        $referrers = array_slice($referrers, 0, $limit);
        
        return $referrers;
    }
    
    /**
     * Get device statistics
     * @param string $startDate Start date (YYYY-MM-DD)
     * @param string $endDate End date (YYYY-MM-DD)
     * @return array Device statistics
     */
    public function getDeviceStats($startDate, $endDate) {
        // In a real implementation, this would query a table of device data
        // For now, we'll return dummy data
        
        $totalVisitors = rand(5000, 15000);
        $mobileVisitors = rand(2000, $totalVisitors * 0.6);
        $desktopVisitors = rand(1000, $totalVisitors - $mobileVisitors);
        $tabletVisitors = $totalVisitors - $mobileVisitors - $desktopVisitors;
        
        return [
            ['device' => 'Mobile', 'visitors' => $mobileVisitors, 'percentage' => ($mobileVisitors / $totalVisitors) * 100],
            ['device' => 'Desktop', 'visitors' => $desktopVisitors, 'percentage' => ($desktopVisitors / $totalVisitors) * 100],
            ['device' => 'Tablet', 'visitors' => $tabletVisitors, 'percentage' => ($tabletVisitors / $totalVisitors) * 100]
        ];
    }
    
    /**
     * Get browser statistics
     * @param string $startDate Start date (YYYY-MM-DD)
     * @param string $endDate End date (YYYY-MM-DD)
     * @return array Browser statistics
     */
    public function getBrowserStats($startDate, $endDate) {
        // In a real implementation, this would query a table of browser data
        // For now, we'll return dummy data
        
        $totalVisitors = rand(5000, 15000);
        $chromeVisitors = rand(2000, $totalVisitors * 0.6);
        $safariVisitors = rand(1000, $totalVisitors - $chromeVisitors);
        $firefoxVisitors = rand(500, $totalVisitors - $chromeVisitors - $safariVisitors);
        $edgeVisitors = rand(300, $totalVisitors - $chromeVisitors - $safariVisitors - $firefoxVisitors);
        $otherVisitors = $totalVisitors - $chromeVisitors - $safariVisitors - $firefoxVisitors - $edgeVisitors;
        
        return [
            ['browser' => 'Chrome', 'visitors' => $chromeVisitors, 'percentage' => ($chromeVisitors / $totalVisitors) * 100],
            ['browser' => 'Safari', 'visitors' => $safariVisitors, 'percentage' => ($safariVisitors / $totalVisitors) * 100],
            ['browser' => 'Firefox', 'visitors' => $firefoxVisitors, 'percentage' => ($firefoxVisitors / $totalVisitors) * 100],
            ['browser' => 'Edge', 'visitors' => $edgeVisitors, 'percentage' => ($edgeVisitors / $totalVisitors) * 100],
            ['browser' => 'Other', 'visitors' => $otherVisitors, 'percentage' => ($otherVisitors / $totalVisitors) * 100]
        ];
    }
    
    /**
     * Get country statistics
     * @param string $startDate Start date (YYYY-MM-DD)
     * @param string $endDate End date (YYYY-MM-DD)
     * @return array Country statistics
     */
    public function getCountryStats($startDate, $endDate) {
        // In a real implementation, this would query a table of country data
        // For now, we'll return dummy data
        
        $totalVisitors = rand(5000, 15000);
        $indiaVisitors = rand(2000, $totalVisitors * 0.6);
        $usVisitors = rand(1000, $totalVisitors - $indiaVisitors);
        $ukVisitors = rand(500, $totalVisitors - $indiaVisitors - $usVisitors);
        $canadaVisitors = rand(300, $totalVisitors - $indiaVisitors - $usVisitors - $ukVisitors);
        $otherVisitors = $totalVisitors - $indiaVisitors - $usVisitors - $ukVisitors - $canadaVisitors;
        
        return [
            ['country' => 'India', 'visitors' => $indiaVisitors, 'percentage' => ($indiaVisitors / $totalVisitors) * 100],
            ['country' => 'United States', 'visitors' => $usVisitors, 'percentage' => ($usVisitors / $totalVisitors) * 100],
            ['country' => 'United Kingdom', 'visitors' => $ukVisitors, 'percentage' => ($ukVisitors / $totalVisitors) * 100],
            ['country' => 'Canada', 'visitors' => $canadaVisitors, 'percentage' => ($canadaVisitors / $totalVisitors) * 100],
            ['country' => 'Other', 'visitors' => $otherVisitors, 'percentage' => ($otherVisitors / $totalVisitors) * 100]
        ];
    }
    
    /**
     * Track page view
     * @param string $pageUrl Page URL
     * @param string $pageTitle Page title
     * @param string $referrer Referrer URL
     * @param string $userAgent User agent
     * @param string $ipAddress IP address
     * @return bool True if tracking successful
     */
    public function trackPageView($pageUrl, $pageTitle, $referrer, $userAgent, $ipAddress) {
        // In a real implementation, this would insert a record into a page views table
        // For now, we'll just return true
        return true;
    }
    
    /**
     * Export analytics data
     * @param string $format Export format (csv, excel, pdf)
     * @param string $startDate Start date (YYYY-MM-DD)
     * @param string $endDate End date (YYYY-MM-DD)
     * @return string|bool File path or false on failure
     */
    public function exportData($format, $startDate, $endDate) {
        // In a real implementation, this would generate a file in the requested format
        // For now, we'll just return a dummy file path
        return 'exports/analytics_' . date('Ymd') . '.' . $format;
    }
}