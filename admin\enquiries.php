<?php
/**
 * Admin Enquiries
 */
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    require_once '../includes/autoload.php';
    
    // Check user authentication
    $user = new User();
    if (!$user->isLoggedIn() || !$user->isAdmin()) {
        header('Location: login.php');
        exit;
    }
    
    // Get admin info
    $adminInfo = $user->getUserData();
    
    // Initialize settings
    $settings = Settings::getInstance();
    
    // Initialize enquiry object
    $enquiryObj = new Enquiry();
    
    // Handle actions
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    $enquiryId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    $message = '';
    
    if ($action === 'delete' && $enquiryId > 0) {
        if ($enquiryObj->delete($enquiryId)) {
            $message = '<div class="alert alert-success">Enquiry deleted successfully.</div>';
        } else {
            $message = '<div class="alert alert-danger">Failed to delete enquiry.</div>';
        }
    } else if ($action === 'mark-read' && $enquiryId > 0) {
        if ($enquiryObj->update($enquiryId, ['status' => 'read'])) {
            $message = '<div class="alert alert-success">Enquiry marked as read.</div>';
        } else {
            $message = '<div class="alert alert-danger">Failed to update enquiry status.</div>';
        }
    } else if ($action === 'mark-unread' && $enquiryId > 0) {
        if ($enquiryObj->update($enquiryId, ['status' => 'unread'])) {
            $message = '<div class="alert alert-success">Enquiry marked as unread.</div>';
        } else {
            $message = '<div class="alert alert-danger">Failed to update enquiry status.</div>';
        }
    }
    
    // Get enquiries with pagination
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = 10;
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    $coachingId = isset($_GET['coaching_id']) ? (int)$_GET['coaching_id'] : 0;
    
    // Prepare filters
    $filters = [];
    if (!empty($status)) {
        $filters['status'] = $status;
    }
    if ($coachingId > 0) {
        $filters['coaching_id'] = $coachingId;
    }
    
    // Get enquiries
    $result = $enquiryObj->getAll($filters, $page, $limit);
    $enquiries = $result['enquiries'] ?? [];
    $totalPages = $result['total_pages'] ?? 1;
    $totalCount = $result['total_count'] ?? 0;
    
    // Get coaching centers for filter dropdown
    $coachingObj = new CoachingCenter();
    $coachings = $coachingObj->getAllForDropdown();
    
} catch (Exception $e) {
    die('<div style="color:red;padding:20px;border:2px solid red;margin:20px;">'
        . '<h2>Error</h2>'
        . '<p><strong>Message:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>'
        . '<p><strong>File:</strong> ' . htmlspecialchars($e->getFile()) . '</p>'
        . '<p><strong>Line:</strong> ' . htmlspecialchars($e->getLine()) . '</p>'
        . '</div>');
}

// Page title
$pageTitle = 'Enquiries';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Admin Panel | <?php echo $settings->getSiteName(); ?></title>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo getAssetUrl($settings->getSiteFavicon()); ?>" type="image/x-icon">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Admin CSS -->
    <link rel="stylesheet" href="assets/css/admin.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <?php include 'templates/sidebar.php'; ?>
        
        <!-- Main Content -->
        <div class="admin-main" id="adminMain">
            <!-- Header -->
            <?php include 'templates/header.php'; ?>
            
            <!-- Content -->
            <div class="admin-content">
                <div class="page-title">
                    <div>
                        <h1><?php echo $pageTitle; ?></h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="dashboard.php">Home</a></li>
                                <li class="breadcrumb-item active" aria-current="page"><?php echo $pageTitle; ?></li>
                            </ol>
                        </nav>
                    </div>
                </div>
                
                <?php echo $message; ?>
                
                <!-- Filters -->
                <div class="admin-card mb-4">
                    <div class="admin-card-body">
                        <form action="" method="get" class="row g-3">
                            <div class="col-md-5">
                                <label for="coaching_id" class="form-label">Coaching Center</label>
                                <select class="form-select" id="coaching_id" name="coaching_id">
                                    <option value="">All Coaching Centers</option>
                                    <?php foreach ($coachings as $coaching): ?>
                                        <option value="<?php echo $coaching['coaching_id']; ?>" <?php echo $coachingId == $coaching['coaching_id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($coaching['coaching_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">All Status</option>
                                    <option value="unread" <?php echo $status === 'unread' ? 'selected' : ''; ?>>Unread</option>
                                    <option value="read" <?php echo $status === 'read' ? 'selected' : ''; ?>>Read</option>
                                </select>
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <a href="enquiries.php" class="btn btn-secondary w-100">
                                    <i class="fas fa-sync-alt"></i> Reset
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Enquiries Table -->
                <div class="admin-card">
                    <div class="admin-card-header">
                        <h2 class="admin-card-title">
                            <i class="fas fa-envelope me-2"></i> All Enquiries
                        </h2>
                        <span class="badge bg-primary"><?php echo $totalCount; ?> Total</span>
                    </div>
                    <div class="admin-card-body p-0">
                        <div class="table-responsive">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Phone</th>
                                        <th>Coaching Center</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($enquiries)): ?>
                                        <?php foreach ($enquiries as $enquiry): ?>
                                            <tr class="<?php echo $enquiry['status'] === 'unread' ? 'table-active' : ''; ?>">
                                                <td><?php echo $enquiry['enquiry_id']; ?></td>
                                                <td><?php echo htmlspecialchars($enquiry['name']); ?></td>
                                                <td><?php echo htmlspecialchars($enquiry['email']); ?></td>
                                                <td><?php echo htmlspecialchars($enquiry['phone']); ?></td>
                                                <td><?php echo htmlspecialchars($enquiry['coaching_name']); ?></td>
                                                <td>
                                                    <?php if ($enquiry['status'] === 'unread'): ?>
                                                        <span class="badge bg-danger">Unread</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-success">Read</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo date('M d, Y', strtotime($enquiry['created_at'])); ?></td>
                                                <td>
                                                    <div class="btn-group">
                                                        <a href="#" data-bs-toggle="modal" data-bs-target="#enquiryModal<?php echo $enquiry['enquiry_id']; ?>" class="btn btn-sm btn-info" title="View">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <?php if ($enquiry['status'] === 'unread'): ?>
                                                            <a href="enquiries.php?action=mark-read&id=<?php echo $enquiry['enquiry_id']; ?>" class="btn btn-sm btn-success" title="Mark as Read">
                                                                <i class="fas fa-check"></i>
                                                            </a>
                                                        <?php else: ?>
                                                            <a href="enquiries.php?action=mark-unread&id=<?php echo $enquiry['enquiry_id']; ?>" class="btn btn-sm btn-warning" title="Mark as Unread">
                                                                <i class="fas fa-undo"></i>
                                                            </a>
                                                        <?php endif; ?>
                                                        <a href="enquiries.php?action=delete&id=<?php echo $enquiry['enquiry_id']; ?>" class="btn btn-sm btn-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this enquiry?');">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                    </div>
                                                    
                                                    <!-- Enquiry Modal -->
                                                    <div class="modal fade" id="enquiryModal<?php echo $enquiry['enquiry_id']; ?>" tabindex="-1" aria-labelledby="enquiryModalLabel<?php echo $enquiry['enquiry_id']; ?>" aria-hidden="true">
                                                        <div class="modal-dialog modal-lg">
                                                            <div class="modal-content">
                                                                <div class="modal-header">
                                                                    <h5 class="modal-title" id="enquiryModalLabel<?php echo $enquiry['enquiry_id']; ?>">Enquiry Details</h5>
                                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                </div>
                                                                <div class="modal-body">
                                                                    <div class="row mb-3">
                                                                        <div class="col-md-6">
                                                                            <h6>Enquiry Information</h6>
                                                                            <table class="table table-bordered">
                                                                                <tr>
                                                                                    <th>ID</th>
                                                                                    <td><?php echo $enquiry['enquiry_id']; ?></td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <th>Date</th>
                                                                                    <td><?php echo date('M d, Y h:i A', strtotime($enquiry['created_at'])); ?></td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <th>Status</th>
                                                                                    <td>
                                                                                        <?php if ($enquiry['status'] === 'unread'): ?>
                                                                                            <span class="badge bg-danger">Unread</span>
                                                                                        <?php else: ?>
                                                                                            <span class="badge bg-success">Read</span>
                                                                                        <?php endif; ?>
                                                                                    </td>
                                                                                </tr>
                                                                            </table>
                                                                        </div>
                                                                        <div class="col-md-6">
                                                                            <h6>Contact Information</h6>
                                                                            <table class="table table-bordered">
                                                                                <tr>
                                                                                    <th>Name</th>
                                                                                    <td><?php echo htmlspecialchars($enquiry['name']); ?></td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <th>Email</th>
                                                                                    <td><?php echo htmlspecialchars($enquiry['email']); ?></td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <th>Phone</th>
                                                                                    <td><?php echo htmlspecialchars($enquiry['phone']); ?></td>
                                                                                </tr>
                                                                            </table>
                                                                        </div>
                                                                    </div>
                                                                    
                                                                    <div class="mb-3">
                                                                        <h6>Coaching Center</h6>
                                                                        <p><?php echo htmlspecialchars($enquiry['coaching_name']); ?></p>
                                                                    </div>
                                                                    
                                                                    <div class="mb-3">
                                                                        <h6>Message</h6>
                                                                        <div class="p-3 bg-light rounded">
                                                                            <?php echo nl2br(htmlspecialchars($enquiry['message'])); ?>
                                                                        </div>
                                                                    </div>
                                                                    
                                                                    <?php if (!empty($enquiry['course_id'])): ?>
                                                                        <div class="mb-3">
                                                                            <h6>Course</h6>
                                                                            <p><?php echo htmlspecialchars($enquiry['course_name'] ?? 'N/A'); ?></p>
                                                                        </div>
                                                                    <?php endif; ?>
                                                                </div>
                                                                <div class="modal-footer">
                                                                    <a href="mailto:<?php echo htmlspecialchars($enquiry['email']); ?>" class="btn btn-primary">
                                                                        <i class="fas fa-reply"></i> Reply via Email
                                                                    </a>
                                                                    <?php if ($enquiry['status'] === 'unread'): ?>
                                                                        <a href="enquiries.php?action=mark-read&id=<?php echo $enquiry['enquiry_id']; ?>" class="btn btn-success">
                                                                            <i class="fas fa-check"></i> Mark as Read
                                                                        </a>
                                                                    <?php else: ?>
                                                                        <a href="enquiries.php?action=mark-unread&id=<?php echo $enquiry['enquiry_id']; ?>" class="btn btn-warning">
                                                                            <i class="fas fa-undo"></i> Mark as Unread
                                                                        </a>
                                                                    <?php endif; ?>
                                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="8" class="text-center">No enquiries found.</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <div class="admin-card-footer">
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center mb-0">
                                    <?php if ($page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $page - 1; ?>&status=<?php echo urlencode($status); ?>&coaching_id=<?php echo $coachingId; ?>" aria-label="Previous">
                                                <span aria-hidden="true">&laquo;</span>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                    
                                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo urlencode($status); ?>&coaching_id=<?php echo $coachingId; ?>">
                                                <?php echo $i; ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>
                                    
                                    <?php if ($page < $totalPages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $page + 1; ?>&status=<?php echo urlencode($status); ?>&coaching_id=<?php echo $coachingId; ?>" aria-label="Next">
                                                <span aria-hidden="true">&raquo;</span>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Footer -->
            <?php include 'templates/footer.php'; ?>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Admin JS -->
    <script src="assets/js/admin.js"></script>
    
    <script>
        // Mark enquiry as read when modal is opened
        var enquiryModals = document.querySelectorAll('[id^="enquiryModal"]');
        enquiryModals.forEach(function(modal) {
            modal.addEventListener('shown.bs.modal', function() {
                var enquiryId = this.id.replace('enquiryModal', '');
                var statusBadge = this.querySelector('.badge');
                
                if (statusBadge && statusBadge.classList.contains('bg-danger')) {
                    fetch('ajax/mark-enquiry-read.php?id=' + enquiryId)
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                statusBadge.classList.remove('bg-danger');
                                statusBadge.classList.add('bg-success');
                                statusBadge.textContent = 'Read';
                            }
                        });
                }
            });
        });
    </script>
</body>
</html>