<?php
/**
 * Category Class
 * Handles course categories
 */
class Category {
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Add a new category
     * @param array $data Category data
     * @return int|bool Category ID or false on failure
     */
    public function add($data) {
        // Generate slug
        $data['slug'] = Utility::generateUniqueSlug($data['category_name'], 'course_categories');
        
        return $this->db->insert('course_categories', $data);
    }
    
    /**
     * Update a category
     * @param int $categoryId Category ID
     * @param array $data Category data
     * @return bool True if update successful
     */
    public function update($categoryId, $data) {
        // Check if category name is being changed
        if (isset($data['category_name'])) {
            $currentCategory = $this->getById($categoryId);
            
            if ($currentCategory && $data['category_name'] !== $currentCategory['category_name']) {
                $data['slug'] = Utility::generateUniqueSlug($data['category_name'], 'course_categories', 'slug', $categoryId);
            }
        }
        
        return $this->db->update('course_categories', $data, 'category_id = ?', [$categoryId]);
    }
    
    /**
     * Delete a category
     * @param int $categoryId Category ID
     * @return bool True if deletion successful
     */
    public function delete($categoryId) {
        // Check if category has coaching centers
        $coachingCount = $this->db->count('coaching_categories', 'category_id = ?', [$categoryId]);
        
        if ($coachingCount > 0) {
            return false;
        }
        
        // Check if category has courses
        $courseCount = $this->db->count('courses', 'category_id = ?', [$categoryId]);
        
        if ($courseCount > 0) {
            return false;
        }
        
        // Check if category has child categories
        $childCount = $this->db->count('course_categories', 'parent_id = ?', [$categoryId]);
        
        if ($childCount > 0) {
            return false;
        }
        
        return $this->db->delete('course_categories', 'category_id = ?', [$categoryId]);
    }
    
    /**
     * Get category by ID
     * @param int $categoryId Category ID
     * @return array|null Category data
     */
    public function getById($categoryId) {
        return $this->db->fetchRow(
            "SELECT c.*, p.category_name as parent_name
             FROM course_categories c
             LEFT JOIN course_categories p ON c.parent_id = p.category_id
             WHERE c.category_id = ?",
            [$categoryId]
        );
    }
    
    /**
     * Get category by slug
     * @param string $slug Category slug
     * @return array|null Category data
     */
    public function getBySlug($slug) {
        // Map of slugs to correct category names
        $categoryNames = [
            'jee-engineering' => 'JEE/Engineering',
            'neet-medical' => 'NEET/Medical',
            'upsc-civil-services' => 'UPSC/Civil Services',
            'law-entrance' => 'Law Entrance',
            'mba-entrance' => 'MBA Entrance',
            'banking-ssc' => 'Banking/SSC',
            'language-learning' => 'Language Learning',
            'computer-it' => 'Computer/IT',
            'arts-design' => 'Arts & Design',
            'music-dance' => 'Music & Dance',
            'sports-fitness' => 'Sports & Fitness',
            'school-tuition' => 'School Tuition'
        ];
        
        // Get the category directly from the database
        $category = $this->db->fetchRow(
            "SELECT c.*, p.category_name as parent_name
             FROM course_categories c
             LEFT JOIN course_categories p ON c.parent_id = p.category_id
             WHERE c.slug = ?",
            [$slug]
        );
        
        // Removed hardcoded override to always use the database value for category_name
        
        return $category;
    }
    
    /**
     * Get all categories
     * @param bool $activeOnly Get only active categories
     * @return array Categories
     */
    public function getAll($activeOnly = true) {
        $where = $activeOnly ? "WHERE c.status = 'active'" : "";
        
        return $this->db->fetchAll(
            "SELECT c.*, p.category_name as parent_name
             FROM course_categories c
             LEFT JOIN course_categories p ON c.parent_id = p.category_id
             {$where}
             ORDER BY c.display_order ASC, c.category_name ASC"
        );
    }
    
    /**
     * Get parent categories
     * @param bool $activeOnly Get only active categories
     * @return array Parent categories
     */
    public function getParents($activeOnly = true) {
        $where = $activeOnly ? "WHERE parent_id IS NULL AND status = 'active'" : "WHERE parent_id IS NULL";
        
        return $this->db->fetchAll(
            "SELECT * FROM course_categories
             {$where}
             ORDER BY display_order ASC, category_name ASC"
        );
    }
    
    /**
     * Get child categories
     * @param int $parentId Parent category ID
     * @param bool $activeOnly Get only active categories
     * @return array Child categories
     */
    public function getChildren($parentId, $activeOnly = true) {
        $where = $activeOnly ? "WHERE parent_id = ? AND status = 'active'" : "WHERE parent_id = ?";
        
        return $this->db->fetchAll(
            "SELECT * FROM course_categories
             {$where}
             ORDER BY display_order ASC, category_name ASC",
            [$parentId]
        );
    }
    
    /**
     * Get categories with coaching count
     * @return array Categories with coaching count
     */
    public function getCategoriesWithCount() {
        return $this->db->fetchAll(
            "SELECT c.*, COUNT(cc.coaching_id) as coaching_count
             FROM course_categories c
             LEFT JOIN coaching_categories cc ON c.category_id = cc.category_id
             LEFT JOIN coaching_centers ctr ON cc.coaching_id = ctr.coaching_id AND ctr.status = 'active'
             WHERE c.status = 'active'
             GROUP BY c.category_id
             ORDER BY c.display_order ASC, c.category_name ASC"
        );
    }
    
    /**
     * Get popular categories
     * @param int $limit Number of categories to get
     * @return array Popular categories
     */
    public function getPopular($limit = 6) {
        return $this->db->fetchAll(
            "SELECT c.*, COUNT(cc.coaching_id) as coaching_count
             FROM course_categories c
             LEFT JOIN coaching_categories cc ON c.category_id = cc.category_id
             LEFT JOIN coaching_centers ctr ON cc.coaching_id = ctr.coaching_id AND ctr.status = 'active'
             WHERE c.status = 'active'
             GROUP BY c.category_id
             ORDER BY coaching_count DESC, c.display_order ASC
             LIMIT ?",
            [$limit]
        );
    }
    
    /**
     * Get category tree
     * @param bool $activeOnly Get only active categories
     * @return array Category tree
     */
    public function getCategoryTree($activeOnly = true) {
        $categories = $this->getAll($activeOnly);
        $tree = [];
        
        // Build parent categories
        foreach ($categories as $category) {
            if ($category['parent_id'] === null) {
                $category['children'] = [];
                $tree[$category['category_id']] = $category;
            }
        }
        
        // Add child categories
        foreach ($categories as $category) {
            if ($category['parent_id'] !== null && isset($tree[$category['parent_id']])) {
                $tree[$category['parent_id']]['children'][] = $category;
            }
        }
        
        return array_values($tree);
    }
    
    /**
     * Get category dropdown options
     * @param bool $includeEmpty Include empty option
     * @param bool $activeOnly Get only active categories
     * @return array Category dropdown options
     */
    public function getCategoryOptions($includeEmpty = true, $activeOnly = true) {
        $tree = $this->getCategoryTree($activeOnly);
        $options = [];
        
        if ($includeEmpty) {
            $options[] = [
                'value' => '',
                'text' => '-- Select Category --'
            ];
        }
        
        foreach ($tree as $parent) {
            $options[] = [
                'value' => $parent['category_id'],
                'text' => $parent['category_name']
            ];
            
            foreach ($parent['children'] as $child) {
                $options[] = [
                    'value' => $child['category_id'],
                    'text' => '-- ' . $child['category_name']
                ];
            }
        }
        
        return $options;
    }
}