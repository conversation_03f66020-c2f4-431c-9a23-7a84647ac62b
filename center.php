<?php
/**
 * Coaching Center Detail Page
 * This file handles the coaching center detail pages
 */
require_once 'includes/autoload.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Get settings
$settings = Settings::getInstance();

// Include dummy data for development
require_once 'includes/dummy-data.php';



// Get coaching slug from URL
$slug = isset($_GET['slug']) ? $_GET['slug'] : '';

if (empty($slug)) {
    redirect('index.php');
}

// Get location filter if provided
$locationFilter = isset($_GET['location_id']) ? (int)$_GET['location_id'] : null;

// Get coaching center details
$coachingObj = new CoachingCenter();
$coaching = $coachingObj->getBySlug($slug);

// If coaching center not found, use dummy data
if (empty($coaching)) {
    $dummyCoachings = getDummyCoachingCenters(10);
    foreach ($dummyCoachings as $dummyCoaching) {
        if ($dummyCoaching['slug'] == $slug) {
            $coaching = $dummyCoaching;
            break;
        }
    }
    
    // If still not found, redirect to home page
    if (empty($coaching)) {
        redirect('index.php');
    }
}

// Get courses offered by this coaching center
if ($locationFilter) {
    // If location filter is applied, get courses for that specific location
    $courses = $coachingObj->getCourses($coaching['coaching_id'], $locationFilter);
} else {
    // Otherwise, get unique courses to avoid duplicates
    $courses = $coachingObj->getUniqueCourses($coaching['coaching_id']);
}


// Get success stories
$successStories = $coachingObj->getSuccessStories($coaching['coaching_id']);

// Get reviews
$reviews = $coachingObj->getReviews($coaching['coaching_id']);

// Get gallery images
$galleryImages = Database::getInstance()->fetchAll(
    'SELECT * FROM coaching_images WHERE coaching_id = ? ORDER BY display_order ASC, image_id DESC',
    [$coaching['coaching_id']]
);


// Page title and meta
$pageTitle = $coaching['coaching_name'];
$pageDescription = isset($coaching['description']) && !empty($coaching['description']) 
    ? substr(strip_tags($coaching['description']), 0, 160) 
    : $coaching['coaching_name'] . ' - Coaching Center';
$pageKeywords = $coaching['coaching_name'] . ', coaching center, ' . $settings->getMetaKeywords();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?php echo getMetaTags($pageTitle . ' - ' . $settings->getSiteName(), $pageDescription, $pageKeywords); ?>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo getAssetUrl($settings->getSiteFavicon()); ?>" type="image/x-icon">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo getAssetUrl('css/style.css'); ?>">
    
    <!-- Page-specific styles -->
    <style>
        .course-tags {
            margin-top: 10px;
        }
        
        .location-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .location-tag {
            display: inline-flex;
            align-items: center;
            background-color: #e9f5ff;
            color: #0066cc;
            padding: 4px 10px;
            border-radius: 20px;
            font-size: 12px;
        }
        
        .location-tag i {
            margin-right: 5px;
            font-size: 10px;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <?php include 'templates/header.php'; ?>
    
    <!-- Main Content -->
    <main>
        <!-- Page Header with Banner -->
        <section class="page-header">
            <?php if (!empty($coaching['banner_image'])): ?>
                <div class="coaching-banner">
                    <img src="<?php echo getUploadUrl($coaching['banner_image']); ?>" alt="<?php echo $coaching['coaching_name']; ?> Banner" class="img-fluid w-100">
                </div>
            <?php endif; ?>
            <div class="container">
                <h1><?php echo $coaching['coaching_name']; ?></h1>
            </div>
        </section>


                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="index.php">Home</a></li>
                        <li class="breadcrumb-item"><a href="categories.php">Categories</a></li>
                        <?php if (!empty($coaching['categories'])): ?>
                            <li class="breadcrumb-item"><a href="<?php echo getCategoryUrl($coaching['categories'][0]['category_slug']); ?>"><?php echo $coaching['categories'][0]['category_name']; ?></a></li>
                        <?php endif; ?>
                        <li class="breadcrumb-item active" aria-current="page"><?php echo $coaching['coaching_name']; ?></li>
                    </ol>
                </nav>
            </div>
        </section>
        
        <!-- Coaching Center Overview -->
        <section class="coaching-overview">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8">
                        <div class="coaching-detail-card">
                            <div class="coaching-header">
                                <div class="coaching-logo">
                                    <img src="<?php echo isset($coaching['logo']) ? getUploadUrl($coaching['logo']) : getAssetUrl('images/default-logo.png'); ?>" alt="<?php echo $coaching['coaching_name']; ?>">
                                </div>
                                <div class="coaching-title">
                                    <h2><?php echo $coaching['coaching_name']; ?></h2>
                                    <div class="coaching-meta">
                                        <span><i class="fas fa-map-marker-alt"></i> <?php echo isset($coaching['city_name']) ? $coaching['city_name'] : 'N/A'; ?><?php echo isset($coaching['state_name']) ? ', ' . $coaching['state_name'] : ''; ?></span>
                                        <span><i class="fas fa-star"></i> <?php echo isset($coaching['avg_rating']) ? number_format($coaching['avg_rating'], 1) : '0.0'; ?> (<?php echo isset($coaching['total_reviews']) ? $coaching['total_reviews'] : '0'; ?> reviews)</span>
                                        <?php if (!empty($coaching['established_year'])): ?>
                                            <span><i class="fas fa-calendar-alt"></i> Est. <?php echo $coaching['established_year']; ?></span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <?php if (!empty($coaching['locations'])): ?>
                                    <?php endif; ?>
                                    <div class="coaching-categories">
                                        <?php if (!empty($coaching['categories'])): ?>
                                            <?php foreach ($coaching['categories'] as $category): ?>
                                                <a href="<?php echo getCategoryUrl($category['category_slug']); ?>" class="category-badge"><?php echo $category['category_name']; ?></a>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <span class="category-badge">General</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="coaching-description">
                                <h3>About <?php echo $coaching['coaching_name']; ?></h3>
                                <p><?php echo isset($coaching['description']) ? $coaching['description'] : 'No description available.'; ?></p>
                            </div>
                            
                            <div class="coaching-facilities">
                                <h3>Facilities</h3>
                                <div class="facilities-list">
                                    <?php if (!empty($coaching['facilities'])): ?>
                                        <?php foreach ($coaching['facilities'] as $facility): ?>
                                            <div class="facility-item">
                                                <i class="<?php echo !empty($facility['icon']) ? $facility['icon'] : 'fas fa-check-circle'; ?>"></i>
                                                <span><?php echo $facility['facility_name']; ?></span>
                                            </div>
                                        <?php endforeach; ?>
										<?php endif; ?>
                                </div>
                            </div>
                            
                            <?php if (!empty($galleryImages)): ?>
                            <div class="coaching-gallery mt-4">
                                <h3>Gallery</h3>
                                <div class="small-gallery-carousel">
                                    <div id="galleryCarousel" class="carousel slide" data-bs-ride="carousel">
                                        <div class="carousel-inner">
                                            <?php foreach ($galleryImages as $index => $img): ?>
                                                <div class="carousel-item <?php echo $index === 0 ? 'active' : ''; ?>">
                                                    <div class="gallery-frame">
                                                        <img src="<?php echo getUploadUrl($img['image_path']); ?>" class="d-block rounded" alt="Gallery Image" width="250" height="200">
                                                        <?php if (!empty($img['caption'])): ?>
                                                            <div class="carousel-caption d-none d-md-block">
                                                                <p><?php echo htmlspecialchars($img['caption']); ?></p>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                        <button class="carousel-control-prev" type="button" data-bs-target="#galleryCarousel" data-bs-slide="prev">
                                            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                            <span class="visually-hidden">Previous</span>
                                        </button>
                                        <button class="carousel-control-next" type="button" data-bs-target="#galleryCarousel" data-bs-slide="next">
                                            <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                            <span class="visually-hidden">Next</span>
                                        </button>
                                        <div class="carousel-indicators">
                                            <?php foreach ($galleryImages as $index => $img): ?>
                                                <button type="button" data-bs-target="#galleryCarousel" data-bs-slide-to="<?php echo $index; ?>" <?php echo $index === 0 ? 'class="active" aria-current="true"' : ''; ?> aria-label="Slide <?php echo $index + 1; ?>"></button>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Courses Section -->
                        <div class="coaching-detail-card">
                            <h3>
                                Courses Offered
                                <?php if ($locationFilter): ?>
                                    <?php 
                                        $locationName = '';
                                        foreach ($coaching['locations'] as $loc) {
                                            if ($loc['location_id'] == $locationFilter) {
                                                $locationName = $loc['location_name'] . ', ' . $loc['city_name'];
                                                break;
                                            }
                                        }
                                    ?>
                                    <small class="text-muted">(Available at <?php echo htmlspecialchars($locationName); ?>)</small>
                                <?php endif; ?>
                            </h3>
                            <div class="courses-list">
                                <?php if (empty($courses)): ?>
                                    <div class="alert alert-info">
                                        <?php if ($locationFilter): ?>
                                            No courses available at this location. <a href="<?php echo "center.php?slug={$coaching['slug']}"; ?>">View all courses</a>
                                        <?php else: ?>
                                            No courses available.
                                        <?php endif; ?>
                                    </div>
                                <?php else: ?>
                                    <?php foreach ($courses as $course): ?>
                                        <div class="course-item">
                                            <div class="course-header">
                                                <h4><?php echo isset($course['course_name']) ? $course['course_name'] : 'Unnamed Course'; ?></h4>
                                                <div class="course-meta">
                                                    <span><i class="fas fa-clock"></i> <?php echo isset($course['duration']) ? $course['duration'] : 'Duration not specified'; ?></span>
                                                    <span><i class="fas fa-rupee-sign"></i> <?php echo !empty($course['fee_structure']) ? $course['fee_structure'] : 'not mentioned'; ?></span>
                                                    <?php if (!empty($course['batch_size'])): ?>
                                                        <span><i class="fas fa-users"></i> Batch Size: <?php echo $course['batch_size']; ?></span>
                                                    <?php endif; ?>
                                                </div>
                                                
                                                <?php if (!$locationFilter): ?>
                                                    <?php 
                                                        // Get course locations
                                                        $courseLocations = $coachingObj->getCourseLocations($course['course_id']);
                                                        if (!empty($courseLocations)):
                                                    ?>
                                                        <div class="course-tags">
                                                            <div class="location-tags">
                                                                <?php foreach ($courseLocations as $loc): ?>
                                                                    <span class="location-tag">
                                                                        <i class="fas fa-map-marker-alt"></i> 
                                                                        <?php echo htmlspecialchars($loc['location_name']); ?>
                                                                    </span>
                                                                <?php endforeach; ?>
                                                            </div>
                                                        </div>
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                            </div>
                                            <div class="course-description">
                                                <p><?php echo isset($course['description']) ? $course['description'] : 'No description available.'; ?></p>
                                            </div>
                                            <div class="course-footer">
                                                <span class="start-date"><i class="fas fa-calendar-alt"></i> Next Batch: 
                                                <?php 
                                                if (!empty($course['next_batch_date'])) {
                                                    echo date('d M Y', strtotime($course['next_batch_date']));
                                                } elseif (!empty($course['start_date'])) {
                                                    echo date('d M Y', strtotime($course['start_date']));
                                                } else {
                                                    echo 'not mentioned';
                                                }
                                                ?>
                                                </span>
                                                <a href="#enquiry-form" class="btn btn-sm btn-primary" data-course-id="<?php echo isset($course['course_id']) ? $course['course_id'] : ''; ?>">Enquire Now</a>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <!-- Success Stories Section -->
                        <div class="coaching-detail-card">
                            <h3>Success Stories</h3>
                            <div class="success-stories-list">
                                <?php foreach ($successStories as $story): ?>
                                    <div class="success-story-item">
                                        <div class="student-image">
                                            <img src="<?php echo isset($story['image']) ? getUploadUrl($story['image']) : getAssetUrl('images/dummy/student.jpg'); ?>" alt="<?php echo $story['student_name']; ?>">
                                        </div>
                                        <div class="student-details">
                                            <h4><?php echo $story['student_name']; ?></h4>
                                            <div class="achievement">
                                                <span class="badge bg-success"><?php echo $story['achievement']; ?></span>
                                                <span class="year"><?php echo $story['year']; ?></span>
                                            </div>
                                            <p class="testimonial"><?php echo $story['testimonial']; ?></p>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        
                        <!-- Reviews Section -->
                        <div class="coaching-detail-card">
                            <h3>Reviews</h3>
                            <div class="reviews-list">
                                <?php foreach ($reviews as $review): ?>
                                    <div class="review-item">
                                        <div class="review-header">
                                            <div class="reviewer-info">
                                                <img src="<?php echo isset($review['profile_image']) ? getUploadUrl($review['profile_image']) : getAssetUrl('images/dummy/user.jpg'); ?>" alt="<?php echo $review['user_name']; ?>">
                                                <h4><?php echo $review['user_name']; ?></h4>
                                            </div>
                                            <div class="review-rating">
                                                <div class="stars">
                                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                                        <i class="fas fa-star <?php echo $i <= $review['rating'] ? 'filled' : ''; ?>"></i>
                                                    <?php endfor; ?>
                                                </div>
                                                <span class="review-date"><?php echo date('d M Y', strtotime($review['created_at'])); ?></span>
                                            </div>
                                        </div>
                                        <div class="review-content">
                                            <p><?php echo $review['review']; ?></p>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            
                            <div class="write-review">
                                <h4>Write a Review</h4>
                                <form id="review-form" action="submit-review.php" method="post">
                                    <input type="hidden" name="coaching_id" value="<?php echo $coaching['coaching_id']; ?>">
                                    
                                    <div class="mb-3">
                                        <label for="rating" class="form-label">Rating</label>
                                        <div class="rating-input">
                                            <?php for ($i = 5; $i >= 1; $i--): ?>
                                                <input type="radio" id="star<?php echo $i; ?>" name="rating" value="<?php echo $i; ?>" <?php echo $i == 5 ? 'checked' : ''; ?>>
                                                <label for="star<?php echo $i; ?>"><i class="fas fa-star"></i></label>
                                            <?php endfor; ?>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="review" class="form-label">Your Review</label>
                                        <textarea class="form-control" id="review" name="review" rows="4" required></textarea>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary">Submit Review</button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4">
                        <!-- Contact Information -->
                        <div class="coaching-sidebar-card">
                            <h3>Contact Information</h3>
                            <div class="contact-info">
                                <div class="contact-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <div>
                                        <h4>Address</h4>
                                        <p><?php echo $coaching['address']; ?></p>
                                        <p><?php echo $coaching['city_name']; ?>, <?php echo $coaching['state_name']; ?></p>
                                    </div>
                                </div>
                                
                                <div class="contact-item">
                                    <i class="fas fa-phone"></i>
                                    <div>
                                        <h4>Phone</h4>
                                        <p><a href="tel:<?php echo $coaching['phone']; ?>"><?php echo $coaching['phone']; ?></a></p>
                                    </div>
                                </div>
                                
                                <div class="contact-item">
                                    <i class="fas fa-envelope"></i>
                                    <div>
                                        <h4>Email</h4>
                                        <p><a href="mailto:<?php echo $coaching['email']; ?>"><?php echo $coaching['email']; ?></a></p>
                                    </div>
                                </div>
                                
                                <?php if (!empty($coaching['website'])): ?>
                                    <div class="contact-item">
                                        <i class="fas fa-globe"></i>
                                        <div>
                                            <h4>Website</h4>
                                            <p><a href="<?php echo $coaching['website']; ?>" target="_blank"><?php echo $coaching['website']; ?></a></p>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="contact-social">
                                    <?php if (!empty($coaching['facebook'])): ?>
                                        <a href="<?php echo $coaching['facebook']; ?>" target="_blank"><i class="fab fa-facebook-f"></i></a>
                                    <?php endif; ?>
                                    
                                    <?php if (!empty($coaching['twitter'])): ?>
                                        <a href="<?php echo $coaching['twitter']; ?>" target="_blank"><i class="fab fa-twitter"></i></a>
                                    <?php endif; ?>
                                    
                                    <?php if (!empty($coaching['instagram'])): ?>
                                        <a href="<?php echo $coaching['instagram']; ?>" target="_blank"><i class="fab fa-instagram"></i></a>
                                    <?php endif; ?>
                                    
                                    <?php if (!empty($coaching['youtube'])): ?>
                                        <a href="<?php echo $coaching['youtube']; ?>" target="_blank"><i class="fab fa-youtube"></i></a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Center Locations -->
                        <?php if (!empty($coaching['locations'])): ?>
                        <div class="coaching-sidebar-card">
                            <h3>Center Locations</h3>
                            <div class="center-locations">
                                <?php foreach ($coaching['locations'] as $index => $location): ?>
                                    <div class="location-item-sidebar">
                                        <div class="location-header">
                                            
                                            <h6><span> <i class="fas fa-map-marker-alt"></i> <?php echo htmlspecialchars($location['location_name']); ?> - <?php echo htmlspecialchars($location['city_name']); ?></h6>
                                        </div>
                                        <div class="location-details">
                                            <p>
                                                <?php if (!empty($location['address'])): ?>
                                                    <?php echo nl2br(htmlspecialchars($location['address'])); ?>
                                                <?php endif; ?>
                                              <!--  <?php if (!empty($location['pincode'])): ?> - <?php echo htmlspecialchars($location['pincode']); ?><?php endif; ?>-->
                                            </p>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <!-- Enquiry Form -->
                        <div class="coaching-sidebar-card" id="enquiry-form">
                            <h3>Enquire Now</h3>
                            
                            <?php if (isset($_GET['enquiry']) && $_GET['enquiry'] === 'success'): ?>
                                <div class="alert alert-success">
                                    Thank you for your enquiry! We will get back to you soon.
                                </div>
                            <?php elseif (isset($_GET['enquiry']) && $_GET['enquiry'] === 'error'): ?>
                                <div class="alert alert-danger">
                                    <?php 
                                    if (isset($_SESSION['enquiry_error'])) {
                                        echo $_SESSION['enquiry_error'];
                                        unset($_SESSION['enquiry_error']);
                                    } elseif (isset($_SESSION['enquiry_errors']) && is_array($_SESSION['enquiry_errors'])) {
                                        echo '<ul class="mb-0">';
                                        foreach ($_SESSION['enquiry_errors'] as $error) {
                                            echo '<li>' . htmlspecialchars($error) . '</li>';
                                        }
                                        echo '</ul>';
                                        unset($_SESSION['enquiry_errors']);
                                    } else {
                                        echo 'An error occurred while submitting your enquiry. Please try again.';
                                    }
                                    ?>
                                </div>
                            <?php endif; ?>
                            
                            <form action="submit-enquiry.php" method="post">
                                <input type="hidden" name="coaching_id" value="<?php echo $coaching['coaching_id']; ?>">
                                
                                <div class="mb-3">
                                    <label for="name" class="form-label">Your Name</label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email Address</label>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control" id="phone" name="phone" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="course" class="form-label">Course Interested In</label>
                                    <select class="form-select" id="course" name="course">
                                        <option value="">Select Course</option>
                                        <?php foreach ($courses as $course): ?>
                                            <option value="<?php echo $course['course_id']; ?>"><?php echo $course['course_name']; ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <?php if (!empty($coaching['locations'])): ?>
                                <div class="mb-3">
                                    <label for="location" class="form-label">Preferred Location</label>
                                    <select class="form-select" id="location" name="location_id">
                                        <option value="">Select Location</option>
                                        <?php foreach ($coaching['locations'] as $loc): ?>
                                            <option value="<?php echo $loc['location_id']; ?>" <?php echo ($locationFilter == $loc['location_id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($loc['location_name']); ?> (<?php echo htmlspecialchars($loc['city_name']); ?>)
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <?php endif; ?>
                                
                                <div class="mb-3">
                                    <label for="message" class="form-label">Message</label>
                                    <textarea class="form-control" id="message" name="message" rows="4"></textarea>
                                </div>
                                
                                <button type="submit" class="btn btn-primary w-100">Submit Enquiry</button>
                            </form>
                        </div>
                        
                        <!-- Map -->
                        <div class="coaching-sidebar-card">
                            <h3>
                                Location Map
                                <?php if ($locationFilter && !empty($coaching['locations'])): ?>
                                    <?php 
                                        $locationName = '';
                                        foreach ($coaching['locations'] as $loc) {
                                            if ($loc['location_id'] == $locationFilter) {
                                                $locationName = $loc['location_name'];
                                                break;
                                            }
                                        }
                                    ?>
                                    <small>(<?php echo htmlspecialchars($locationName); ?>)</small>
                                <?php endif; ?>
                            </h3>
                            <div class="coaching-map">
                                <?php
                                // For a real implementation, you would store latitude and longitude for each location
                                // and generate a proper map URL. This is a placeholder.
                                $mapQuery = urlencode($coaching['coaching_name'] . ' ' . ($locationFilter ? $locationName : $coaching['address']) . ' ' . $coaching['city_name']);
                                ?>
                                <!-- Replace YOUR_API_KEY with an actual Google Maps API key in production -->
                                <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3505.0757837605!2d77.2272289!3d28.6129!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMjjCsDM2JzQ2LjQiTiA3N8KwMTMnMzguMCJF!5e0!3m2!1sen!2sin!4v1623825278428!5m2!1sen!2sin" 
                                        width="100%" height="300" style="border:0;" allowfullscreen="" loading="lazy"></iframe>
                                <!-- In a production environment, use: -->
                                <!-- <iframe src="https://www.google.com/maps/embed/v1/place?key=YOUR_API_KEY&q=<?php echo $mapQuery; ?>" width="100%" height="300" style="border:0;" allowfullscreen="" loading="lazy"></iframe> -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Similar Coaching Centers -->
        <section class="similar-coaching-section">
            <div class="container">
                <h2>Similar Coaching Centers</h2>
                
                <div class="row">
                    <?php 
                    // Get similar coaching centers
                    $similarCoachings = $coachingObj->getSimilar($coaching['coaching_id'], 3);
                    
                    // If no similar coaching centers found, use dummy data
                    if (empty($similarCoachings)) {
                        $similarCoachings = array_slice(getDummyCoachingCenters(3), 0, 3);
                    }
                    
                    foreach ($similarCoachings as $similarCoaching): 
                    ?>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="coaching-card">
                                <div class="coaching-image">
                                    <img src="<?php echo isset($similarCoaching['logo']) ? getUploadUrl($similarCoaching['logo']) : getAssetUrl('images/default-logo.png'); ?>" alt="<?php echo $similarCoaching['coaching_name']; ?>">
                                    <?php if (isset($similarCoaching['is_featured']) && $similarCoaching['is_featured']): ?>
                                        <span class="featured-badge"><i class="fas fa-star"></i> Featured</span>
                                    <?php endif; ?>
                                </div>
                                <div class="coaching-content">
                                    <h3><a href="<?php echo getCoachingUrl($similarCoaching['slug']); ?>"><?php echo $similarCoaching['coaching_name']; ?></a></h3>
                                    <div class="coaching-meta">
                                        <span><i class="fas fa-map-marker-alt"></i> 
                                            <?php echo isset($similarCoaching['city_name']) ? $similarCoaching['city_name'] : 'N/A'; ?>
                                            <?php echo isset($similarCoaching['state_name']) ? ', ' . $similarCoaching['state_name'] : ''; ?>
                                        </span>
                                        <span><i class="fas fa-star"></i> 
                                            <?php echo isset($similarCoaching['avg_rating']) ? number_format($similarCoaching['avg_rating'], 1) : '0.0'; ?> 
                                            (<?php echo isset($similarCoaching['total_reviews']) ? $similarCoaching['total_reviews'] : '0'; ?> reviews)
                                        </span>
                                    </div>
                                    <div class="coaching-categories">
                                        <?php if (isset($similarCoaching['categories']) && is_array($similarCoaching['categories'])): ?>
                                            <?php foreach ($similarCoaching['categories'] as $category): ?>
                                                <a href="<?php echo getCategoryUrl($category['category_slug']); ?>" class="category-badge"><?php echo $category['category_name']; ?></a>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <span class="category-badge">General</span>
                                        <?php endif; ?>
                                    </div>
                                    <p class="coaching-description">
                                        <?php 
                                        if (isset($similarCoaching['description']) && !empty($similarCoaching['description'])) {
                                            echo substr($similarCoaching['description'], 0, 100) . '...';
                                        } else {
                                            echo 'No description available.';
                                        }
                                        ?>
                                    </p>
                                    <a href="<?php echo getCoachingUrl($similarCoaching['slug']); ?>" class="btn btn-outline-primary btn-sm mt-3">View Details</a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
    </main>
    
    <!-- Footer -->
    <?php include 'templates/footer.php'; ?>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="<?php echo getAssetUrl('js/main.js'); ?>"></script>
    
    <!-- Page-specific JS -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Handle Enquire Now button clicks
            const enquireButtons = document.querySelectorAll('.course-footer .btn-primary');
            const courseSelect = document.getElementById('course');
            const locationSelect = document.getElementById('location');
            
            enquireButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    const courseId = this.getAttribute('data-course-id');
                    if (courseId && courseSelect) {
                        courseSelect.value = courseId;
                    }
                    
                    // Scroll to the form
                    document.getElementById('enquiry-form').scrollIntoView({
                        behavior: 'smooth'
                    });
                });
            });
            
            // Handle location and course interdependency
            if (courseSelect && locationSelect) {
                // Store the original course options
                const originalCourseOptions = Array.from(courseSelect.options).map(option => {
                    return {
                        value: option.value,
                        text: option.text
                    };
                });
                
                // Store course-location mappings
                const courseLocations = <?php 
                    $mappings = [];
                    foreach ($courses as $course) {
                        $courseLocations = $coachingObj->getCourseLocations($course['course_id']);
                        $locationIds = array_column($courseLocations, 'location_id');
                        $mappings[$course['course_id']] = $locationIds;
                    }
                    echo json_encode($mappings);
                ?>;
                
                // When location changes, filter courses
                locationSelect.addEventListener('change', function() {
                    const locationId = this.value;
                    
                    // Reset course dropdown
                    courseSelect.innerHTML = '<option value="">Select Course</option>';
                    
                    if (!locationId) {
                        // If no location selected, show all courses
                        originalCourseOptions.forEach(option => {
                            if (option.value) { // Skip the empty option
                                const newOption = document.createElement('option');
                                newOption.value = option.value;
                                newOption.text = option.text;
                                courseSelect.add(newOption);
                            }
                        });
                    } else {
                        // Filter courses by location
                        originalCourseOptions.forEach(option => {
                            if (option.value && courseLocations[option.value] && 
                                courseLocations[option.value].includes(parseInt(locationId))) {
                                const newOption = document.createElement('option');
                                newOption.value = option.value;
                                newOption.text = option.text;
                                courseSelect.add(newOption);
                            }
                        });
                    }
                });
                
                // When course changes, filter locations
                courseSelect.addEventListener('change', function() {
                    const courseId = this.value;
                    
                    if (!courseId) {
                        // If no course selected, enable all locations
                        Array.from(locationSelect.options).forEach(option => {
                            option.disabled = false;
                        });
                    } else {
                        // Disable locations not available for this course
                        Array.from(locationSelect.options).forEach(option => {
                            if (option.value) { // Skip the empty option
                                option.disabled = !(courseLocations[courseId] && 
                                    courseLocations[courseId].includes(parseInt(option.value)));
                            }
                        });
                        
                        // If the currently selected location is not available for this course, reset it
                        if (locationSelect.value && courseLocations[courseId] && 
                            !courseLocations[courseId].includes(parseInt(locationSelect.value))) {
                            locationSelect.value = '';
                        }
                    }
                });
            }
        });
    </script>
</body>
</html>