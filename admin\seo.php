<?php
/**
 * SEO Settings
 */
require_once '../includes/autoload.php';
// Debug output removed

// Check if logged in (bypassed for debug)
// if (!isset($_SESSION['admin_id'])) {
//     redirect('index.php');
// }

// Get settings
$settings = Settings::getInstance();

// Get admin info
$admin = new Admin();
$adminInfo = $admin->getById($_SESSION['admin_id']);

// Check permission (bypassed)
// if (!$admin->hasPermission('manage_seo')) {
//     setFlashMessage('error', 'You do not have permission to access this page.');
//     redirect('dashboard.php');
// }

// Process form submission
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Update SEO settings
    $seoSettings = [
        'site_title' => $_POST['site_title'],
        'site_description' => $_POST['site_description'],
        'meta_keywords' => $_POST['meta_keywords'],
        'google_analytics_id' => $_POST['google_analytics_id'],
        'facebook_pixel_id' => $_POST['facebook_pixel_id'],
        'google_verification' => $_POST['google_verification'],
        'bing_verification' => $_POST['bing_verification'],
        'robots_txt' => $_POST['robots_txt'],
        'sitemap_frequency' => $_POST['sitemap_frequency']
    ];
    
    // Update settings
    $updated = $settings->updateSettings($seoSettings);
    
    if ($updated) {
        $message = 'SEO settings updated successfully.';
        $messageType = 'success';
        
        // Log activity
        $admin->logActivity('update_seo', 'Updated SEO settings', $_SESSION['admin_id']);
        
        // Generate sitemap if requested
        if (isset($_POST['generate_sitemap']) && $_POST['generate_sitemap'] === '1') {
            $sitemapGenerator = new SitemapGenerator();
            $generated = $sitemapGenerator->generate();
            
            if ($generated) {
                $message .= ' Sitemap generated successfully.';
            } else {
                $message .= ' Failed to generate sitemap.';
                $messageType = 'warning';
            }
        }
    } else {
        $message = 'Failed to update SEO settings.';
        $messageType = 'error';
    }
}

// Get current SEO settings
$siteTitle = $settings->get('site_title');
$siteDescription = $settings->get('site_description');
$metaKeywords = $settings->get('meta_keywords');
$googleAnalyticsId = $settings->get('google_analytics_id');
$facebookPixelId = $settings->get('facebook_pixel_id');
$googleVerification = $settings->get('google_verification');
$bingVerification = $settings->get('bing_verification');
$robotsTxt = $settings->get('robots_txt');
$sitemapFrequency = $settings->get('sitemap_frequency', 'weekly');

// Get dashboard stats for header
$coachingObj = new CoachingCenter();
$pendingCoachings = $coachingObj->getTotalCount('pending');

$reviewObj = new Review();
$pendingReviews = $reviewObj->getTotalCount('pending');

$enquiryObj = new Enquiry();
$newEnquiries = $enquiryObj->getNewEnquiriesCount(7); // New enquiries in last 7 days

// Page title
$pageTitle = 'SEO Settings';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Admin Panel | <?php echo $settings->getSiteName(); ?></title>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo getAssetUrl($settings->getSiteFavicon()); ?>" type="image/x-icon">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Admin CSS -->
    <link rel="stylesheet" href="assets/css/admin.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <?php include 'templates/sidebar.php'; ?>
        
        <!-- Main Content -->
        <div class="admin-main" id="adminMain">
            <!-- Header -->
            <?php include 'templates/header.php'; ?>
            
            <!-- Content -->
            <div class="admin-content">
                <div class="page-title">
                    <div>
                        <h1><?php echo $pageTitle; ?></h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="dashboard.php">Home</a></li>
                                <li class="breadcrumb-item active" aria-current="page"><?php echo $pageTitle; ?></li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#seoTipsModal">
                            <i class="fas fa-lightbulb me-2"></i> SEO Tips
                        </button>
                    </div>
                </div>
                
                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $messageType === 'error' ? 'danger' : $messageType; ?> alert-dismissible fade show" role="alert">
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <div class="row">
                    <div class="col-lg-8">
                        <!-- SEO Settings Form -->
                        <div class="admin-card">
                            <div class="admin-card-header">
                                <h2 class="admin-card-title">
                                    <i class="fas fa-search me-2"></i> SEO Settings
                                </h2>
                            </div>
                            <div class="admin-card-body">
                                <form action="seo.php" method="post" class="admin-form">
                                    <div class="mb-4">
                                        <label for="site_title" class="form-label">Site Title</label>
                                        <input type="text" class="form-control" id="site_title" name="site_title" value="<?php echo htmlspecialchars($siteTitle); ?>" required>
                                        <div class="form-text">This will be used as the default title for your website.</div>
                                    </div>
                                    
                                    <div class="mb-4">
                                        <label for="site_description" class="form-label">Site Description</label>
                                        <textarea class="form-control" id="site_description" name="site_description" rows="3" required><?php echo htmlspecialchars($siteDescription); ?></textarea>
                                        <div class="form-text">This will be used as the default meta description for your website. Keep it under 160 characters.</div>
                                        <div class="mt-2">
                                            <span id="descriptionCounter" class="badge bg-secondary">0/160</span>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-4">
                                        <label for="meta_keywords" class="form-label">Meta Keywords</label>
                                        <textarea class="form-control" id="meta_keywords" name="meta_keywords" rows="2"><?php echo htmlspecialchars($metaKeywords); ?></textarea>
                                        <div class="form-text">Comma-separated list of keywords. Note: Most search engines don't use this anymore, but it's still good to have.</div>
                                    </div>
                                    
                                    <div class="mb-4">
                                        <label for="google_analytics_id" class="form-label">Google Analytics ID</label>
                                        <input type="text" class="form-control" id="google_analytics_id" name="google_analytics_id" value="<?php echo htmlspecialchars($googleAnalyticsId); ?>" placeholder="UA-XXXXXXXXX-X or G-XXXXXXXXXX">
                                        <div class="form-text">Your Google Analytics tracking ID. Leave empty to disable Google Analytics.</div>
                                    </div>
                                    
                                    <div class="mb-4">
                                        <label for="facebook_pixel_id" class="form-label">Facebook Pixel ID</label>
                                        <input type="text" class="form-control" id="facebook_pixel_id" name="facebook_pixel_id" value="<?php echo htmlspecialchars($facebookPixelId); ?>" placeholder="XXXXXXXXXXXXXXXXXX">
                                        <div class="form-text">Your Facebook Pixel ID. Leave empty to disable Facebook Pixel.</div>
                                    </div>
                                    
                                    <div class="mb-4">
                                        <label for="google_verification" class="form-label">Google Site Verification</label>
                                        <input type="text" class="form-control" id="google_verification" name="google_verification" value="<?php echo htmlspecialchars($googleVerification); ?>" placeholder="Google verification code">
                                        <div class="form-text">Google Search Console verification code. Leave empty if not needed.</div>
                                    </div>
                                    
                                    <div class="mb-4">
                                        <label for="bing_verification" class="form-label">Bing Site Verification</label>
                                        <input type="text" class="form-control" id="bing_verification" name="bing_verification" value="<?php echo htmlspecialchars($bingVerification); ?>" placeholder="Bing verification code">
                                        <div class="form-text">Bing Webmaster Tools verification code. Leave empty if not needed.</div>
                                    </div>
                                    
                                    <div class="mb-4">
                                        <label for="robots_txt" class="form-label">Robots.txt Content</label>
                                        <textarea class="form-control font-monospace" id="robots_txt" name="robots_txt" rows="6"><?php echo htmlspecialchars($robotsTxt); ?></textarea>
                                        <div class="form-text">Content for your robots.txt file. This tells search engines which pages to crawl and which to ignore.</div>
                                    </div>
                                    
                                    <div class="mb-4">
                                        <label for="sitemap_frequency" class="form-label">Sitemap Update Frequency</label>
                                        <select class="form-select" id="sitemap_frequency" name="sitemap_frequency">
                                            <option value="daily" <?php echo $sitemapFrequency === 'daily' ? 'selected' : ''; ?>>Daily</option>
                                            <option value="weekly" <?php echo $sitemapFrequency === 'weekly' ? 'selected' : ''; ?>>Weekly</option>
                                            <option value="monthly" <?php echo $sitemapFrequency === 'monthly' ? 'selected' : ''; ?>>Monthly</option>
                                        </select>
                                        <div class="form-text">How often your sitemap should indicate that your content changes.</div>
                                    </div>
                                    
                                    <div class="mb-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="generate_sitemap" name="generate_sitemap" value="1">
                                            <label class="form-check-label" for="generate_sitemap">
                                                Generate sitemap.xml after saving
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="text-end">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i> Save SEO Settings
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4">
                        <!-- SEO Preview -->
                        <div class="admin-card">
                            <div class="admin-card-header">
                                <h2 class="admin-card-title">
                                    <i class="fas fa-eye me-2"></i> SEO Preview
                                </h2>
                            </div>
                            <div class="admin-card-body">
                                <div class="seo-preview">
                                    <div class="seo-preview-title" id="previewTitle"><?php echo htmlspecialchars($siteTitle); ?></div>
                                    <div class="seo-preview-url"><?php echo getBaseUrl(); ?></div>
                                    <div class="seo-preview-description" id="previewDescription"><?php echo htmlspecialchars($siteDescription); ?></div>
                                </div>
                                
                                <div class="mt-4">
                                    <h5>SEO Checklist</h5>
                                    <ul class="list-group">
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            Title Length
                                            <span class="badge bg-success" id="titleCheck">Good</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            Description Length
                                            <span class="badge bg-success" id="descriptionCheck">Good</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            Keywords
                                            <span class="badge bg-success" id="keywordsCheck">Good</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            Google Analytics
                                            <span class="badge <?php echo !empty($googleAnalyticsId) ? 'bg-success' : 'bg-warning'; ?>" id="analyticsCheck"><?php echo !empty($googleAnalyticsId) ? 'Configured' : 'Not Set'; ?></span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            Robots.txt
                                            <span class="badge <?php echo !empty($robotsTxt) ? 'bg-success' : 'bg-warning'; ?>" id="robotsCheck"><?php echo !empty($robotsTxt) ? 'Configured' : 'Not Set'; ?></span>
                                        </li>
                                    </ul>
                                </div>
                                
                                <div class="mt-4">
                                    <h5>SEO Tools</h5>
                                    <div class="list-group">
                                        <a href="<?php echo getBaseUrl(); ?>sitemap.xml" target="_blank" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                            View Sitemap
                                            <i class="fas fa-external-link-alt"></i>
                                        </a>
                                        <a href="<?php echo getBaseUrl(); ?>robots.txt" target="_blank" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                            View Robots.txt
                                            <i class="fas fa-external-link-alt"></i>
                                        </a>
                                        <a href="https://search.google.com/search-console" target="_blank" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                            Google Search Console
                                            <i class="fas fa-external-link-alt"></i>
                                        </a>
                                        <a href="https://analytics.google.com/" target="_blank" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                            Google Analytics
                                            <i class="fas fa-external-link-alt"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- SEO Tips Modal -->
    <div class="modal fade" id="seoTipsModal" tabindex="-1" aria-labelledby="seoTipsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="seoTipsModalLabel">SEO Best Practices</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Title Tags</h5>
                            <ul>
                                <li>Keep titles under 60 characters</li>
                                <li>Include primary keyword near the beginning</li>
                                <li>Make each title unique across your site</li>
                                <li>Be descriptive and compelling</li>
                            </ul>
                            
                            <h5>Meta Descriptions</h5>
                            <ul>
                                <li>Keep descriptions under 160 characters</li>
                                <li>Include primary and secondary keywords naturally</li>
                                <li>Write compelling copy that encourages clicks</li>
                                <li>Include a call to action when appropriate</li>
                            </ul>
                            
                            <h5>URL Structure</h5>
                            <ul>
                                <li>Keep URLs short and descriptive</li>
                                <li>Use hyphens to separate words</li>
                                <li>Include relevant keywords</li>
                                <li>Avoid parameters when possible</li>
                            </ul>
                        </div>
                        
                        <div class="col-md-6">
                            <h5>Content Optimization</h5>
                            <ul>
                                <li>Create high-quality, original content</li>
                                <li>Use headers (H1, H2, H3) to structure content</li>
                                <li>Include keywords naturally throughout the text</li>
                                <li>Optimize images with alt text</li>
                                <li>Link to relevant internal and external pages</li>
                            </ul>
                            
                            <h5>Technical SEO</h5>
                            <ul>
                                <li>Ensure your site is mobile-friendly</li>
                                <li>Improve page load speed</li>
                                <li>Use HTTPS for secure connections</li>
                                <li>Implement structured data/schema markup</li>
                                <li>Create and maintain an XML sitemap</li>
                                <li>Use a robots.txt file to guide search engines</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Admin JS -->
    <script src="assets/js/admin.js"></script>
    
    <script>
        // SEO Preview
        const siteTitle = document.getElementById('site_title');
        const siteDescription = document.getElementById('site_description');
        const metaKeywords = document.getElementById('meta_keywords');
        const previewTitle = document.getElementById('previewTitle');
        const previewDescription = document.getElementById('previewDescription');
        const descriptionCounter = document.getElementById('descriptionCounter');
        const titleCheck = document.getElementById('titleCheck');
        const descriptionCheck = document.getElementById('descriptionCheck');
        const keywordsCheck = document.getElementById('keywordsCheck');
        
        // Update preview and counters
        function updatePreview() {
            // Update preview
            previewTitle.textContent = siteTitle.value;
            previewDescription.textContent = siteDescription.value;
            
            // Update description counter
            const descLength = siteDescription.value.length;
            descriptionCounter.textContent = `${descLength}/160`;
            
            // Update title check
            if (siteTitle.value.length < 10) {
                titleCheck.textContent = 'Too Short';
                titleCheck.className = 'badge bg-danger';
            } else if (siteTitle.value.length > 60) {
                titleCheck.textContent = 'Too Long';
                titleCheck.className = 'badge bg-warning';
            } else {
                titleCheck.textContent = 'Good';
                titleCheck.className = 'badge bg-success';
            }
            
            // Update description check
            if (descLength < 50) {
                descriptionCheck.textContent = 'Too Short';
                descriptionCheck.className = 'badge bg-danger';
            } else if (descLength > 160) {
                descriptionCheck.textContent = 'Too Long';
                descriptionCheck.className = 'badge bg-warning';
            } else {
                descriptionCheck.textContent = 'Good';
                descriptionCheck.className = 'badge bg-success';
            }
            
            // Update keywords check
            if (metaKeywords.value.length === 0) {
                keywordsCheck.textContent = 'Not Set';
                keywordsCheck.className = 'badge bg-warning';
            } else {
                const keywords = metaKeywords.value.split(',').filter(k => k.trim().length > 0);
                if (keywords.length < 3) {
                    keywordsCheck.textContent = 'Too Few';
                    keywordsCheck.className = 'badge bg-warning';
                } else if (keywords.length > 10) {
                    keywordsCheck.textContent = 'Too Many';
                    keywordsCheck.className = 'badge bg-warning';
                } else {
                    keywordsCheck.textContent = 'Good';
                    keywordsCheck.className = 'badge bg-success';
                }
            }
        }
        
        // Initial update
        updatePreview();
        
        // Add event listeners
        siteTitle.addEventListener('input', updatePreview);
        siteDescription.addEventListener('input', updatePreview);
        metaKeywords.addEventListener('input', updatePreview);
        
        // Default robots.txt if empty
        const robotsTxt = document.getElementById('robots_txt');
        if (!robotsTxt.value.trim()) {
            robotsTxt.value = `User-agent: *
Allow: /
Disallow: /admin/
Disallow: /coaching-panel/
Disallow: /includes/

Sitemap: ${window.location.origin}/coaching/sitemap.xml`;
        }
    </script>
</body>
</html>