<?php
// Database connection details
$host = 'localhost';
$dbname = 'coaching_directory';
$username = 'root';
$password = '';

try {
    // Connect to database
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create admins table
    $sql = "CREATE TABLE IF NOT EXISTS `admins` (
        `admin_id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(100) NOT NULL,
        `email` varchar(100) NOT NULL,
        `password` varchar(255) NOT NULL,
        `role` enum('super_admin','admin','editor') NOT NULL DEFAULT 'admin',
        `status` enum('active','inactive') NOT NULL DEFAULT 'active',
        `profile_image` varchar(255) DEFAULT NULL,
        `remember_token` varchar(255) DEFAULT NULL,
        `remember_expires` datetime DEFAULT NULL,
        `reset_token` varchar(255) DEFAULT NULL,
        `reset_expires` datetime DEFAULT NULL,
        `created_at` datetime NOT NULL,
        `updated_at` datetime DEFAULT NULL,
        PRIMARY KEY (`admin_id`),
        UNIQUE KEY `email` (`email`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;";
    
    $pdo->exec($sql);
    echo "Admins table created successfully!<br>";
    
    // Create permissions table
    $sql = "CREATE TABLE IF NOT EXISTS `permissions` (
        `permission_id` int(11) NOT NULL AUTO_INCREMENT,
        `permission_name` varchar(100) NOT NULL,
        `permission_description` varchar(255) NOT NULL,
        PRIMARY KEY (`permission_id`),
        UNIQUE KEY `permission_name` (`permission_name`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;";
    
    $pdo->exec($sql);
    echo "Permissions table created successfully!<br>";
    
    // Create admin_permissions table
    $sql = "CREATE TABLE IF NOT EXISTS `admin_permissions` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `admin_id` int(11) NOT NULL,
        `permission_id` int(11) NOT NULL,
        PRIMARY KEY (`id`),
        KEY `admin_id` (`admin_id`),
        KEY `permission_id` (`permission_id`),
        CONSTRAINT `admin_permissions_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`admin_id`) ON DELETE CASCADE,
        CONSTRAINT `admin_permissions_ibfk_2` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`permission_id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;";
    
    $pdo->exec($sql);
    echo "Admin permissions table created successfully!<br>";
    
    // Insert default permissions
    $permissions = [
        ['manage_admins', 'Can manage admin users'],
        ['manage_coaching_centers', 'Can manage coaching centers'],
        ['manage_users', 'Can manage regular users'],
        ['manage_courses', 'Can manage courses'],
        ['manage_reviews', 'Can manage reviews'],
        ['manage_inquiries', 'Can manage inquiries'],
        ['manage_blog', 'Can manage blog posts'],
        ['manage_settings', 'Can manage site settings'],
        ['manage_seo', 'Can manage SEO settings'],
        ['view_analytics', 'Can view analytics data'],
        ['manage_subscriptions', 'Can manage subscription plans']
    ];
    
    $stmt = $pdo->prepare("INSERT INTO `permissions` (`permission_name`, `permission_description`) VALUES (?, ?)");
    
    foreach ($permissions as $permission) {
        try {
            $stmt->execute($permission);
        } catch (PDOException $e) {
            // Skip if permission already exists
            if ($e->getCode() != 23000) { // 23000 is the SQLSTATE for duplicate entry
                throw $e;
            }
        }
    }
    
    echo "Default permissions inserted successfully!<br>";
    
    // Create default admin user with password 'admin123'
    $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
    $now = date('Y-m-d H:i:s');
    
    try {
        $stmt = $pdo->prepare("INSERT INTO `admins` (`name`, `email`, `password`, `role`, `status`, `created_at`) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute(['Admin User', '<EMAIL>', $hashedPassword, 'super_admin', 'active', $now]);
        echo "Default admin user created successfully!<br>";
        echo "Email: <EMAIL><br>";
        echo "Password: admin123<br>";
    } catch (PDOException $e) {
        // Skip if admin already exists
        if ($e->getCode() == 23000) { // 23000 is the SQLSTATE for duplicate entry
            echo "Default admin user already exists.<br>";
        } else {
            throw $e;
        }
    }
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>