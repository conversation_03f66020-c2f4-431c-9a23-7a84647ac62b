/**
 * Redirect to a URL
 * @param string $url URL to redirect to
 */
function redirect($url) {
    // Make sure nothing has been output yet
    if (!headers_sent()) {
        header("Location: $url");
        exit;
    } else {
        // If headers already sent, use JavaScript
        echo '<script>window.location.href="' . $url . '";</script>';
        echo '<noscript><meta http-equiv="refresh" content="0;url=' . $url . '"></noscript>';
        exit;
    }
}