<?php
/**
 * Coaching Panel Profile
 */
require_once '../includes/autoload.php';

// Check if user is logged in as coaching owner
Auth::requireCoachingOwner();

// Get coaching center data
$coachingId = $_SESSION['coaching_id'];
$coachingObj = new CoachingCenter();
$coaching = $coachingObj->getById($coachingId);

// If coaching center not found, redirect to login
if (!$coaching) {
    redirect('login.php');
}

// Get user data
$userObj = new User();
$user = $userObj->getUserById($_SESSION['user_id']);

// Get categories
$categoryObj = new Category();
$allCategories = $categoryObj->getAll();

// Get states
$locationObj = new Location();
$states = $locationObj->getAllStates();

// Get cities based on state
$cityObj = new City();
$cities = [];
if (!empty($coaching['state_id'])) {
    $locationObj = new Location();
    $cities = $locationObj->getCitiesByState($coaching['state_id']);
}

// Get locations based on city
$locationObj = new Location();
$locations = [];
if (!empty($coaching['city_id'])) {
    $locations = $locationObj->getLocationsByCity($coaching['city_id']);
}

// Get facilities
$facilityObj = new Facility();
$allFacilities = $facilityObj->getAllForDropdown();
$coachingFacilities = $coachingObj->getFacilitiesWithJoinData($coachingId);
$coachingFacilityIds = array_column($coachingFacilities, 'facility_id');

// Process form submission
$errors = [];
$success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $coachingData = [
        'coaching_name' => trim(isset($_POST['coaching_name']) ? $_POST['coaching_name'] : ''),
        'description' => trim(isset($_POST['description']) ? $_POST['description'] : ''),
        'established_year' => isset($_POST['established_year']) ? $_POST['established_year'] : null,
        'website' => trim(isset($_POST['website']) ? $_POST['website'] : ''),
        'email' => trim(isset($_POST['email']) ? $_POST['email'] : ''),
        'phone' => trim(isset($_POST['phone']) ? $_POST['phone'] : ''),
        'whatsapp' => trim(isset($_POST['whatsapp']) ? $_POST['whatsapp'] : ''),
        'address' => trim(isset($_POST['address']) ? $_POST['address'] : ''),
        'state_id' => isset($_POST['state_id']) ? $_POST['state_id'] : null,
        'city_id' => isset($_POST['city_id']) ? $_POST['city_id'] : null,
        'location_id' => isset($_POST['location_id']) ? $_POST['location_id'] : null,
        'pincode' => trim(isset($_POST['pincode']) ? $_POST['pincode'] : ''),
        'working_hours' => trim(isset($_POST['working_hours']) ? $_POST['working_hours'] : ''),
        'meta_title' => trim(isset($_POST['meta_title']) ? $_POST['meta_title'] : ''),
        'meta_description' => trim(isset($_POST['meta_description']) ? $_POST['meta_description'] : ''),
        'meta_keywords' => trim(isset($_POST['meta_keywords']) ? $_POST['meta_keywords'] : '')
    ];
    
    // Validate form data
    if (empty($coachingData['coaching_name'])) {
        $errors['coaching_name'] = 'Coaching center name is required';
    }
    
    if (empty($coachingData['description'])) {
        $errors['description'] = 'Description is required';
    }
    
    if (empty($coachingData['email'])) {
        $errors['email'] = 'Email is required';
    } elseif (!filter_var($coachingData['email'], FILTER_VALIDATE_EMAIL)) {
        $errors['email'] = 'Invalid email format';
    }
    
    if (empty($coachingData['phone'])) {
        $errors['phone'] = 'Phone number is required';
    }
    
    if (empty($coachingData['address'])) {
        $errors['address'] = 'Address is required';
    }
    
    if (empty($coachingData['state_id'])) {
        $errors['state_id'] = 'State is required';
    }
    
    if (empty($coachingData['city_id'])) {
        $errors['city_id'] = 'City is required';
    }
    
    // Handle logo upload
    if (!empty($_FILES['logo']['name'])) {
        $uploadDir = '../uploads/coaching_logos/';
        if (!is_dir($uploadDir)) mkdir($uploadDir, 0755, true);
        $fileName = 'logo_' . $coachingId . '_' . time() . '_' . basename($_FILES['logo']['name']);
        $targetFile = $uploadDir . $fileName;
        
        error_log("Uploading logo: " . $targetFile);
        
        if (move_uploaded_file($_FILES['logo']['tmp_name'], $targetFile)) {
            // Store the path relative to the base directory
            $coachingData['logo'] = 'uploads/coaching_logos/' . $fileName;
            error_log("Logo uploaded successfully. Path stored in database: " . $coachingData['logo']);
        } else {
            $errors['logo'] = 'Failed to upload logo.';
            error_log("Failed to upload logo. Error: " . error_get_last()['message']);
        }
    }
    // Handle banner upload
    if (!empty($_FILES['banner_image']['name'])) {
        $uploadDir = '../uploads/coaching_banners/';
        if (!is_dir($uploadDir)) mkdir($uploadDir, 0755, true);
        $fileName = 'banner_' . $coachingId . '_' . time() . '_' . basename($_FILES['banner_image']['name']);
        $targetFile = $uploadDir . $fileName;
        
        error_log("Uploading banner: " . $targetFile);
        
        if (move_uploaded_file($_FILES['banner_image']['tmp_name'], $targetFile)) {
            // Store the path relative to the base directory
            $coachingData['banner_image'] = 'uploads/coaching_banners/' . $fileName;
            error_log("Banner uploaded successfully. Path stored in database: " . $coachingData['banner_image']);
        } else {
            $errors['banner_image'] = 'Failed to upload banner image.';
            error_log("Failed to upload banner. Error: " . error_get_last()['message']);
        }
    }
    
    // Handle categories
    $selectedCategories = isset($_POST['categories']) ? $_POST['categories'] : [];
    if (empty($selectedCategories)) {
        $errors['categories'] = 'Please select at least one category';
    }
    
    // Handle facilities
    $selectedFacilities = isset($_POST['facilities']) ? $_POST['facilities'] : [];
    
    // If no errors, update coaching center
    if (empty($errors)) {
        // Log the data being sent for update
        error_log("Updating coaching center with data: " . print_r($coachingData, true));
        error_log("Selected categories: " . print_r($selectedCategories, true));
        error_log("Selected facilities: " . print_r($selectedFacilities, true));
        
        // Make sure we're not passing empty arrays for categories and facilities
        if (empty($selectedCategories)) {
            error_log("Warning: No categories selected");
        }
        
        // Perform the update
        $updateResult = $coachingObj->update($coachingId, $coachingData);
        
        if ($updateResult) {
            // Update categories
            $categoryResult = $coachingObj->updateCategories($coachingId, $selectedCategories);
            error_log("Category update result: " . ($categoryResult ? "Success" : "Failed"));
            
            // Update facilities
            $facilityResult = $coachingObj->updateFacilities($coachingId, $selectedFacilities);
            error_log("Facility update result: " . ($facilityResult ? "Success" : "Failed"));
            
            $success = true;
            
            // Refresh coaching data
            $coaching = $coachingObj->getById($coachingId);
            error_log("Profile updated successfully");
        } else {
            error_log("Failed to update coaching center");
            $errors['general'] = 'Failed to update coaching center';
        }
    }
}

// Get coaching categories
$coachingCategories = $coachingObj->getCategories($coachingId);
$selectedCategoryIds = array_column($coachingCategories, 'category_id');

// Page title
$pageTitle = 'Profile';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Coaching Panel</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo getAssetUrl('css/admin.css'); ?>">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <?php include 'templates/sidebar.php'; ?>
        
        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <?php include 'templates/header.php'; ?>
            
            <!-- Profile Content -->
            <div class="container-fluid">
                <div class="row mb-4">
                    <div class="col-12">
                        <h1 class="page-title">Coaching Center Profile</h1>
                        <p class="text-muted">Manage your coaching center information</p>
                    </div>
                </div>
                
                <?php if ($success): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <strong>Success!</strong> Your profile has been updated successfully.
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($errors['general'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <strong>Error!</strong> <?php echo $errors['general']; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <form method="POST" action="" enctype="multipart/form-data">
                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-lg-8">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Basic Information</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="coaching_name" class="form-label">Coaching Center Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control <?php echo isset($errors['coaching_name']) ? 'is-invalid' : ''; ?>" 
                                               id="coaching_name" name="coaching_name" 
                                               value="<?php echo htmlspecialchars(isset($coaching['coaching_name']) ? $coaching['coaching_name'] : ''); ?>">
                                        <?php if (isset($errors['coaching_name'])): ?>
                                            <div class="invalid-feedback">
                                                <?php echo $errors['coaching_name']; ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                                        <textarea class="form-control <?php echo isset($errors['description']) ? 'is-invalid' : ''; ?>" 
                                                  id="description" name="description" rows="5"><?php echo htmlspecialchars(isset($coaching['description']) ? $coaching['description'] : ''); ?></textarea>
                                        <?php if (isset($errors['description'])): ?>
                                            <div class="invalid-feedback">
                                                <?php echo $errors['description']; ?>
                                            </div>
                                        <?php endif; ?>
                                        <small class="text-muted">Provide a detailed description of your coaching center, including your teaching methodology, faculty, and achievements.</small>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="established_year" class="form-label">Established Year</label>
                                            <select class="form-select" id="established_year" name="established_year">
                                                <option value="">Select Year</option>
                                                <?php for ($year = date('Y'); $year >= 1950; $year--): ?>
                                                    <option value="<?php echo $year; ?>" <?php echo ($coaching['established_year'] == $year) ? 'selected' : ''; ?>>
                                                        <?php echo $year; ?>
                                                    </option>
                                                <?php endfor; ?>
                                            </select>
                                        </div>
                                        
                                        <div class="col-md-6 mb-3">
                                            <label for="website" class="form-label">Website</label>
                                            <input type="url" class="form-control" id="website" name="website" 
                                                   value="<?php echo htmlspecialchars(isset($coaching['website']) ? $coaching['website'] : ''); ?>" 
                                                   placeholder="https://example.com">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Contact Information -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Contact Information</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                                            <input type="email" class="form-control <?php echo isset($errors['email']) ? 'is-invalid' : ''; ?>" 
                                                   id="email" name="email" 
                                                   value="<?php echo htmlspecialchars(isset($coaching['email']) ? $coaching['email'] : ''); ?>">
                                            <?php if (isset($errors['email'])): ?>
                                                <div class="invalid-feedback">
                                                    <?php echo $errors['email']; ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <div class="col-md-6 mb-3">
                                            <label for="phone" class="form-label">Phone <span class="text-danger">*</span></label>
                                            <input type="tel" class="form-control <?php echo isset($errors['phone']) ? 'is-invalid' : ''; ?>" 
                                                   id="phone" name="phone" 
                                                   value="<?php echo htmlspecialchars(isset($coaching['phone']) ? $coaching['phone'] : ''); ?>">
                                            <?php if (isset($errors['phone'])): ?>
                                                <div class="invalid-feedback">
                                                    <?php echo $errors['phone']; ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="whatsapp" class="form-label">WhatsApp Number</label>
                                        <input type="tel" class="form-control" id="whatsapp" name="whatsapp" 
                                               value="<?php echo htmlspecialchars(isset($coaching['whatsapp']) ? $coaching['whatsapp'] : ''); ?>">
                                        <small class="text-muted">Leave empty if same as phone number</small>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="address" class="form-label">Address <span class="text-danger">*</span></label>
                                        <textarea class="form-control <?php echo isset($errors['address']) ? 'is-invalid' : ''; ?>" 
                                                  id="address" name="address" rows="3"><?php echo htmlspecialchars(isset($coaching['address']) ? $coaching['address'] : ''); ?></textarea>
                                        <?php if (isset($errors['address'])): ?>
                                            <div class="invalid-feedback">
                                                <?php echo $errors['address']; ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label for="state_id" class="form-label">State <span class="text-danger">*</span></label>
                                            <select class="form-select <?php echo isset($errors['state_id']) ? 'is-invalid' : ''; ?>" 
                                                    id="state_id" name="state_id">
                                                <option value="">Select State</option>
                                                <?php foreach ($states as $state): ?>
                                                <?php if (is_array($state) && isset($state['state_id'], $state['state_name'])): ?>
                                                <option value="<?php echo $state['state_id']; ?>" 
                                                <?php echo ($coaching['state_id'] == $state['state_id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($state['state_name']); ?>
                                                </option>
                                                <?php endif; ?>
                                                <?php endforeach; ?>
                                            </select>
                                            <?php if (isset($errors['state_id'])): ?>
                                                <div class="invalid-feedback">
                                                    <?php echo $errors['state_id']; ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <div class="col-md-4 mb-3">
                                            <label for="city_id" class="form-label">City <span class="text-danger">*</span></label>
                                            <select class="form-select <?php echo isset($errors['city_id']) ? 'is-invalid' : ''; ?>" 
                                                    id="city_id" name="city_id">
                                                <option value="">Select City</option>
                                                <?php foreach ($cities as $city): ?>
                                                    <option value="<?php echo $city['city_id']; ?>" 
                                                            <?php echo ($coaching['city_id'] == $city['city_id']) ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($city['city_name']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                            <?php if (isset($errors['city_id'])): ?>
                                                <div class="invalid-feedback">
                                                    <?php echo $errors['city_id']; ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <div class="col-md-4 mb-3">
                                            <label for="location_id" class="form-label">Area/Location</label>
                                            <select class="form-select" id="location_id" name="location_id">
                                                <option value="">Select Area</option>
                                                <?php foreach ($locations as $location): ?>
                                                    <option value="<?php echo $location['location_id']; ?>" 
                                                            <?php echo ($coaching['location_id'] == $location['location_id']) ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($location['location_name']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="pincode" class="form-label">Pincode</label>
                                        <input type="text" class="form-control" id="pincode" name="pincode" 
                                               value="<?php echo htmlspecialchars(isset($coaching['pincode']) ? $coaching['pincode'] : ''); ?>">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="working_hours" class="form-label">Working Hours</label>
                                        <textarea class="form-control" id="working_hours" name="working_hours" 
                                                  rows="3" placeholder="e.g. Monday-Friday: 9 AM - 6 PM, Saturday: 9 AM - 2 PM"><?php echo htmlspecialchars(isset($coaching['working_hours']) ? $coaching['working_hours'] : ''); ?></textarea>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Categories and Facilities -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Categories & Facilities</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="categories" class="form-label">Categories <span class="text-danger">*</span></label>
                                        <select class="form-select select2 <?php echo isset($errors['categories']) ? 'is-invalid' : ''; ?>" 
                                                id="categories" name="categories[]" multiple>
                                            <?php foreach ($allCategories as $category): ?>
                                                <option value="<?php echo $category['category_id']; ?>" 
                                                        <?php echo in_array($category['category_id'], $selectedCategoryIds) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($category['category_name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <?php if (isset($errors['categories'])): ?>
                                            <div class="invalid-feedback">
                                                <?php echo $errors['categories']; ?>
                                            </div>
                                        <?php endif; ?>
                                        <small class="text-muted">Select all categories that apply to your coaching center</small>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="facilities" class="form-label">Facilities</label>
                                        <select class="form-select select2" id="facilities" name="facilities[]" multiple>
                                            <?php foreach ($allFacilities as $facility): ?>
                                            <?php if (is_array($facility) && isset($facility['facility_id'], $facility['facility_name'])): ?>
                                                <option value="<?php echo $facility['facility_id']; ?>" 
                                                    <?php echo in_array($facility['facility_id'], $coachingFacilityIds) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($facility['facility_name']); ?>
                                                </option>
                                            <?php endif; ?>
                                            <?php endforeach; ?>
                                        </select>
                                        <small class="text-muted">Select all facilities that your coaching center offers</small>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- SEO Information -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">SEO Information</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="meta_title" class="form-label">Meta Title</label>
                                        <input type="text" class="form-control" id="meta_title" name="meta_title" 
                                               value="<?php echo htmlspecialchars(isset($coaching['meta_title']) ? $coaching['meta_title'] : ''); ?>">
                                        <small class="text-muted">Leave empty to use coaching center name</small>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="meta_description" class="form-label">Meta Description</label>
                                        <textarea class="form-control" id="meta_description" name="meta_description" 
                                                  rows="3"><?php echo htmlspecialchars(isset($coaching['meta_description']) ? $coaching['meta_description'] : ''); ?></textarea>
                                        <small class="text-muted">Leave empty to use coaching center description</small>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="meta_keywords" class="form-label">Meta Keywords</label>
                                        <input type="text" class="form-control" id="meta_keywords" name="meta_keywords" 
                                               value="<?php echo htmlspecialchars(isset($coaching['meta_keywords']) ? $coaching['meta_keywords'] : ''); ?>" 
                                               placeholder="keyword1, keyword2, keyword3">
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Sidebar -->
                        <div class="col-lg-4">
                            <!-- Logo and Banner -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Logo & Banner</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-4">
                                        <label for="logo" class="form-label">Logo</label>
                                        <div class="mb-3">
                                            <?php if (!empty($coaching['logo'])): ?>
                                                <div class="current-image mb-2">
                                                    <img src="<?php echo getUploadUrl($coaching['logo']); ?>" alt="Logo" class="img-thumbnail" style="max-height: 100px;">
                                                    <p class="small text-muted mt-1">Current logo: <?php echo $coaching['logo']; ?></p>
                                                </div>
                                            <?php else: ?>
                                                <div class="alert alert-info small">
                                                    No logo uploaded yet. Please upload a logo for your coaching center.
                                                </div>
                                            <?php endif; ?>
                                            <input type="file" class="form-control <?php echo isset($errors['logo']) ? 'is-invalid' : ''; ?>" 
                                                   id="logo" name="logo" accept="image/*">
                                            <?php if (isset($errors['logo'])): ?>
                                                <div class="invalid-feedback">
                                                    <?php echo $errors['logo']; ?>
                                                </div>
                                            <?php endif; ?>
                                            <small class="text-muted">Recommended size: 200x200 pixels</small>
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <label for="banner_image" class="form-label">Banner Image</label>
                                        <div class="mb-3">
                                            <?php if (!empty($coaching['banner_image'])): ?>
                                                <div class="current-image mb-2">
                                                    <img src="<?php echo getUploadUrl($coaching['banner_image']); ?>" alt="Banner" class="img-thumbnail" style="max-height: 150px;">
                                                    <p class="small text-muted mt-1">Current banner: <?php echo $coaching['banner_image']; ?></p>
                                                </div>
                                            <?php else: ?>
                                                <div class="alert alert-info small">
                                                    No banner uploaded yet. Please upload a banner image for your coaching center.
                                                </div>
                                            <?php endif; ?>
                                            <input type="file" class="form-control <?php echo isset($errors['banner_image']) ? 'is-invalid' : ''; ?>" 
                                                   id="banner_image" name="banner_image" accept="image/*">
                                            <?php if (isset($errors['banner_image'])): ?>
                                                <div class="invalid-feedback">
                                                    <?php echo $errors['banner_image']; ?>
                                                </div>
                                            <?php endif; ?>
                                            <small class="text-muted">Recommended size: 1200x400 pixels</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Status Information -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Status Information</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">Verification Status</label>
                                        <div>
                                            <?php if ($coaching['is_verified']): ?>
                                                <span class="badge bg-success"><i class="fas fa-check-circle me-1"></i> Verified</span>
                                            <?php else: ?>
                                                <span class="badge bg-warning"><i class="fas fa-clock me-1"></i> Pending Verification</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">Featured Status</label>
                                        <div>
                                            <?php if ($coaching['is_featured']): ?>
                                                <span class="badge bg-primary"><i class="fas fa-star me-1"></i> Featured</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">Not Featured</span>
                                                <div class="mt-2">
                                                    <a href="subscription.php" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-crown me-1"></i> Upgrade to Featured
                                                    </a>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <label class="form-label">Account Status</label>
                                        <div>
                                            <?php if ($coaching['status'] === 'active'): ?>
                                                <span class="badge bg-success">Active</span>
                                            <?php elseif ($coaching['status'] === 'pending'): ?>
                                                <span class="badge bg-warning">Pending Approval</span>
                                            <?php elseif ($coaching['status'] === 'inactive'): ?>
                                                <span class="badge bg-danger">Inactive</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">Rejected</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Save Button -->
                            <div class="card mb-4">
                                <div class="card-body">
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-save me-1"></i> Save Changes
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    
    <!-- Custom JS -->
    <script src="<?php echo getAssetUrl('js/admin.js'); ?>"></script>
    
    <script>
        $(document).ready(function() {
            // Initialize Select2
            $('.select2').select2({
                theme: 'bootstrap-5',
                placeholder: 'Select options',
                allowClear: true
            });
            
            // State-City-Location Dependent Dropdown
            $('#state_id').change(function() {
                var stateId = $(this).val();
                if (stateId) {
                    $.ajax({
                        url: 'ajax/get-cities.php',
                        type: 'POST',
                        data: {state_id: stateId},
                        dataType: 'json',
                        success: function(data) {
                            $('#city_id').empty();
                            $('#city_id').append('<option value="">Select City</option>');
                            $.each(data, function(key, value) {
                                $('#city_id').append('<option value="' + value.city_id + '">' + value.city_name + '</option>');
                            });
                            $('#location_id').empty();
                            $('#location_id').append('<option value="">Select Area</option>');
                        }
                    });
                } else {
                    $('#city_id').empty();
                    $('#city_id').append('<option value="">Select City</option>');
                    $('#location_id').empty();
                    $('#location_id').append('<option value="">Select Area</option>');
                }
            });
            
            $('#city_id').change(function() {
                var cityId = $(this).val();
                if (cityId) {
                    $.ajax({
                        url: 'ajax/get-locations.php',
                        type: 'POST',
                        data: {city_id: cityId},
                        dataType: 'json',
                        success: function(data) {
                            $('#location_id').empty();
                            $('#location_id').append('<option value="">Select Area</option>');
                            $.each(data, function(key, value) {
                                $('#location_id').append('<option value="' + value.location_id + '">' + value.location_name + '</option>');
                            });
                        }
                    });
                } else {
                    $('#location_id').empty();
                    $('#location_id').append('<option value="">Select Area</option>');
                }
            });
        });
    </script>
</body>
</html>