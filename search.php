<?php
/**
 * Search Page
 */
require_once 'includes/autoload.php';

// Get settings
$settings = Settings::getInstance();

// Get search parameters
$keyword = isset($_GET['keyword']) ? trim($_GET['keyword']) : '';
$categoryId = isset($_GET['category_id']) ? (int)$_GET['category_id'] : 0;
$stateId = isset($_GET['state_id']) ? (int)$_GET['state_id'] : 0;
$cityId = isset($_GET['city_id']) ? (int)$_GET['city_id'] : 0;
$locationId = isset($_GET['location_id']) ? (int)$_GET['location_id'] : 0;
$featured = isset($_GET['featured']) ? (int)$_GET['featured'] : 0;
$sort = isset($_GET['sort']) ? $_GET['sort'] : 'featured';
$page = getCurrentPage();

// Get categories
$categoryObj = new Category();
$categories = $categoryObj->getCategoriesWithCount();

// Get locations
$locationObj = new Location();
$states = $locationObj->getStatesWithCount();
$cities = $stateId ? $locationObj->getCitiesByState($stateId) : [];
$locations = $cityId ? $locationObj->getLocationsByCity($cityId) : [];

// Search coaching centers
$coachingObj = new CoachingCenter();
$filters = [
    'keyword' => $keyword,
    'category_id' => $categoryId,
    'state_id' => $stateId,
    'city_id' => $cityId,
    'location_id' => $locationId,
    'sort' => $sort
];

if ($featured) {
    $filters['is_featured'] = 1;
}

$searchResults = $coachingObj->search($filters, $page, 10);
$coachingCenters = $searchResults['coaching_centers'] ?? [];
$totalResults = $searchResults['total'] ?? 0;
$totalPages = $searchResults['total_pages'] ?? 1;

// Build page title
$pageTitle = 'Search Results';

if (!empty($keyword)) {
    $pageTitle = 'Search Results for "' . $keyword . '"';
} elseif ($categoryId) {
    $category = $categoryObj->getById($categoryId);
    if ($category) {
        $pageTitle = $category['category_name'] . ' Coaching Centers';
    }
} elseif ($cityId) {
    $city = $locationObj->getCityById($cityId);
    if ($city) {
        $pageTitle = 'Coaching Centers in ' . $city['city_name'];
    }
} elseif ($stateId) {
    $state = $locationObj->getStateById($stateId);
    if ($state) {
        $pageTitle = 'Coaching Centers in ' . $state['state_name'];
    }
} elseif ($featured) {
    $pageTitle = 'Featured Coaching Centers';
}

// Build meta description
$pageDescription = 'Find the best coaching centers';

if (!empty($keyword)) {
    $pageDescription .= ' for ' . $keyword;
}

if ($cityId) {
    $city = $locationObj->getCityById($cityId);
    if ($city) {
        $pageDescription .= ' in ' . $city['city_name'];
    }
} elseif ($stateId) {
    $state = $locationObj->getStateById($stateId);
    if ($state) {
        $pageDescription .= ' in ' . $state['state_name'];
    }
}

$pageDescription .= '. Compare, review, and choose the best coaching institute for your educational needs.';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?php echo getMetaTags($pageTitle . ' - ' . $settings->getSiteName(), $pageDescription); ?>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo getAssetUrl($settings->getSiteFavicon()); ?>" type="image/x-icon">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo getAssetUrl('css/style.css'); ?>">
</head>
<body>
    <!-- Header -->
    <?php include 'templates/header.php'; ?>
    
    <!-- Breadcrumb -->
    <section class="breadcrumb-section">
        <div class="container">
            <?php
            $breadcrumbItems = [
                ['title' => 'Home', 'url' => getBaseUrl()],
                ['title' => 'Search Results', 'url' => '']
            ];
            echo getBreadcrumb($breadcrumbItems);
            ?>
        </div>
    </section>
    
    <!-- Search Section -->
    <section class="search-section section-padding">
        <div class="container">
            <div class="row">
                <!-- Sidebar Filters -->
                <div class="col-lg-3">
                    <div class="filter-sidebar" id="filterSidebar">
                        <div class="filter-header d-lg-none">
                            <h4>Filters</h4>
                            <button type="button" class="btn-close" id="filterClose"></button>
                        </div>
                        
                        <form action="search.php" method="GET" class="filter-form">
                            <!-- Keyword Search -->
                            <div class="filter-box">
                                <h4>Search</h4>
                                <div class="mb-3">
                                    <input type="text" name="keyword" class="form-control" placeholder="Search coaching centers..." value="<?php echo htmlspecialchars($keyword); ?>">
                                </div>
                            </div>
                            
                            <!-- Categories Filter -->
                            <div class="filter-box">
                                <h4>Categories</h4>
                                <div class="mb-3">
                                    <select name="category_id" class="form-select">
                                        <option value="">All Categories</option>
                                        <?php foreach ($categories as $category): ?>
                                            <option value="<?php echo $category['category_id']; ?>" <?php echo $categoryId == $category['category_id'] ? 'selected' : ''; ?>>
                                                <?php echo $category['category_name']; ?> (<?php echo $category['coaching_count']; ?>)
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            
                            <!-- Location Filter -->
                            <div class="filter-box">
                                <h4>Location</h4>
                                <div class="mb-3">
                                    <select name="state_id" id="state_id" class="form-select mb-2">
                                        <option value="">All States</option>
                                        <?php foreach ($states as $state): ?>
                                            <option value="<?php echo $state['state_id']; ?>" <?php echo $stateId == $state['state_id'] ? 'selected' : ''; ?>>
                                                <?php echo $state['state_name']; ?> (<?php echo $state['coaching_count']; ?>)
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    
                                    <select name="city_id" id="city_id" class="form-select mb-2">
                                        <option value="">All Cities</option>
                                        <?php foreach ($cities as $city): ?>
                                            <option value="<?php echo $city['city_id']; ?>" <?php echo $cityId == $city['city_id'] ? 'selected' : ''; ?>>
                                                <?php echo $city['city_name']; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    
                                    <select name="location_id" id="location_id" class="form-select">
                                        <option value="">All Locations</option>
                                        <?php foreach ($locations as $location): ?>
                                            <option value="<?php echo $location['location_id']; ?>" <?php echo $locationId == $location['location_id'] ? 'selected' : ''; ?>>
                                                <?php echo $location['location_name']; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            
                            <!-- Featured Filter -->
                            <div class="filter-box">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="featured" value="1" id="featuredCheck" <?php echo $featured ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="featuredCheck">
                                        Featured Only
                                    </label>
                                </div>
                            </div>
                            
                            <!-- Apply Filters Button -->
                            <div class="filter-box">
                                <button type="submit" class="btn btn-primary w-100">Apply Filters</button>
                                <a href="search.php" class="btn btn-outline-secondary w-100 mt-2">Clear Filters</a>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Search Results -->
                <div class="col-lg-9">
                    <div class="search-results">
                        <!-- Results Header -->
                        <div class="results-header">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <h2><?php echo $pageTitle; ?></h2>
                                    <p><?php echo $totalResults; ?> coaching centers found</p>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex justify-content-md-end align-items-center">
                                        <button class="btn btn-outline-primary d-lg-none me-2" id="filterToggle">
                                            <i class="fas fa-filter"></i> Filters
                                        </button>
                                        <div class="sort-by">
                                            <select class="form-select" id="sortSelect" onchange="window.location.href=this.value">
                                                <option value="<?php echo getCurrentUrlWithParams(['sort' => 'featured'], ['page']); ?>" <?php echo $sort == 'featured' ? 'selected' : ''; ?>>Featured</option>
                                                <option value="<?php echo getCurrentUrlWithParams(['sort' => 'rating_desc'], ['page']); ?>" <?php echo $sort == 'rating_desc' ? 'selected' : ''; ?>>Highest Rated</option>
                                                <option value="<?php echo getCurrentUrlWithParams(['sort' => 'reviews_desc'], ['page']); ?>" <?php echo $sort == 'reviews_desc' ? 'selected' : ''; ?>>Most Reviewed</option>
                                                <option value="<?php echo getCurrentUrlWithParams(['sort' => 'name_asc'], ['page']); ?>" <?php echo $sort == 'name_asc' ? 'selected' : ''; ?>>Name (A-Z)</option>
                                                <option value="<?php echo getCurrentUrlWithParams(['sort' => 'name_desc'], ['page']); ?>" <?php echo $sort == 'name_desc' ? 'selected' : ''; ?>>Name (Z-A)</option>
                                                <option value="<?php echo getCurrentUrlWithParams(['sort' => 'newest'], ['page']); ?>" <?php echo $sort == 'newest' ? 'selected' : ''; ?>>Newest</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Results List -->
                        <div class="results-list">
                            <?php if (count($coachingCenters) > 0): ?>
                                <?php foreach ($coachingCenters as $coaching): ?>
                                    <div class="coaching-list-item">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="coaching-image">
                                                    <img src="<?php echo isset($coaching['logo']) ? getUploadUrl($coaching['logo']) : getAssetUrl('images/default-logo.png'); ?>" alt="<?php echo $coaching['coaching_name']; ?>">
                                                    <?php if ($coaching['is_featured']): ?>
                                                        <span class="featured-badge"><i class="fas fa-star"></i> Featured</span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            <div class="col-md-9">
                                                <div class="coaching-content">
                                                    <div class="d-flex justify-content-between align-items-start">
                                                        <h3><a href="<?php echo getCoachingUrl($coaching['slug']); ?>"><?php echo $coaching['coaching_name']; ?></a></h3>
                                                        <div class="rating">
                                                            <?php echo getStarRating($coaching['avg_rating']); ?>
                                                            <span class="reviews">(<?php echo $coaching['total_reviews']; ?> reviews)</span>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="location">
                                                        <i class="fas fa-map-marker-alt"></i>
                                                        <?php 
                                                        $locationParts = [];
                                                        if (!empty($coaching['location_name'])) $locationParts[] = $coaching['location_name'];
                                                        if (!empty($coaching['city_name'])) $locationParts[] = $coaching['city_name'];
                                                        if (!empty($coaching['state_name'])) $locationParts[] = $coaching['state_name'];
                                                        echo implode(', ', $locationParts);
                                                        ?>
                                                    </div>
                                                    
                                                    <div class="coaching-categories">
                                                        <?php foreach (($coaching['categories'] ?? []) as $index => $category): ?>
                                                            <?php if ($index < 5): ?>
                                                                <a href="<?php echo getCategoryUrl($category['category_slug']); ?>" class="category-badge"><?php echo $category['category_name']; ?></a>
                                                            <?php endif; ?>
                                                        <?php endforeach; ?>
                                                        <?php if (count($coaching['categories'] ?? []) > 5): ?>
                                                            <span class="category-badge">+<?php echo count($coaching['categories'] ?? []) - 5; ?> more</span>
                                                        <?php endif; ?>
                                                    </div>
                                                    
                                                    <div class="coaching-description">
                                                        <?php echo truncate($coaching['description'], 150); ?>
                                                    </div>
                                                    
                                                    <div class="coaching-actions">
                                                        <a href="<?php echo getCoachingUrl($coaching['slug']); ?>" class="btn btn-primary btn-sm">View Details</a>
                                                        <?php if (isLoggedIn()): ?>
                                                            <button class="btn btn-outline-primary btn-sm favorite-button <?php echo $coachingObj->isInFavorites(getCurrentUser('user_id'), $coaching['coaching_id']) ? 'active' : ''; ?>" data-id="<?php echo $coaching['coaching_id']; ?>">
                                                                <i class="<?php echo $coachingObj->isInFavorites(getCurrentUser('user_id'), $coaching['coaching_id']) ? 'fas' : 'far'; ?> fa-heart"></i>
                                                            </button>
                                                        <?php else: ?>
                                                            <a href="login.php?redirect=<?php echo urlencode(getCurrentUrl()); ?>" class="btn btn-outline-primary btn-sm">
                                                                <i class="far fa-heart"></i>
                                                            </a>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                                
                                <!-- Pagination -->
                                <?php if ($totalPages > 1): ?>
                                    <div class="pagination-container">
                                        <?php echo getPagination($totalResults, $page, 10, getCurrentUrlWithParams([], ['page'])); ?>
                                    </div>
                                <?php endif; ?>
                            <?php else: ?>
                                <div class="no-results">
                                    <div class="text-center">
                                        <i class="fas fa-search fa-3x mb-3"></i>
                                        <h3>No Results Found</h3>
                                        <p>We couldn't find any coaching centers matching your search criteria.</p>
                                        <a href="search.php" class="btn btn-primary">Clear Filters</a>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Footer -->
    <?php include 'templates/footer.php'; ?>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="<?php echo getAssetUrl('js/main.js'); ?>"></script>
</body>
</html>