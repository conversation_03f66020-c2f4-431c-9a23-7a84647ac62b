<?php
/**
 * Blog Post Page
 * Shows a single blog post
 */
require_once 'includes/autoload.php';

// Get settings
$settings = Settings::getInstance();

// Include dummy data for development
require_once 'includes/dummy-data.php';

// Get post slug from URL
$slug = isset($_GET['slug']) ? $_GET['slug'] : '';

if (empty($slug)) {
    redirect('blog.php');
}

// Get post details
$blogObj = new Blog();
$post = $blogObj->getBySlug($slug);

// If post not found, use dummy data
if (empty($post)) {
    $dummyPosts = getDummyBlogPosts(10);
    foreach ($dummyPosts as $dummyPost) {
        if ($dummyPost['slug'] == $slug) {
            $post = $dummyPost;
            break;
        }
    }
    
    // If still not found, redirect to blog page
    if (empty($post)) {
        redirect('blog.php');
    }
}

// Get related posts
$relatedPosts = $blogObj->getRelatedPosts($post['post_id'], $post['category_id'], 3);

// If no related posts found, use dummy data
if (empty($relatedPosts)) {
    $relatedPosts = getDummyBlogPosts(3);
}

// Page title and meta
$pageTitle = $post['title'];
$pageDescription = substr(strip_tags($post['content']), 0, 160);
$pageKeywords = $post['category_name'] . ', blog, ' . $settings->getMetaKeywords();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?php echo getMetaTags($pageTitle . ' - ' . $settings->getSiteName(), $pageDescription, $pageKeywords, !empty($post['featured_image']) ? getUploadUrl($post['featured_image']) : ''); ?>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo getAssetUrl($settings->getSiteFavicon()); ?>" type="image/x-icon">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo getAssetUrl('css/style.css'); ?>">
</head>
<body>
    <!-- Header -->
    <?php include 'templates/header.php'; ?>
    
    <!-- Main Content -->
    <main>
        <!-- Page Header -->
        <section class="page-header">
            <div class="container">
                <h1><?php echo $post['title']; ?></h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="index.php">Home</a></li>
                        <li class="breadcrumb-item"><a href="blog.php">Blog</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo getBlogCategoryUrl($post['category_slug']); ?>"><?php echo $post['category_name']; ?></a></li>
                        <li class="breadcrumb-item active" aria-current="page"><?php echo $post['title']; ?></li>
                    </ol>
                </nav>
            </div>
        </section>
        
        <!-- Blog Post Section -->
        <section class="blog-post-section">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8">
                        <article class="blog-post">
                            <?php if (!empty($post['featured_image'])): ?>
                                <div class="post-image">
                                    <img src="<?php echo getUploadUrl($post['featured_image']); ?>" alt="<?php echo $post['title']; ?>">
                                </div>
                            <?php endif; ?>
                            
                            <div class="post-meta">
                                <span><i class="fas fa-calendar"></i> <?php echo date('F j, Y', strtotime($post['published_at'])); ?></span>
                                <span><i class="fas fa-user"></i> <?php echo $post['author_name']; ?></span>
                                <span><i class="fas fa-folder"></i> <a href="<?php echo getBlogCategoryUrl($post['category_slug']); ?>"><?php echo $post['category_name']; ?></a></span>
                            </div>
                            
                            <div class="post-content">
                                <?php echo $post['content']; ?>
                            </div>
                            
                            <div class="post-tags">
                                <?php if (!empty($post['tags'])): ?>
                                    <h4>Tags:</h4>
                                    <?php foreach (explode(',', $post['tags']) as $tag): ?>
                                        <a href="blog.php?tag=<?php echo trim($tag); ?>" class="tag"><?php echo trim($tag); ?></a>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                            
                            <div class="post-share">
                                <h4>Share:</h4>
                                <div class="social-share">
                                    <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(getCurrentUrl()); ?>" target="_blank" class="facebook"><i class="fab fa-facebook-f"></i></a>
                                    <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode(getCurrentUrl()); ?>&text=<?php echo urlencode($post['title']); ?>" target="_blank" class="twitter"><i class="fab fa-twitter"></i></a>
                                    <a href="https://www.linkedin.com/shareArticle?mini=true&url=<?php echo urlencode(getCurrentUrl()); ?>&title=<?php echo urlencode($post['title']); ?>" target="_blank" class="linkedin"><i class="fab fa-linkedin-in"></i></a>
                                    <a href="https://api.whatsapp.com/send?text=<?php echo urlencode($post['title'] . ' ' . getCurrentUrl()); ?>" target="_blank" class="whatsapp"><i class="fab fa-whatsapp"></i></a>
                                </div>
                            </div>
                        </article>
                        
                        <!-- Related Posts -->
                        <div class="related-posts">
                            <h3>Related Posts</h3>
                            <div class="row">
                                <?php foreach ($relatedPosts as $relatedPost): ?>
                                    <div class="col-md-4">
                                        <div class="blog-card">
                                            <div class="blog-image">
                                                <?php if (!empty($relatedPost['featured_image'])): ?>
                                                    <img src="<?php echo getUploadUrl($relatedPost['featured_image']); ?>" alt="<?php echo $relatedPost['title']; ?>">
                                                <?php else: ?>
                                                    <img src="<?php echo getAssetUrl('images/dummy/blog.jpg'); ?>" alt="<?php echo $relatedPost['title']; ?>">
                                                <?php endif; ?>
                                                <a href="<?php echo getBlogCategoryUrl($relatedPost['category_slug']); ?>" class="category"><?php echo $relatedPost['category_name']; ?></a>
                                            </div>
                                            <div class="blog-content">
                                                <div class="blog-meta">
                                                    <span><i class="fas fa-calendar"></i> <?php echo date('F j, Y', strtotime($relatedPost['published_at'])); ?></span>
                                                </div>
                                                <h3><a href="<?php echo getBlogPostUrl($relatedPost['slug']); ?>"><?php echo $relatedPost['title']; ?></a></h3>
                                                <p><?php echo substr(strip_tags($relatedPost['content']), 0, 100); ?>...</p>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4">
                        <div class="sidebar">
                            <!-- Search Widget -->
                            <div class="widget search-widget">
                                <h3 class="widget-title">Search</h3>
                                <form action="blog.php" method="get">
                                    <div class="input-group">
                                        <input type="text" name="search" class="form-control" placeholder="Search...">
                                        <button type="submit" class="btn btn-primary"><i class="fas fa-search"></i></button>
                                    </div>
                                </form>
                            </div>
                            
                            <!-- Categories Widget -->
                            <div class="widget categories-widget">
                                <h3 class="widget-title">Categories</h3>
                                <ul>
                                    <?php 
                                    $blogCategories = $blogObj->getCategories();
                                    if (empty($blogCategories)) {
                                        $blogCategories = [
                                            ['category_name' => 'Education News', 'category_slug' => 'education-news', 'post_count' => 5],
                                            ['category_name' => 'Exam Tips', 'category_slug' => 'exam-tips', 'post_count' => 3],
                                            ['category_name' => 'Study Guides', 'category_slug' => 'study-guides', 'post_count' => 7],
                                            ['category_name' => 'Career Advice', 'category_slug' => 'career-advice', 'post_count' => 4],
                                            ['category_name' => 'Success Stories', 'category_slug' => 'success-stories', 'post_count' => 2]
                                        ];
                                    }
                                    ?>
                                    <?php foreach ($blogCategories as $category): ?>
                                        <li>
                                            <a href="<?php echo getBlogCategoryUrl($category['category_slug']); ?>">
                                                <?php echo $category['category_name']; ?>
                                                <span>(<?php echo $category['post_count']; ?>)</span>
                                            </a>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                            
                            <!-- Recent Posts Widget -->
                            <div class="widget recent-posts-widget">
                                <h3 class="widget-title">Recent Posts</h3>
                                <ul>
                                    <?php 
                                    $recentPosts = $blogObj->getRecentPosts(5);
                                    if (empty($recentPosts)) {
                                        $recentPosts = getDummyBlogPosts(5);
                                    }
                                    ?>
                                    <?php foreach ($recentPosts as $recentPost): ?>
                                        <li>
                                            <div class="post-image">
                                                <?php if (!empty($recentPost['featured_image'])): ?>
                                                    <img src="<?php echo getUploadUrl($recentPost['featured_image']); ?>" alt="<?php echo $recentPost['title']; ?>">
                                                <?php else: ?>
                                                    <img src="<?php echo getAssetUrl('images/dummy/blog-small.jpg'); ?>" alt="<?php echo $recentPost['title']; ?>">
                                                <?php endif; ?>
                                            </div>
                                            <div class="post-info">
                                                <h4><a href="<?php echo getBlogPostUrl($recentPost['slug']); ?>"><?php echo $recentPost['title']; ?></a></h4>
                                                <span><i class="fas fa-calendar"></i> <?php echo date('F j, Y', strtotime($recentPost['published_at'])); ?></span>
                                            </div>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
    
    <!-- Footer -->
    <?php include 'templates/footer.php'; ?>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="<?php echo getAssetUrl('js/main.js'); ?>"></script>
</body>
</html>