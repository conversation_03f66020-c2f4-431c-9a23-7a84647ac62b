<?php
/**
 * Facility Class
 * Handles operations related to facilities
 */
class Facility {
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Get all facilities
     * @param array $filters Optional filters
     * @param int $page Page number
     * @param int $limit Records per page
     * @return array Facilities and pagination info
     */
    public function getAll($filters = [], $page = 1, $limit = 10) {
        $where = "1=1";
        $params = [];
        
        // Apply filters
        if (isset($filters['status']) && !empty($filters['status'])) {
            $where .= " AND status = ?";
            $params[] = $filters['status'];
        }
        
        if (isset($filters['search']) && !empty($filters['search'])) {
            $where .= " AND (facility_name LIKE ?)";
            $params[] = "%{$filters['search']}%";
        }
        
        // Count total records
        $totalCount = $this->db->count('facilities', $where, $params);
        $totalPages = ceil($totalCount / $limit);
        
        // Ensure page is within valid range
        $page = max(1, min($page, $totalPages));
        $offset = ($page - 1) * $limit;
        
        // Get facilities
        $sql = "SELECT * FROM facilities WHERE {$where} ORDER BY facility_name ASC LIMIT ?, ?";
        $params[] = $offset;
        $params[] = $limit;
        
        $facilities = $this->db->fetchAll($sql, $params);
        
        return [
            'facilities' => $facilities,
            'total_count' => $totalCount,
            'total_pages' => $totalPages,
            'current_page' => $page
        ];
    }
    
    /**
     * Get facility by ID
     * @param int $facilityId Facility ID
     * @return array|null Facility data
     */
    public function getById($facilityId) {
        return $this->db->fetchRow(
            "SELECT * FROM facilities WHERE facility_id = ?",
            [$facilityId]
        );
    }
    
    /**
     * Create new facility
     * @param array $data Facility data
     * @return int|bool New facility ID or false on failure
     */
    public function create($data) {
        return $this->db->insert('facilities', $data);
    }
    
    /**
     * Update facility
     * @param int $facilityId Facility ID
     * @param array $data Facility data
     * @return bool Success or failure
     */
    public function update($facilityId, $data) {
        return $this->db->update('facilities', $data, 'facility_id = ?', [$facilityId]);
    }
    
    /**
     * Delete facility
     * @param int $facilityId Facility ID
     * @return bool Success or failure
     */
    public function delete($facilityId) {
        return $this->db->delete('facilities', 'facility_id = ?', [$facilityId]);
    }
    
    /**
     * Get all facilities for dropdown
     * @param string $status Status filter (optional)
     * @return array Facilities
     */
    public function getAllForDropdown($status = 'active') {
        $sql = "SELECT facility_id, facility_name FROM facilities";
        $params = [];
        
        if (!empty($status)) {
            $sql .= " WHERE status = ?";
            $params[] = $status;
        }
        
        $sql .= " ORDER BY facility_name ASC";
        
        return $this->db->fetchAll($sql, $params);
    }
}