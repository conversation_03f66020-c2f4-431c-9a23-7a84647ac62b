<?php
require_once 'includes/autoload.php';

// Check if user is admin
if (!Auth::isAdmin()) {
    die("Access denied. Admin privileges required.");
}

$db = Database::getInstance();

// Check if address column already exists
$checkColumn = $db->fetchRow("SHOW COLUMNS FROM locations LIKE 'address'");

if (!$checkColumn) {
    // Add address column to locations table
    $result = $db->query("ALTER TABLE locations ADD COLUMN address TEXT AFTER pincode");
    
    if ($result) {
        echo "Address column added to locations table successfully!";
        
        // Update database schema file
        $schemaFile = file_get_contents('database_schema.sql');
        $locationTablePattern = "/CREATE TABLE IF NOT EXISTS locations \(\s+location_id INT AUTO_INCREMENT PRIMARY KEY,\s+location_name VARCHAR\(100\) NOT NULL,\s+city_id INT,\s+pincode VARCHAR\(20\),\s+status ENUM\('active', 'inactive'\) DEFAULT 'active',/";
        $replacement = "CREATE TABLE IF NOT EXISTS locations (\n    location_id INT AUTO_INCREMENT PRIMARY KEY,\n    location_name VARCHAR(100) NOT NULL,\n    city_id INT,\n    pincode VARCHAR(20),\n    address TEXT,\n    status ENUM('active', 'inactive') DEFAULT 'active',";
        
        $updatedSchema = preg_replace($locationTablePattern, $replacement, $schemaFile);
        
        if ($updatedSchema !== $schemaFile) {
            file_put_contents('database_schema.sql', $updatedSchema);
            echo "<br>Database schema file updated successfully!";
        } else {
            echo "<br>Failed to update database schema file. Please update it manually.";
        }
    } else {
        echo "Failed to add address column to locations table.";
    }
} else {
    echo "Address column already exists in locations table.";
}