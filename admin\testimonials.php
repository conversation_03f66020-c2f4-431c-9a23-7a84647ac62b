<?php
/**
 * Admin Testimonials
 */
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    require_once '../includes/autoload.php';
    
    // Check user authentication
    $user = new User();
    if (!$user->isLoggedIn() || !$user->isAdmin()) {
        header('Location: login.php');
        exit;
    }
    
    // Get admin info
    $adminInfo = $user->getUserData();
    
    // Initialize settings
    $settings = Settings::getInstance();
    
    // Initialize testimonial object
    $testimonialObj = new Testimonial();
    
    // Handle actions
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    $testimonialId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    $message = '';
    
    if ($action === 'delete' && $testimonialId > 0) {
        if ($testimonialObj->delete($testimonialId)) {
            $message = '<div class="alert alert-success">Testimonial deleted successfully.</div>';
        } else {
            $message = '<div class="alert alert-danger">Failed to delete testimonial.</div>';
        }
    } else if ($action === 'activate' && $testimonialId > 0) {
        if ($testimonialObj->update($testimonialId, ['status' => 'active'])) {
            $message = '<div class="alert alert-success">Testimonial activated successfully.</div>';
        } else {
            $message = '<div class="alert alert-danger">Failed to activate testimonial.</div>';
        }
    } else if ($action === 'deactivate' && $testimonialId > 0) {
        if ($testimonialObj->update($testimonialId, ['status' => 'inactive'])) {
            $message = '<div class="alert alert-success">Testimonial deactivated successfully.</div>';
        } else {
            $message = '<div class="alert alert-danger">Failed to deactivate testimonial.</div>';
        }
    }
    
    // Handle form submission for adding/editing testimonial
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $name = trim($_POST['name']);
        $position = trim($_POST['position']);
        $content = trim($_POST['content']);
        $status = $_POST['status'];
        $displayOrder = (int)$_POST['display_order'];
        $isFeatured = isset($_POST['is_featured']) ? 1 : 0;
        
        if (empty($name) || empty($content)) {
            $message = '<div class="alert alert-danger">Name and content are required.</div>';
        } else {
            $testimonialData = [
                'name' => $name,
                'position' => $position,
                'content' => $content,
                'status' => $status,
                'display_order' => $displayOrder,
                'is_featured' => $isFeatured
            ];
            
            if (isset($_POST['edit_id']) && $_POST['edit_id'] > 0) {
                // Update existing testimonial
                $editId = (int)$_POST['edit_id'];
                
                // Handle image upload
                if (!empty($_FILES['image']['name'])) {
                    $uploadDir = '../uploads/testimonials/';
                    if (!is_dir($uploadDir)) {
                        mkdir($uploadDir, 0755, true);
                    }
                    
                    $fileName = 'testimonial_' . $editId . '_' . time() . '.' . pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION);
                    $targetFile = $uploadDir . $fileName;
                    
                    if (move_uploaded_file($_FILES['image']['tmp_name'], $targetFile)) {
                        $testimonialData['image'] = 'uploads/testimonials/' . $fileName;
                    }
                }
                
                if ($testimonialObj->update($editId, $testimonialData)) {
                    $message = '<div class="alert alert-success">Testimonial updated successfully.</div>';
                } else {
                    $message = '<div class="alert alert-danger">Failed to update testimonial.</div>';
                }
            } else {
                // Add new testimonial
                $testimonialData['created_at'] = date('Y-m-d H:i:s');
                
                $testimonialId = $testimonialObj->create($testimonialData);
                
                if ($testimonialId) {
                    // Handle image upload
                    if (!empty($_FILES['image']['name'])) {
                        $uploadDir = '../uploads/testimonials/';
                        if (!is_dir($uploadDir)) {
                            mkdir($uploadDir, 0755, true);
                        }
                        
                        $fileName = 'testimonial_' . $testimonialId . '_' . time() . '.' . pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION);
                        $targetFile = $uploadDir . $fileName;
                        
                        if (move_uploaded_file($_FILES['image']['tmp_name'], $targetFile)) {
                            $imagePath = 'uploads/testimonials/' . $fileName;
                            $testimonialObj->update($testimonialId, ['image' => $imagePath]);
                        }
                    }
                    
                    $message = '<div class="alert alert-success">Testimonial added successfully.</div>';
                } else {
                    $message = '<div class="alert alert-danger">Failed to add testimonial.</div>';
                }
            }
        }
    }
    
    // Get testimonial to edit
    $editTestimonial = null;
    if ($action === 'edit' && $testimonialId > 0) {
        $editTestimonial = $testimonialObj->getById($testimonialId);
    }
    
    // Get all testimonials
    $testimonials = $testimonialObj->getAll();
    
} catch (Exception $e) {
    die('<div style="color:red;padding:20px;border:2px solid red;margin:20px;">'
        . '<h2>Error</h2>'
        . '<p><strong>Message:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>'
        . '<p><strong>File:</strong> ' . htmlspecialchars($e->getFile()) . '</p>'
        . '<p><strong>Line:</strong> ' . htmlspecialchars($e->getLine()) . '</p>'
        . '</div>');
}

// Page title
$pageTitle = 'Testimonials';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Admin Panel | <?php echo $settings->getSiteName(); ?></title>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo getAssetUrl($settings->getSiteFavicon()); ?>" type="image/x-icon">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Admin CSS -->
    <link rel="stylesheet" href="assets/css/admin.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <?php include 'templates/sidebar.php'; ?>
        
        <!-- Main Content -->
        <div class="admin-main" id="adminMain">
            <!-- Header -->
            <?php include 'templates/header.php'; ?>
            
            <!-- Content -->
            <div class="admin-content">
                <div class="page-title">
                    <div>
                        <h1><?php echo $pageTitle; ?></h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="dashboard.php">Home</a></li>
                                <li class="breadcrumb-item">Content</li>
                                <li class="breadcrumb-item active" aria-current="page"><?php echo $pageTitle; ?></li>
                            </ol>
                        </nav>
                    </div>
                </div>
                
                <?php echo $message; ?>
                
                <div class="row">
                    <!-- Add/Edit Testimonial Form -->
                    <div class="col-md-4">
                        <div class="admin-card">
                            <div class="admin-card-header">
                                <h2 class="admin-card-title">
                                    <i class="fas fa-quote-right me-2"></i> <?php echo $editTestimonial ? 'Edit Testimonial' : 'Add New Testimonial'; ?>
                                </h2>
                            </div>
                            <div class="admin-card-body">
                                <form action="" method="post" enctype="multipart/form-data">
                                    <?php if ($editTestimonial): ?>
                                        <input type="hidden" name="edit_id" value="<?php echo $editTestimonial['testimonial_id']; ?>">
                                    <?php endif; ?>
                                    
                                    <div class="mb-3">
                                        <label for="name" class="form-label">Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="name" name="name" value="<?php echo $editTestimonial ? htmlspecialchars($editTestimonial['name']) : ''; ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="position" class="form-label">Position/Company</label>
                                        <input type="text" class="form-control" id="position" name="position" value="<?php echo $editTestimonial ? htmlspecialchars($editTestimonial['position']) : ''; ?>">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="content" class="form-label">Content <span class="text-danger">*</span></label>
                                        <textarea class="form-control" id="content" name="content" rows="5" required><?php echo $editTestimonial ? htmlspecialchars($editTestimonial['content']) : ''; ?></textarea>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="image" class="form-label">Image</label>
                                        <?php if ($editTestimonial && !empty($editTestimonial['image'])): ?>
                                            <div class="mb-2">
                                                <img src="<?php echo getAssetUrl($editTestimonial['image']); ?>" alt="<?php echo htmlspecialchars($editTestimonial['name']); ?>" class="img-thumbnail" style="max-width: 100px;">
                                            </div>
                                        <?php endif; ?>
                                        <input type="file" class="form-control" id="image" name="image" accept="image/*">
                                        <small class="text-muted">Recommended size: 200x200 pixels</small>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="status" class="form-label">Status</label>
                                        <select class="form-select" id="status" name="status">
                                            <option value="active" <?php echo $editTestimonial && $editTestimonial['status'] === 'active' ? 'selected' : ''; ?>>Active</option>
                                            <option value="inactive" <?php echo $editTestimonial && $editTestimonial['status'] === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="display_order" class="form-label">Display Order</label>
                                        <input type="number" class="form-control" id="display_order" name="display_order" value="<?php echo $editTestimonial ? (int)$editTestimonial['display_order'] : 0; ?>" min="0">
                                    </div>
                                    
                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="is_featured" name="is_featured" <?php echo $editTestimonial && $editTestimonial['is_featured'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="is_featured">Featured Testimonial</label>
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <?php if ($editTestimonial): ?>
                                            <button type="submit" class="btn btn-primary">Update Testimonial</button>
                                            <a href="testimonials.php" class="btn btn-secondary">Cancel</a>
                                        <?php else: ?>
                                            <button type="submit" class="btn btn-primary">Add Testimonial</button>
                                        <?php endif; ?>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Testimonials List -->
                    <div class="col-md-8">
                        <div class="admin-card">
                            <div class="admin-card-header">
                                <h2 class="admin-card-title">
                                    <i class="fas fa-list me-2"></i> All Testimonials
                                </h2>
                                <span class="badge bg-primary"><?php echo count($testimonials); ?> Total</span>
                            </div>
                            <div class="admin-card-body p-0">
                                <div class="table-responsive">
                                    <table class="admin-table">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Name</th>
                                                <th>Position</th>
                                                <th>Content</th>
                                                <th>Status</th>
                                                <th>Featured</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (!empty($testimonials)): ?>
                                                <?php foreach ($testimonials as $testimonial): ?>
                                                <?php if (is_array($testimonial) && isset($testimonial['testimonial_id'])): ?>
                                                <tr>
                                                <td><?php echo $testimonial['testimonial_id']; ?></td>
                                                <td>
                                                <div class="d-flex align-items-center">
                                                <?php if (!empty($testimonial['image'])): ?>
                                                <img src="<?php echo getAssetUrl($testimonial['image']); ?>" alt="<?php echo htmlspecialchars($testimonial['name']); ?>" class="me-2" style="width: 40px; height: 40px; object-fit: cover; border-radius: 50%;">
                                                <?php else: ?>
                                                <div class="placeholder-image me-2" style="width: 40px; height: 40px; background-color: #e9ecef; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                                <i class="fas fa-user text-secondary"></i>
                                                </div>
                                                <?php endif; ?>
                                                <div>
                                                <?php echo htmlspecialchars($testimonial['name']); ?>
                                                </div>
                                                </div>
                                                </td>
                                                <td><?php echo htmlspecialchars($testimonial['position']); ?></td>
                                                <td><?php echo substr(htmlspecialchars($testimonial['content']), 0, 50) . '...'; ?></td>
                                                <td>
                                                <?php if ($testimonial['status'] === 'active'): ?>
                                                <span class="badge bg-success">Active</span>
                                                <?php else: ?>
                                                <span class="badge bg-danger">Inactive</span>
                                                <?php endif; ?>
                                                </td>
                                                <td>
                                                <?php if ($testimonial['is_featured']): ?>
                                                <span class="badge bg-primary">Featured</span>
                                                <?php else: ?>
                                                <span class="badge bg-secondary">No</span>
                                                <?php endif; ?>
                                                </td>
                                                <td>
                                                <div class="btn-group">
                                                <a href="testimonials.php?action=edit&id=<?php echo $testimonial['testimonial_id']; ?>" class="btn btn-sm btn-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                                </a>
                                                <?php if ($testimonial['status'] === 'active'): ?>
                                                <a href="testimonials.php?action=deactivate&id=<?php echo $testimonial['testimonial_id']; ?>" class="btn btn-sm btn-warning" title="Deactivate">
                                                <i class="fas fa-ban"></i>
                                                </a>
                                                <?php else: ?>
                                                <a href="testimonials.php?action=activate&id=<?php echo $testimonial['testimonial_id']; ?>" class="btn btn-sm btn-success" title="Activate">
                                                <i class="fas fa-check"></i>
                                                </a>
                                                <?php endif; ?>
                                                <a href="testimonials.php?action=delete&id=<?php echo $testimonial['testimonial_id']; ?>" class="btn btn-sm btn-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this testimonial?');">
                                                <i class="fas fa-trash"></i>
                                                </a>
                                                </div>
                                                </td>
                                                </tr>
                                                <?php endif; ?>
                                                <?php endforeach; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="7" class="text-center">No testimonials found.</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Footer -->
            <?php include 'templates/footer.php'; ?>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Admin JS -->
    <script src="assets/js/admin.js"></script>
</body>
</html>