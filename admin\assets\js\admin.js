/**
 * Admin Panel JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    // Sidebar Toggle
    const sidebarToggle = document.getElementById('sidebarToggle');
    const adminSidebar = document.getElementById('adminSidebar');
    const adminMain = document.getElementById('adminMain');
    
    if (sidebarToggle && adminSidebar && adminMain) {
        sidebarToggle.addEventListener('click', function() {
            adminSidebar.classList.toggle('collapsed');
            adminMain.classList.toggle('expanded');
            
            // Save state to localStorage
            const isSidebarCollapsed = adminSidebar.classList.contains('collapsed');
            localStorage.setItem('sidebarCollapsed', isSidebarCollapsed);
        });
        
        // Check localStorage for sidebar state
        const isSidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
        if (isSidebarCollapsed) {
            adminSidebar.classList.add('collapsed');
            adminMain.classList.add('expanded');
        }
    }
    
    // Submenu Toggle
    const submenuToggles = document.querySelectorAll('.menu-link[data-bs-toggle="collapse"]');
    submenuToggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            const submenuId = this.getAttribute('href');
            const submenu = document.querySelector(submenuId);
            
            if (submenu) {
                const isExpanded = this.getAttribute('aria-expanded') === 'true';
                this.setAttribute('aria-expanded', !isExpanded);
                
                if (isExpanded) {
                    submenu.classList.remove('show');
                } else {
                    submenu.classList.add('show');
                }
            }
        });
    });
    
    // File Upload Preview
    const fileInputs = document.querySelectorAll('input[type="file"]');
    fileInputs.forEach(input => {
        input.addEventListener('change', function() {
            const fileNameElement = this.parentElement.querySelector('.file-name');
            if (fileNameElement && this.files.length > 0) {
                fileNameElement.textContent = this.files[0].name;
            }
            
            // Image preview
            const previewElement = this.parentElement.parentElement.querySelector('.image-preview');
            if (previewElement && this.files.length > 0) {
                const file = this.files[0];
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    previewElement.src = e.target.result;
                    previewElement.style.display = 'block';
                };
                
                reader.readAsDataURL(file);
            }
        });
    });
    
    // Confirm Delete
    const deleteButtons = document.querySelectorAll('.btn-delete');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            if (!confirm('Are you sure you want to delete this item? This action cannot be undone.')) {
                e.preventDefault();
            }
        });
    });
    
    // Tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function(popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // Auto-dismiss alerts
    const autoAlerts = document.querySelectorAll('.alert.auto-dismiss');
    autoAlerts.forEach(alert => {
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });
    
    // Toggle Password Visibility
    const togglePasswordButtons = document.querySelectorAll('.toggle-password');
    togglePasswordButtons.forEach(button => {
        button.addEventListener('click', function() {
            const passwordInput = this.parentElement.querySelector('input');
            const icon = this.querySelector('i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    });
    
    // Bulk Actions
    const bulkActionCheckboxes = document.querySelectorAll('.bulk-action-checkbox');
    const bulkActionSelectAll = document.getElementById('bulkActionSelectAll');
    
    if (bulkActionSelectAll) {
        bulkActionSelectAll.addEventListener('change', function() {
            bulkActionCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            
            updateBulkActionButton();
        });
    }
    
    bulkActionCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateBulkActionButton();
            
            // Update select all checkbox
            if (bulkActionSelectAll) {
                const allChecked = Array.from(bulkActionCheckboxes).every(cb => cb.checked);
                bulkActionSelectAll.checked = allChecked;
            }
        });
    });
    
    function updateBulkActionButton() {
        const bulkActionButton = document.getElementById('bulkActionButton');
        if (bulkActionButton) {
            const checkedCount = document.querySelectorAll('.bulk-action-checkbox:checked').length;
            bulkActionButton.disabled = checkedCount === 0;
            
            const countBadge = bulkActionButton.querySelector('.count-badge');
            if (countBadge) {
                countBadge.textContent = checkedCount;
                countBadge.style.display = checkedCount > 0 ? 'inline-block' : 'none';
            }
        }
    }
    
    // Bulk Action Form Submit
    const bulkActionForm = document.getElementById('bulkActionForm');
    if (bulkActionForm) {
        bulkActionForm.addEventListener('submit', function(e) {
            const action = document.getElementById('bulkAction').value;
            const checkedCount = document.querySelectorAll('.bulk-action-checkbox:checked').length;
            
            if (action === '') {
                e.preventDefault();
                alert('Please select an action.');
                return;
            }
            
            if (checkedCount === 0) {
                e.preventDefault();
                alert('Please select at least one item.');
                return;
            }
            
            if (action === 'delete' && !confirm('Are you sure you want to delete the selected items? This action cannot be undone.')) {
                e.preventDefault();
            }
        });
    }
    
    // Datepicker Initialization
    const datepickers = document.querySelectorAll('.datepicker');
    datepickers.forEach(datepicker => {
        new Datepicker(datepicker, {
            format: 'yyyy-mm-dd',
            autohide: true
        });
    });
    
    // Rich Text Editor Initialization
    const richTextEditors = document.querySelectorAll('.rich-text-editor');
    richTextEditors.forEach(editor => {
        ClassicEditor
            .create(editor)
            .catch(error => {
                console.error(error);
            });
    });
    
    // Image Upload Preview
    const imageUploads = document.querySelectorAll('.image-upload');
    imageUploads.forEach(upload => {
        const input = upload.querySelector('input[type="file"]');
        const preview = upload.querySelector('.image-preview');
        
        if (input && preview) {
            input.addEventListener('change', function() {
                if (this.files.length > 0) {
                    const file = this.files[0];
                    const reader = new FileReader();
                    
                    reader.onload = function(e) {
                        preview.src = e.target.result;
                        preview.style.display = 'block';
                    };
                    
                    reader.readAsDataURL(file);
                }
            });
        }
    });
    
    // Drag and Drop File Upload
    const dropZones = document.querySelectorAll('.file-upload');
    dropZones.forEach(zone => {
        const input = zone.querySelector('input[type="file"]');
        
        if (input) {
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                zone.addEventListener(eventName, preventDefaults, false);
            });
            
            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }
            
            ['dragenter', 'dragover'].forEach(eventName => {
                zone.addEventListener(eventName, highlight, false);
            });
            
            ['dragleave', 'drop'].forEach(eventName => {
                zone.addEventListener(eventName, unhighlight, false);
            });
            
            function highlight() {
                zone.classList.add('highlight');
            }
            
            function unhighlight() {
                zone.classList.remove('highlight');
            }
            
            zone.addEventListener('drop', handleDrop, false);
            
            function handleDrop(e) {
                const dt = e.dataTransfer;
                const files = dt.files;
                
                input.files = files;
                
                // Trigger change event
                const event = new Event('change', { bubbles: true });
                input.dispatchEvent(event);
            }
        }
    });
});