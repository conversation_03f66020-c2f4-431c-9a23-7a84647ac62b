<?php
/**
 * Coaching Panel Login
 */
require_once '../includes/autoload.php';

// Redirect if already logged in
if (Auth::isCoachingOwnerLoggedIn()) {
    redirect('index.php');
}

// Process login form
$errors = [];
$email = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']);
    
    // Validate form data
    if (empty($email)) {
        $errors['email'] = 'Email is required';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors['email'] = 'Invalid email format';
    }
    
    if (empty($password)) {
        $errors['password'] = 'Password is required';
    }
    
    // If no errors, attempt to login
    if (empty($errors)) {
        $userObj = new User();
        $result = $userObj->login($email, $password, $remember, 'coaching_owner');
        
        if ($result === true) {
            // Get coaching center ID
            $coachingObj = new CoachingCenter();
            $coaching = $coachingObj->getByUserId($_SESSION['user_id']);
            
            if ($coaching) {
                $_SESSION['coaching_id'] = $coaching['coaching_id'];
                $_SESSION['user_type'] = 'coaching_owner'; // Ensure user_type is set
                redirect('index.php');
            } else {
                $errors['general'] = 'No coaching center found for this account';
                // Clear session if no coaching center found
                unset($_SESSION['user_id']);
                unset($_SESSION['user_type']);
            }
        } else {
            $errors['general'] = $result;
        }
    }
}

// Page title
$pageTitle = 'Login';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Coaching Panel</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo getAssetUrl('css/admin.css'); ?>">
    
    <style>
        body {
            background-color: #f8f9fa;
        }
        .login-container {
            max-width: 450px;
            margin: 100px auto;
        }
        .login-logo {
            text-align: center;
            margin-bottom: 30px;
        }
        .login-logo img {
            max-width: 150px;
        }
        .login-card {
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }
        .login-card .card-header {
            background-color: #003366;
            color: white;
            text-align: center;
            border-radius: 10px 10px 0 0;
            padding: 20px;
        }
        .login-card .card-body {
            padding: 30px;
        }
        .form-control {
            padding: 12px;
        }
        .btn-login {
            padding: 12px;
            background-color: #003366;
            border-color: #003366;
        }
        .btn-login:hover {
            background-color: #002244;
            border-color: #002244;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-logo">
            <img src="<?php echo getAssetUrl('images/logo.png'); ?>" alt="Coaching Directory">
        </div>
        
        <div class="card login-card">
            <div class="card-header">
                <h4 class="mb-0">Coaching Center Login</h4>
            </div>
            <div class="card-body">
                <?php if (isset($errors['general'])): ?>
                    <div class="alert alert-danger">
                        <?php echo $errors['general']; ?>
                    </div>
                <?php endif; ?>
                
                <form method="POST" action="">
                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                            <input type="email" class="form-control <?php echo isset($errors['email']) ? 'is-invalid' : ''; ?>" 
                                   id="email" name="email" value="<?php echo htmlspecialchars($email); ?>" placeholder="Enter your email">
                        </div>
                        <?php if (isset($errors['email'])): ?>
                            <div class="invalid-feedback d-block">
                                <?php echo $errors['email']; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                            <input type="password" class="form-control <?php echo isset($errors['password']) ? 'is-invalid' : ''; ?>" 
                                   id="password" name="password" placeholder="Enter your password">
                        </div>
                        <?php if (isset($errors['password'])): ?>
                            <div class="invalid-feedback d-block">
                                <?php echo $errors['password']; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="remember" name="remember">
                        <label class="form-check-label" for="remember">Remember me</label>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary btn-login">Login</button>
                    </div>
                </form>
                
                <div class="mt-3 text-center">
                    <a href="forgot-password.php" class="text-decoration-none">Forgot Password?</a>
                </div>
                
                <hr>
                
                <div class="text-center">
                    <p class="mb-0">Don't have an account?</p>
                    <a href="../register.php?type=coaching_owner" class="btn btn-outline-primary mt-2">Register Your Coaching Center</a>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-3">
            <a href="../index.php" class="text-decoration-none">
                <i class="fas fa-arrow-left me-1"></i> Back to Home
            </a>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
