<?php
/**
 * Header Template
 */

// Get settings
$settings = Settings::getInstance();

// Get categories for menu
$categoryObj = new Category();
$menuCategories = $categoryObj->getPopular(5);

// Get cities for menu
$locationObj = new Location();
$menuCities = $locationObj->getPopularCities(5);
?>
<header class="site-header">
    <!-- Top Bar -->
    <div class="top-bar bg-dark text-light">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="contact-info">
                        <span><i class="fas fa-phone"></i> <?php echo $settings->getContactPhone(); ?></span>
                        <span><i class="fas fa-envelope"></i> <?php echo $settings->getContactEmail(); ?></span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="social-links text-md-end">
                        <?php $socialLinks = $settings->getSocialLinks(); ?>
                        <?php if (!empty($socialLinks['facebook'])): ?>
                            <a href="<?php echo $socialLinks['facebook']; ?>" target="_blank"><i class="fab fa-facebook-f"></i></a>
                        <?php endif; ?>
                        <?php if (!empty($socialLinks['twitter'])): ?>
                            <a href="<?php echo $socialLinks['twitter']; ?>" target="_blank"><i class="fab fa-twitter"></i></a>
                        <?php endif; ?>
                        <?php if (!empty($socialLinks['instagram'])): ?>
                            <a href="<?php echo $socialLinks['instagram']; ?>" target="_blank"><i class="fab fa-instagram"></i></a>
                        <?php endif; ?>
                        <?php if (!empty($socialLinks['linkedin'])): ?>
                            <a href="<?php echo $socialLinks['linkedin']; ?>" target="_blank"><i class="fab fa-linkedin-in"></i></a>
                        <?php endif; ?>
                        <?php if (!empty($socialLinks['youtube'])): ?>
                            <a href="<?php echo $socialLinks['youtube']; ?>" target="_blank"><i class="fab fa-youtube"></i></a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Main Header -->
    <div class="main-header">
        <div class="container">
            <nav class="navbar navbar-expand-lg navbar-light">
                <a class="navbar-brand" href="<?php echo getBaseUrl(); ?>">
                    <img src="<?php echo getAssetUrl($settings->getSiteLogo()); ?>" alt="<?php echo $settings->getSiteName(); ?>" class="logo">
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav ms-auto">
                        <li class="nav-item">
                            <a class="nav-link <?php echo isCurrentUrl('/index.php') || $_SERVER['REQUEST_URI'] == '/' ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>">Home</a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle <?php echo isCurrentUrl('/category/') ? 'active' : ''; ?>" href="#" id="categoriesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Categories
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="categoriesDropdown">
                                <?php foreach ($menuCategories as $category): ?>
                                    <li><a class="dropdown-item" href="<?php echo getCategoryUrl($category['slug']); ?>"><?php echo $category['category_name']; ?></a></li>
                                <?php endforeach; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?php echo getBaseUrl(); ?>categories.php">All Categories</a></li>
                            </ul>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle <?php echo isCurrentUrl('/city/') ? 'active' : ''; ?>" href="#" id="citiesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Cities
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="citiesDropdown">
                                <?php foreach ($menuCities as $city): ?>
                                    <li><a class="dropdown-item" href="<?php echo getCityUrl($city['slug']); ?>"><?php echo $city['city_name']; ?></a></li>
                                <?php endforeach; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?php echo getBaseUrl(); ?>cities.php">All Cities</a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo isCurrentUrl('/blog') ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>blog.php">Blog</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo isCurrentUrl('/contact.php') ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>contact.php">Contact</a>
                        </li>
                        <?php if (isLoggedIn()): ?>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-user"></i> <?php echo getCurrentUser('username'); ?>
                                </a>
                                <ul class="dropdown-menu" aria-labelledby="userDropdown">
                                    <?php if (isAdmin()): ?>
                                        <li><a class="dropdown-item" href="<?php echo getAdminUrl(); ?>">Admin Dashboard</a></li>
                                    <?php elseif (isCoachingOwner()): ?>
                                        <li><a class="dropdown-item" href="<?php echo getBaseUrl(); ?>dashboard/index.php">Dashboard</a></li>
                                        <li><a class="dropdown-item" href="<?php echo getBaseUrl(); ?>dashboard/coaching.php">My Coaching Centers</a></li>
                                    <?php else: ?>
                                        <li><a class="dropdown-item" href="<?php echo getBaseUrl(); ?>dashboard/index.php">Dashboard</a></li>
                                        <li><a class="dropdown-item" href="<?php echo getBaseUrl(); ?>dashboard/favorites.php">My Favorites</a></li>
                                    <?php endif; ?>
                                    <li><a class="dropdown-item" href="<?php echo getBaseUrl(); ?>dashboard/profile.php">My Profile</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="<?php echo getBaseUrl(); ?>logout.php">Logout</a></li>
                                </ul>
                            </li>
                        <?php else: ?>
                            <li class="nav-item">
                                <a class="nav-link <?php echo isCurrentUrl('/login.php') ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>login.php">Login</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link btn btn-primary text-white <?php echo isCurrentUrl('/register.php') ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>register.php">Register</a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </nav>
        </div>
    </div>
</header>

<?php if (hasFlashMessage()): ?>
    <div class="container mt-3">
        <?php echo getFlashMessage(); ?>
    </div>
<?php endif; ?>