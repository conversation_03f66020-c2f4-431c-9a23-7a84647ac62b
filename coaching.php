<?php
/**
 * Coaching Center Detail Page
 */
require_once 'includes/autoload.php';

// Get settings
$settings = Settings::getInstance();

// Get coaching slug from URL
$slug = isset($_GET['slug']) ? $_GET['slug'] : '';

if (empty($slug)) {
    redirect('index.php');
}

// Get coaching center details
$coachingObj = new CoachingCenter();
$coaching = $coachingObj->getBySlug($slug);

if (!$coaching) {
    setFlashMessage('Coaching center not found.', 'danger');
    redirect('index.php');
}

// Get reviews
$page = getCurrentPage();
$reviewsData = $coachingObj->getReviews($coaching['coaching_id'], $page, 5);
$reviews = $reviewsData['reviews'];
$totalReviews = $reviewsData['total'];
$totalPages = $reviewsData['total_pages'];

// Process review form
$reviewSubmitted = false;
if (isPostRequest() && isLoggedIn()) {
    if (checkCSRFToken()) {
        $reviewData = [
            'coaching_id' => $coaching['coaching_id'],
            'user_id' => getCurrentUser('user_id'),
            'rating' => $_POST['rating'],
            'title' => $_POST['title'],
            'review_text' => $_POST['review_text'],
            'pros' => $_POST['pros'],
            'cons' => $_POST['cons'],
            'is_anonymous' => isset($_POST['is_anonymous']) ? 1 : 0,
            'status' => 'pending'
        ];
        
        $reviewId = $coachingObj->addReview($reviewData);
        
        if ($reviewId) {
            $reviewSubmitted = true;
            setFlashMessage('Your review has been submitted and is pending approval. Thank you for your feedback!', 'success');
        } else {
            setFlashMessage('Failed to submit review. Please try again.', 'danger');
        }
    } else {
        setFlashMessage('Invalid form submission. Please try again.', 'danger');
    }
}

// Process inquiry form
$inquirySubmitted = false;
if (isPostRequest() && isset($_POST['inquiry_submit'])) {
    if (checkCSRFToken()) {
        $inquiryData = [
            'coaching_id' => $coaching['coaching_id'],
            'user_id' => isLoggedIn() ? getCurrentUser('user_id') : null,
            'name' => $_POST['name'],
            'email' => $_POST['email'],
            'phone' => $_POST['phone'],
            'subject' => $_POST['subject'],
            'message' => $_POST['message'],
            'status' => 'pending'
        ];
        
        $inquiryId = $coachingObj->addInquiry($inquiryData);
        
        if ($inquiryId) {
            $inquirySubmitted = true;
            setFlashMessage('Your inquiry has been sent. We will get back to you soon!', 'success');
        } else {
            setFlashMessage('Failed to send inquiry. Please try again.', 'danger');
        }
    } else {
        setFlashMessage('Invalid form submission. Please try again.', 'danger');
    }
}

// Get similar coaching centers
$similarCoachings = [];
if (!empty($coaching['categories']) && isset($coaching['categories'][0])) {
    $categoryId = $coaching['categories'][0]['category_id'];
    $similarCoachings = $coachingObj->getByCategory($categoryId, 3);
    
    // Remove current coaching from similar list
    foreach ($similarCoachings as $key => $similar) {
        if ($similar['coaching_id'] == $coaching['coaching_id']) {
            unset($similarCoachings[$key]);
            break;
        }
    }
    
    // Limit to 3 items
    $similarCoachings = array_slice($similarCoachings, 0, 3);
}

// Page title and meta
$pageTitle = $coaching['coaching_name'];
$pageDescription = truncate(strip_tags($coaching['description']), 160);
$pageKeywords = '';

// Build keywords from categories
if (!empty($coaching['categories'])) {
    $categoryNames = array_column($coaching['categories'], 'category_name');
    $pageKeywords = implode(', ', $categoryNames);
}

// Add location to keywords
if (!empty($coaching['city_name'])) {
    $pageKeywords .= ', ' . $coaching['city_name'];
}
if (!empty($coaching['state_name'])) {
    $pageKeywords .= ', ' . $coaching['state_name'];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?php echo getMetaTags($pageTitle . ' - ' . $settings->getSiteName(), $pageDescription, $pageKeywords, !empty($coaching['logo']) ? getUploadUrl($coaching['logo']) : ''); ?>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo getAssetUrl($settings->getSiteFavicon()); ?>" type="image/x-icon">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- GLightbox CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/glightbox/dist/css/glightbox.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo getAssetUrl('css/style.css'); ?>">
</head>
<body>
    <!-- Header -->
    <?php include 'templates/header.php'; ?>
    
    <!-- Breadcrumb -->
    <section class="breadcrumb-section">
        <div class="container">
            <?php
            $breadcrumbItems = [
                ['title' => 'Home', 'url' => getBaseUrl()],
                ['title' => 'Coaching Centers', 'url' => getBaseUrl() . 'search.php']
            ];
            
            if (!empty($coaching['categories']) && isset($coaching['categories'][0])) {
                $breadcrumbItems[] = [
                    'title' => $coaching['categories'][0]['category_name'],
                    'url' => getCategoryUrl($coaching['categories'][0]['category_slug'])
                ];
            }
            
            $breadcrumbItems[] = ['title' => $coaching['coaching_name'], 'url' => ''];
            
            echo getBreadcrumb($breadcrumbItems);
            ?>
        </div>
    </section>
    
    <!-- Coaching Header Section -->
    <section class="coaching-header-section">
        <div class="container">
            <div class="coaching-header">
                <div class="row align-items-center">
                    <div class="col-lg-2 col-md-3">
                        <div class="coaching-logo">
                            <img src="<?php echo isset($coaching['logo']) ? getUploadUrl($coaching['logo']) : getAssetUrl('images/default-logo.png'); ?>" alt="<?php echo $coaching['coaching_name']; ?>">
                        </div>
                    </div>
                    <div class="col-lg-7 col-md-6">
                        <div class="coaching-info">
                            <h1><?php echo $coaching['coaching_name']; ?></h1>
                            <div class="coaching-meta">
                                <div class="location">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <?php 
                                    $locationParts = [];
                                    if (!empty($coaching['location_name'])) $locationParts[] = $coaching['location_name'];
                                    if (!empty($coaching['city_name'])) $locationParts[] = $coaching['city_name'];
                                    if (!empty($coaching['state_name'])) $locationParts[] = $coaching['state_name'];
                                    echo implode(', ', $locationParts);
                                    ?>
                                </div>
                                <div class="categories">
                                    <?php foreach ($coaching['categories'] as $index => $category): ?>
                                        <?php if ($index < 3): ?>
                                            <a href="<?php echo getCategoryUrl($category['category_slug']); ?>" class="category-badge"><?php echo $category['category_name']; ?></a>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                    <?php if (count($coaching['categories']) > 3): ?>
                                        <span class="category-badge">+<?php echo count($coaching['categories']) - 3; ?> more</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="coaching-rating">
                                <?php echo getStarRating($coaching['avg_rating']); ?>
                                <span class="rating-text"><?php echo number_format($coaching['avg_rating'], 1); ?> out of 5</span>
                                <span class="reviews">(<?php echo $coaching['total_reviews']; ?> reviews)</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-3">
                        <div class="coaching-actions">
                            <?php if (isLoggedIn()): ?>
                                <button class="btn btn-outline-primary favorite-button <?php echo $coachingObj->isInFavorites(getCurrentUser('user_id'), $coaching['coaching_id']) ? 'active' : ''; ?>" data-id="<?php echo $coaching['coaching_id']; ?>">
                                    <i class="<?php echo $coachingObj->isInFavorites(getCurrentUser('user_id'), $coaching['coaching_id']) ? 'fas' : 'far'; ?> fa-heart"></i> 
                                    <?php echo $coachingObj->isInFavorites(getCurrentUser('user_id'), $coaching['coaching_id']) ? 'Saved' : 'Save'; ?>
                                </button>
                            <?php else: ?>
                                <a href="login.php?redirect=<?php echo urlencode(getCurrentUrl()); ?>" class="btn btn-outline-primary">
                                    <i class="far fa-heart"></i> Save
                                </a>
                            <?php endif; ?>
                            <a href="#inquiry" class="btn btn-primary">
                                <i class="fas fa-envelope"></i> Contact
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Coaching Detail Section -->
    <section class="coaching-detail-section section-padding">
        <div class="container">
            <div class="row">
                <!-- Main Content -->
                <div class="col-lg-8">
                    <!-- Tabs Navigation -->
                    <ul class="nav nav-tabs coaching-tabs" id="coachingTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab" aria-controls="overview" aria-selected="true">Overview</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="courses-tab" data-bs-toggle="tab" data-bs-target="#courses" type="button" role="tab" aria-controls="courses" aria-selected="false">Courses</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="results-tab" data-bs-toggle="tab" data-bs-target="#results" type="button" role="tab" aria-controls="results" aria-selected="false">Success Stories</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="reviews-tab" data-bs-toggle="tab" data-bs-target="#reviews" type="button" role="tab" aria-controls="reviews" aria-selected="false">Reviews</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="gallery-tab" data-bs-toggle="tab" data-bs-target="#gallery" type="button" role="tab" aria-controls="gallery" aria-selected="false">Gallery</button>
                        </li>
                    </ul>
                    
                    <!-- Tabs Content -->
                    <div class="tab-content coaching-tab-content" id="coachingTabsContent">
                        <!-- Overview Tab -->
                        <div class="tab-pane fade show active" id="overview" role="tabpanel" aria-labelledby="overview-tab">
                            <div class="overview-content">
                                <h3>About <?php echo $coaching['coaching_name']; ?></h3>
                                <div class="coaching-description">
                                    <?php echo nl2br($coaching['description']); ?>
                                </div>
                                
                                <?php if (!empty($coaching['established_year'])): ?>
                                    <div class="established">
                                        <strong>Established:</strong> <?php echo $coaching['established_year']; ?>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if (!empty($coaching['facilities'])): ?>
                                    <h4>Facilities</h4>
                                    <div class="facilities-list">
                                        <div class="row">
                                            <?php foreach ($coaching['facilities'] as $facility): ?>
                                                <div class="col-md-4 col-6">
                                                    <div class="facility-item">
                                                        <?php if (!empty($facility['icon'])): ?>
                                                            <img src="<?php echo getAssetUrl($facility['icon']); ?>" alt="<?php echo $facility['facility_name']; ?>">
                                                        <?php else: ?>
                                                            <i class="fas fa-check-circle"></i>
                                                        <?php endif; ?>
                                                        <span><?php echo $facility['facility_name']; ?></span>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if (!empty($coaching['working_hours'])): ?>
                                    <h4>Working Hours</h4>
                                    <div class="working-hours">
                                        <?php echo nl2br($coaching['working_hours']); ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <!-- Courses Tab -->
                        <div class="tab-pane fade" id="courses" role="tabpanel" aria-labelledby="courses-tab">
                            <?php if (!empty($coaching['courses'])): ?>
                                <div class="courses-list">
                                    <h3>Courses Offered</h3>
                                    <div class="accordion" id="coursesAccordion">
                                        <?php foreach ($coaching['courses'] as $index => $course): ?>
                                            <div class="accordion-item">
                                                <h2 class="accordion-header" id="course-heading-<?php echo $course['course_id']; ?>">
                                                    <button class="accordion-button <?php echo $index > 0 ? 'collapsed' : ''; ?>" type="button" data-bs-toggle="collapse" data-bs-target="#course-collapse-<?php echo $course['course_id']; ?>" aria-expanded="<?php echo $index === 0 ? 'true' : 'false'; ?>" aria-controls="course-collapse-<?php echo $course['course_id']; ?>">
                                                        <?php echo $course['course_name']; ?>
                                                        <?php if (!empty($course['category_name'])): ?>
                                                            <span class="course-category"><?php echo $course['category_name']; ?></span>
                                                        <?php endif; ?>
                                                    </button>
                                                </h2>
                                                <div id="course-collapse-<?php echo $course['course_id']; ?>" class="accordion-collapse collapse <?php echo $index === 0 ? 'show' : ''; ?>" aria-labelledby="course-heading-<?php echo $course['course_id']; ?>" data-bs-parent="#coursesAccordion">
                                                    <div class="accordion-body">
                                                        <div class="course-details">
                                                            <?php if (!empty($course['description'])): ?>
                                                                <div class="course-description">
                                                                    <?php echo nl2br($course['description']); ?>
                                                                </div>
                                                            <?php endif; ?>
                                                            
                                                            <div class="course-meta">
                                                                <?php if (!empty($course['duration'])): ?>
                                                                    <div class="meta-item">
                                                                        <strong><i class="far fa-clock"></i> Duration:</strong>
                                                                        <span><?php echo $course['duration']; ?></span>
                                                                    </div>
                                                                <?php endif; ?>
                                                                
                                                                <?php if (!empty($course['batch_size'])): ?>
                                                                    <div class="meta-item">
                                                                        <strong><i class="fas fa-users"></i> Batch Size:</strong>
                                                                        <span><?php echo $course['batch_size']; ?> students</span>
                                                                    </div>
                                                                <?php endif; ?>
                                                            </div>
                                                            
                                                            <?php if (!empty($course['fee_structure'])): ?>
                                                                <div class="course-section">
                                                                    <h5>Fee Structure</h5>
                                                                    <div class="section-content">
                                                                        <?php echo nl2br($course['fee_structure']); ?>
                                                                    </div>
                                                                </div>
                                                            <?php endif; ?>
                                                            
                                                            <?php if (!empty($course['batch_timings'])): ?>
                                                                <div class="course-section">
                                                                    <h5>Batch Timings</h5>
                                                                    <div class="section-content">
                                                                        <?php echo nl2br($course['batch_timings']); ?>
                                                                    </div>
                                                                </div>
                                                            <?php endif; ?>
                                                            
                                                            <?php if (!empty($course['study_material'])): ?>
                                                                <div class="course-section">
                                                                    <h5>Study Material</h5>
                                                                    <div class="section-content">
                                                                        <?php echo nl2br($course['study_material']); ?>
                                                                    </div>
                                                                </div>
                                                            <?php endif; ?>
                                                            
                                                            <?php if (!empty($course['faculty_details'])): ?>
                                                                <div class="course-section">
                                                                    <h5>Faculty</h5>
                                                                    <div class="section-content">
                                                                        <?php echo nl2br($course['faculty_details']); ?>
                                                                    </div>
                                                                </div>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="no-data">
                                    <p>No courses information available.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Success Stories Tab -->
                        <div class="tab-pane fade" id="results" role="tabpanel" aria-labelledby="results-tab">
                            <?php if (!empty($coaching['success_stories'])): ?>
                                <div class="success-stories">
                                    <h3>Success Stories</h3>
                                    <div class="row">
                                        <?php foreach ($coaching['success_stories'] as $story): ?>
                                            <div class="col-md-6">
                                                <div class="success-story-card">
                                                    <div class="story-header">
                                                        <div class="student-image">
                                                            <?php if (!empty($story['student_image'])): ?>
                                                                <img src="<?php echo getUploadUrl($story['student_image']); ?>" alt="<?php echo $story['student_name']; ?>">
                                                            <?php else: ?>
                                                                <img src="<?php echo getAssetUrl('images/student-default.jpg'); ?>" alt="<?php echo $story['student_name']; ?>">
                                                            <?php endif; ?>
                                                        </div>
                                                        <div class="student-info">
                                                            <h4><?php echo $story['student_name']; ?></h4>
                                                            <?php if (!empty($story['course_name'])): ?>
                                                                <div class="course"><?php echo $story['course_name']; ?></div>
                                                            <?php endif; ?>
                                                            <?php if (!empty($story['year'])): ?>
                                                                <div class="year"><?php echo $story['year']; ?></div>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                    <div class="story-content">
                                                        <?php if (!empty($story['rank_achieved'])): ?>
                                                            <div class="achievement-rank">
                                                                <span class="rank"><?php echo $story['rank_achieved']; ?></span>
                                                                <span class="rank-label">Rank</span>
                                                            </div>
                                                        <?php endif; ?>
                                                        <div class="achievement">
                                                            <?php echo nl2br($story['achievement']); ?>
                                                        </div>
                                                        <?php if (!empty($story['testimonial'])): ?>
                                                            <div class="testimonial">
                                                                <i class="fas fa-quote-left"></i>
                                                                <?php echo nl2br($story['testimonial']); ?>
                                                                <i class="fas fa-quote-right"></i>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="no-data">
                                    <p>No success stories available.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Reviews Tab -->
                        <div class="tab-pane fade" id="reviews" role="tabpanel" aria-labelledby="reviews-tab">
                            <div class="reviews-section">
                                <div class="reviews-header">
                                    <h3>Student Reviews</h3>
                                    <div class="overall-rating">
                                        <div class="rating-number"><?php echo number_format($coaching['avg_rating'], 1); ?></div>
                                        <div class="rating-stars">
                                            <?php echo getStarRating($coaching['avg_rating']); ?>
                                            <span class="rating-count"><?php echo $coaching['total_reviews']; ?> reviews</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <?php if (count($reviews) > 0): ?>
                                    <div class="reviews-list">
                                        <?php foreach ($reviews as $review): ?>
                                            <div class="review-item">
                                                <div class="review-header">
                                                    <div class="reviewer-info">
                                                        <?php if ($review['is_anonymous']): ?>
                                                            <div class="reviewer-image">
                                                                <img src="<?php echo getAssetUrl('images/anonymous-user.jpg'); ?>" alt="Anonymous User">
                                                            </div>
                                                            <div class="reviewer-name">Anonymous</div>
                                                        <?php else: ?>
                                                            <div class="reviewer-image">
                                                                <img src="<?php echo getUserProfileImage($review['profile_image']); ?>" alt="<?php echo $review['username']; ?>">
                                                            </div>
                                                            <div class="reviewer-name">
                                                                <?php 
                                                                if (!empty($review['first_name']) && !empty($review['last_name'])) {
                                                                    echo $review['first_name'] . ' ' . $review['last_name'];
                                                                } else {
                                                                    echo $review['username'];
                                                                }
                                                                ?>
                                                            </div>
                                                        <?php endif; ?>
                                                        <div class="review-date"><?php echo formatDate($review['created_at']); ?></div>
                                                    </div>
                                                    <div class="review-rating">
                                                        <?php echo getStarRating($review['rating']); ?>
                                                    </div>
                                                </div>
                                                <div class="review-content">
                                                    <?php if (!empty($review['title'])): ?>
                                                        <h4 class="review-title"><?php echo $review['title']; ?></h4>
                                                    <?php endif; ?>
                                                    
                                                    <div class="review-text">
                                                        <?php echo nl2br($review['review_text']); ?>
                                                    </div>
                                                    
                                                    <?php if (!empty($review['pros']) || !empty($review['cons'])): ?>
                                                        <div class="review-pros-cons">
                                                            <?php if (!empty($review['pros'])): ?>
                                                                <div class="pros">
                                                                    <h5><i class="fas fa-thumbs-up"></i> Pros</h5>
                                                                    <p><?php echo nl2br($review['pros']); ?></p>
                                                                </div>
                                                            <?php endif; ?>
                                                            
                                                            <?php if (!empty($review['cons'])): ?>
                                                                <div class="cons">
                                                                    <h5><i class="fas fa-thumbs-down"></i> Cons</h5>
                                                                    <p><?php echo nl2br($review['cons']); ?></p>
                                                                </div>
                                                            <?php endif; ?>
                                                        </div>
                                                    <?php endif; ?>
                                                    
                                                    <?php if (!empty($review['admin_response'])): ?>
                                                        <div class="admin-response">
                                                            <h5><i class="fas fa-reply"></i> Response from <?php echo $coaching['coaching_name']; ?></h5>
                                                            <p><?php echo nl2br($review['admin_response']); ?></p>
                                                        </div>
                                                    <?php endif; ?>
                                                    
                                                    <div class="review-actions">
                                                        <button class="helpful-button <?php echo isLoggedIn() ? '' : 'disabled'; ?>" data-id="<?php echo $review['review_id']; ?>" data-type="helpful" <?php echo isLoggedIn() ? '' : 'disabled'; ?>>
                                                            <i class="far fa-thumbs-up"></i> Helpful
                                                            <span class="count">(<?php echo $review['helpful_count']; ?>)</span>
                                                        </button>
                                                        <button class="helpful-button report-button <?php echo isLoggedIn() ? '' : 'disabled'; ?>" data-id="<?php echo $review['review_id']; ?>" data-type="report" <?php echo isLoggedIn() ? '' : 'disabled'; ?>>
                                                            <i class="far fa-flag"></i> Report
                                                            <span class="count">(<?php echo $review['report_count']; ?>)</span>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                        
                                        <!-- Pagination -->
                                        <?php if ($totalPages > 1): ?>
                                            <div class="pagination-container">
                                                <?php echo getPagination($totalReviews, $page, 5, getCurrentUrlWithParams([], ['page']) . '#reviews'); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php else: ?>
                                    <div class="no-reviews">
                                        <p>No reviews yet. Be the first to review this coaching center!</p>
                                    </div>
                                <?php endif; ?>
                                
                                <!-- Write Review Form -->
                                <?php if (isLoggedIn() && !$reviewSubmitted): ?>
                                    <div class="write-review">
                                        <h4>Write a Review</h4>
                                        <form action="<?php echo getCurrentUrl(); ?>#reviews" method="POST" class="review-form">
                                            <?php echo getCSRFTokenField(); ?>
                                            
                                            <div class="form-group mb-3">
                                                <label>Your Rating</label>
                                                <div class="rating-input">
                                                    <div class="stars">
                                                        <span class="star" data-value="1"><i class="far fa-star"></i></span>
                                                        <span class="star" data-value="2"><i class="far fa-star"></i></span>
                                                        <span class="star" data-value="3"><i class="far fa-star"></i></span>
                                                        <span class="star" data-value="4"><i class="far fa-star"></i></span>
                                                        <span class="star" data-value="5"><i class="far fa-star"></i></span>
                                                    </div>
                                                    <input type="hidden" name="rating" value="5" required>
                                                </div>
                                            </div>
                                            
                                            <div class="form-group mb-3">
                                                <label for="review-title">Review Title</label>
                                                <input type="text" id="review-title" name="title" class="form-control" placeholder="Summarize your experience" required>
                                            </div>
                                            
                                            <div class="form-group mb-3">
                                                <label for="review-text">Your Review</label>
                                                <textarea id="review-text" name="review_text" class="form-control" rows="4" placeholder="Describe your experience with this coaching center" required></textarea>
                                            </div>
                                            
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group mb-3">
                                                        <label for="review-pros">Pros (Optional)</label>
                                                        <textarea id="review-pros" name="pros" class="form-control" rows="3" placeholder="What did you like?"></textarea>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group mb-3">
                                                        <label for="review-cons">Cons (Optional)</label>
                                                        <textarea id="review-cons" name="cons" class="form-control" rows="3" placeholder="What could be improved?"></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="form-check mb-3">
                                                <input type="checkbox" class="form-check-input" id="anonymous-review" name="is_anonymous">
                                                <label class="form-check-label" for="anonymous-review">Post as Anonymous</label>
                                            </div>
                                            
                                            <div class="form-group">
                                                <button type="submit" class="btn btn-primary">Submit Review</button>
                                            </div>
                                        </form>
                                    </div>
                                <?php elseif (!isLoggedIn()): ?>
                                    <div class="login-to-review">
                                        <p>Please <a href="login.php?redirect=<?php echo urlencode(getCurrentUrl() . '#reviews'); ?>">login</a> to write a review.</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <!-- Gallery Tab -->
                        <div class="tab-pane fade" id="gallery" role="tabpanel" aria-labelledby="gallery-tab">
                            <?php if (!empty($coaching['images'])): ?>
                                <div class="gallery-section">
                                    <h3>Photo Gallery</h3>
                                    <div class="row gallery-grid">
                                        <?php foreach ($coaching['images'] as $image): ?>
                                            <div class="col-md-4 col-6">
                                                <a href="<?php echo getUploadUrl($image['image_path']); ?>" class="gallery-item" data-caption="<?php echo !empty($image['caption']) ? $image['caption'] : $coaching['coaching_name']; ?>">
                                                    <img src="<?php echo getUploadUrl($image['image_path']); ?>" alt="<?php echo !empty($image['caption']) ? $image['caption'] : $coaching['coaching_name']; ?>" class="img-fluid">
                                                </a>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="no-data">
                                    <p>No gallery images available.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Sidebar -->
                <div class="col-lg-4">
                    <div class="coaching-sidebar">
                        <!-- Contact Information -->
                        <div class="sidebar-widget contact-widget">
                            <h3>Contact Information</h3>
                            <ul class="contact-info">
                                <?php if (!empty($coaching['address'])): ?>
                                    <li>
                                        <i class="fas fa-map-marker-alt"></i>
                                        <div>
                                            <span class="label">Address:</span>
                                            <span class="value"><?php echo nl2br($coaching['address']); ?></span>
                                        </div>
                                    </li>
                                <?php endif; ?>
                                
                                <?php if (!empty($coaching['phone'])): ?>
                                    <li>
                                        <i class="fas fa-phone"></i>
                                        <div>
                                            <span class="label">Phone:</span>
                                            <span class="value"><a href="tel:<?php echo $coaching['phone']; ?>"><?php echo $coaching['phone']; ?></a></span>
                                        </div>
                                    </li>
                                <?php endif; ?>
                                
                                <?php if (!empty($coaching['whatsapp'])): ?>
                                    <li>
                                        <i class="fab fa-whatsapp"></i>
                                        <div>
                                            <span class="label">WhatsApp:</span>
                                            <span class="value"><a href="https://wa.me/<?php echo preg_replace('/[^0-9]/', '', $coaching['whatsapp']); ?>" target="_blank"><?php echo $coaching['whatsapp']; ?></a></span>
                                        </div>
                                    </li>
                                <?php endif; ?>
                                
                                <?php if (!empty($coaching['email'])): ?>
                                    <li>
                                        <i class="fas fa-envelope"></i>
                                        <div>
                                            <span class="label">Email:</span>
                                            <span class="value"><a href="mailto:<?php echo $coaching['email']; ?>"><?php echo $coaching['email']; ?></a></span>
                                        </div>
                                    </li>
                                <?php endif; ?>
                                
                                <?php if (!empty($coaching['website'])): ?>
                                    <li>
                                        <i class="fas fa-globe"></i>
                                        <div>
                                            <span class="label">Website:</span>
                                            <span class="value"><a href="<?php echo $coaching['website']; ?>" target="_blank"><?php echo $coaching['website']; ?></a></span>
                                        </div>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </div>
                        
                        <!-- Map Widget -->
                        <?php if (!empty($coaching['latitude']) && !empty($coaching['longitude'])): ?>
                            <div class="sidebar-widget map-widget">
                                <h3>Location Map</h3>
                                <div class="map-container">
                                    <iframe 
                                        width="100%" 
                                        height="250" 
                                        frameborder="0" 
                                        scrolling="no" 
                                        marginheight="0" 
                                        marginwidth="0" 
                                        src="https://maps.google.com/maps?q=<?php echo $coaching['latitude']; ?>,<?php echo $coaching['longitude']; ?>&z=15&output=embed"
                                    ></iframe>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Inquiry Form -->
                        <div class="sidebar-widget inquiry-widget" id="inquiry">
                            <h3>Send Inquiry</h3>
                            <form action="<?php echo getCurrentUrl(); ?>#inquiry" method="POST" class="inquiry-form">
                                <?php echo getCSRFTokenField(); ?>
                                <input type="hidden" name="inquiry_submit" value="1">
                                
                                <div class="form-group mb-3">
                                    <label for="inquiry-name">Your Name</label>
                                    <input type="text" id="inquiry-name" name="name" class="form-control" value="<?php echo isLoggedIn() ? getCurrentUser('first_name') . ' ' . getCurrentUser('last_name') : ''; ?>" required>
                                </div>
                                
                                <div class="form-group mb-3">
                                    <label for="inquiry-email">Email Address</label>
                                    <input type="email" id="inquiry-email" name="email" class="form-control" value="<?php echo isLoggedIn() ? getCurrentUser('email') : ''; ?>" required>
                                </div>
                                
                                <div class="form-group mb-3">
                                    <label for="inquiry-phone">Phone Number</label>
                                    <input type="tel" id="inquiry-phone" name="phone" class="form-control" value="<?php echo isLoggedIn() ? getCurrentUser('phone') : ''; ?>">
                                </div>
                                
                                <div class="form-group mb-3">
                                    <label for="inquiry-subject">Subject</label>
                                    <input type="text" id="inquiry-subject" name="subject" class="form-control" required>
                                </div>
                                
                                <div class="form-group mb-3">
                                    <label for="inquiry-message">Message</label>
                                    <textarea id="inquiry-message" name="message" class="form-control" rows="4" required></textarea>
                                </div>
                                
                                <div class="form-group">
                                    <button type="submit" class="btn btn-primary w-100">Send Message</button>
                                </div>
                            </form>
                        </div>
                        
                        <!-- Similar Coaching Centers -->
                        <?php if (!empty($similarCoachings)): ?>
                            <div class="sidebar-widget similar-widget">
                                <h3>Similar Coaching Centers</h3>
                                <div class="similar-list">
                                    <?php foreach ($similarCoachings as $similar): ?>
                                        <div class="similar-item">
                                            <div class="similar-image">
                                                <img src="<?php echo isset($similar['logo']) ? getUploadUrl($similar['logo']) : getAssetUrl('images/default-logo.png'); ?>" alt="<?php echo $similar['coaching_name']; ?>">
                                            </div>
                                            <div class="similar-content">
                                                <h4><a href="<?php echo getCoachingUrl($similar['slug']); ?>"><?php echo $similar['coaching_name']; ?></a></h4>
                                                <div class="similar-location">
                                                    <i class="fas fa-map-marker-alt"></i> <?php echo $similar['city_name']; ?>
                                                </div>
                                                <div class="similar-rating">
                                                    <?php echo getStarRating($similar['avg_rating']); ?>
                                                    <span class="rating-count">(<?php echo $similar['total_reviews']; ?>)</span>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Footer -->
    <?php include 'templates/footer.php'; ?>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- GLightbox JS -->
    <script src="https://cdn.jsdelivr.net/npm/glightbox/dist/js/glightbox.min.js"></script>
    
    <!-- Custom JS -->
    <script src="<?php echo getAssetUrl('js/main.js'); ?>"></script>
</body>
</html>