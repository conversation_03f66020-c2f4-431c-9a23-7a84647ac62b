<?php
/**
 * Coaching Panel - Enquiries
 * This file handles the display and management of enquiries for a coaching center
 */
require_once '../includes/autoload.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in as coaching owner
Auth::requireCoachingOwner();

// Get coaching center data
if (!isset($_SESSION['coaching_id'])) {
    redirect('login.php');
    exit;
}
$coachingId = $_SESSION['coaching_id'];

// Get coaching center details
$coachingObj = new CoachingCenter();
$coaching = $coachingObj->getById($coachingId);

// If coaching center not found, redirect to login
if (!$coaching) {
    redirect('login.php');
}

// Initialize database
$db = Database::getInstance();

// Handle status update
if (isset($_POST['update_status']) && isset($_POST['enquiry_id']) && isset($_POST['status'])) {
    $enquiryId = (int)$_POST['enquiry_id'];
    $status = $_POST['status'];
    
    // Validate status
    if (in_array($status, ['new', 'contacted', 'closed'])) {
        $result = $db->update('enquiries', [
            'status' => $status,
            'updated_at' => date('Y-m-d H:i:s')
        ], 'enquiry_id = ? AND coaching_id = ?', [$enquiryId, $coachingId]);
        
        if ($result) {
            $successMessage = 'Enquiry status updated successfully.';
        } else {
            $errorMessage = 'Failed to update enquiry status.';
        }
    } else {
        $errorMessage = 'Invalid status value.';
    }
}

// Get enquiries for this coaching center
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10;
$offset = ($page - 1) * $limit;

// Get status filter
$statusFilter = isset($_GET['status']) ? $_GET['status'] : '';
$whereClause = 'coaching_id = ?';
$params = [$coachingId];

if (!empty($statusFilter) && in_array($statusFilter, ['new', 'contacted', 'closed'])) {
    $whereClause .= ' AND status = ?';
    $params[] = $statusFilter;
}

// Count total enquiries
$totalEnquiries = $db->count('enquiries', $whereClause, $params);
$totalPages = ceil($totalEnquiries / $limit);

// Get enquiries with pagination
$enquiries = $db->fetchAll(
    "SELECT e.*, c.course_name, l.location_name, ci.city_name
     FROM enquiries e
     LEFT JOIN courses c ON e.course_id = c.course_id
     LEFT JOIN locations l ON e.location_id = l.location_id
     LEFT JOIN cities ci ON l.city_id = ci.city_id
     WHERE $whereClause
     ORDER BY e.created_at DESC
     LIMIT $offset, $limit",
    $params
);

// Page title
$pageTitle = 'Manage Enquiries';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Coaching Panel</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo getAssetUrl('css/admin.css'); ?>">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <?php include 'templates/sidebar.php'; ?>
        
        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <?php include 'templates/header.php'; ?>
            
            <!-- Content -->
            <div class="container-fluid">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h1 class="page-title"><?php echo $pageTitle; ?></h1>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="btn-group">
                            <a href="enquiries.php" class="btn <?php echo empty($statusFilter) ? 'btn-primary' : 'btn-outline-primary'; ?>">All</a>
                            <a href="enquiries.php?status=new" class="btn <?php echo $statusFilter === 'new' ? 'btn-primary' : 'btn-outline-primary'; ?>">New</a>
                            <a href="enquiries.php?status=contacted" class="btn <?php echo $statusFilter === 'contacted' ? 'btn-primary' : 'btn-outline-primary'; ?>">Contacted</a>
                            <a href="enquiries.php?status=closed" class="btn <?php echo $statusFilter === 'closed' ? 'btn-primary' : 'btn-outline-primary'; ?>">Closed</a>
                        </div>
                    </div>
                </div>
                
                <?php if (isset($successMessage)): ?>
                    <div class="alert alert-success"><?php echo $successMessage; ?></div>
                <?php endif; ?>
                
                <?php if (isset($errorMessage)): ?>
                    <div class="alert alert-danger"><?php echo $errorMessage; ?></div>
                <?php endif; ?>
                
                <div class="card">
                    <div class="card-body">
                        <?php if (empty($enquiries)): ?>
                            <div class="alert alert-info">No enquiries found.</div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Name</th>
                                            <th>Contact</th>
                                            <th>Course</th>
                                            <th>Location</th>
                                            <th>Date</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($enquiries as $enquiry): ?>
                                            <tr>
                                                <td><?php echo $enquiry['enquiry_id']; ?></td>
                                                <td><?php echo htmlspecialchars($enquiry['name']); ?></td>
                                                <td>
                                                    <div><?php echo htmlspecialchars($enquiry['email']); ?></div>
                                                    <div><?php echo htmlspecialchars($enquiry['phone']); ?></div>
                                                </td>
                                                <td><?php echo !empty($enquiry['course_name']) ? htmlspecialchars($enquiry['course_name']) : 'N/A'; ?></td>
                                                <td>
                                                    <?php 
                                                    if (!empty($enquiry['location_name'])) {
                                                        echo htmlspecialchars($enquiry['location_name']);
                                                        if (!empty($enquiry['city_name'])) {
                                                            echo ', ' . htmlspecialchars($enquiry['city_name']);
                                                        }
                                                    } else {
                                                        echo 'N/A';
                                                    }
                                                    ?>
                                                </td>
                                                <td><?php echo date('d M Y', strtotime($enquiry['created_at'])); ?></td>
                                                <td>
                                                    <span class="badge bg-<?php 
                                                        echo $enquiry['status'] === 'new' ? 'primary' : 
                                                            ($enquiry['status'] === 'contacted' ? 'warning' : 'success'); 
                                                    ?>">
                                                        <?php echo ucfirst($enquiry['status']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-primary view-enquiry" 
                                                            data-bs-toggle="modal" data-bs-target="#enquiryModal" 
                                                            data-enquiry-id="<?php echo $enquiry['enquiry_id']; ?>"
                                                            data-name="<?php echo htmlspecialchars($enquiry['name']); ?>"
                                                            data-email="<?php echo htmlspecialchars($enquiry['email']); ?>"
                                                            data-phone="<?php echo htmlspecialchars($enquiry['phone']); ?>"
                                                            data-course="<?php echo !empty($enquiry['course_name']) ? htmlspecialchars($enquiry['course_name']) : ''; ?>"
                                                            data-location="<?php 
                                                                $location = '';
                                                                if (!empty($enquiry['location_name'])) {
                                                                    $location = htmlspecialchars($enquiry['location_name']);
                                                                    if (!empty($enquiry['city_name'])) {
                                                                        $location .= ', ' . htmlspecialchars($enquiry['city_name']);
                                                                    }
                                                                }
                                                                echo $location;
                                                            ?>"
                                                            data-message="<?php echo htmlspecialchars($enquiry['message']); ?>"
                                                            data-date="<?php echo date('d M Y H:i', strtotime($enquiry['created_at'])); ?>"
                                                            data-status="<?php echo $enquiry['status']; ?>">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- Pagination -->
                            <?php if ($totalPages > 1): ?>
                                <nav aria-label="Page navigation">
                                    <ul class="pagination justify-content-center">
                                        <?php if ($page > 1): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="?page=<?php echo $page - 1; ?><?php echo !empty($statusFilter) ? '&status=' . $statusFilter : ''; ?>">
                                                    <i class="fas fa-chevron-left"></i>
                                                </a>
                                            </li>
                                        <?php endif; ?>
                                        
                                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                                <a class="page-link" href="?page=<?php echo $i; ?><?php echo !empty($statusFilter) ? '&status=' . $statusFilter : ''; ?>">
                                                    <?php echo $i; ?>
                                                </a>
                                            </li>
                                        <?php endfor; ?>
                                        
                                        <?php if ($page < $totalPages): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="?page=<?php echo $page + 1; ?><?php echo !empty($statusFilter) ? '&status=' . $statusFilter : ''; ?>">
                                                    <i class="fas fa-chevron-right"></i>
                                                </a>
                                            </li>
                                        <?php endif; ?>
                                    </ul>
                                </nav>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Enquiry Modal -->
    <div class="modal fade" id="enquiryModal" tabindex="-1" aria-labelledby="enquiryModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="enquiryModalLabel">Enquiry Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6>Contact Information</h6>
                            <p><strong>Name:</strong> <span id="modal-name"></span></p>
                            <p><strong>Email:</strong> <span id="modal-email"></span></p>
                            <p><strong>Phone:</strong> <span id="modal-phone"></span></p>
                        </div>
                        <div class="col-md-6">
                            <h6>Enquiry Details</h6>
                            <p><strong>Date:</strong> <span id="modal-date"></span></p>
                            <p><strong>Course:</strong> <span id="modal-course"></span></p>
                            <p><strong>Location:</strong> <span id="modal-location"></span></p>
                        </div>
                    </div>
                    <div class="mb-3">
                        <h6>Message</h6>
                        <div class="p-3 bg-light rounded" id="modal-message"></div>
                    </div>
                    <form id="status-form" method="post">
                        <input type="hidden" name="enquiry_id" id="modal-enquiry-id">
                        <input type="hidden" name="update_status" value="1">
                        <div class="mb-3">
                            <label for="modal-status" class="form-label">Status</label>
                            <select class="form-select" id="modal-status" name="status">
                                <option value="new">New</option>
                                <option value="contacted">Contacted</option>
                                <option value="closed">Closed</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary">Update Status</button>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="<?php echo getAssetUrl('js/admin.js'); ?>"></script>
    
    <script>
        $(document).ready(function() {
            // Handle view enquiry button click
            $('.view-enquiry').click(function() {
                const enquiryId = $(this).data('enquiry-id');
                const name = $(this).data('name');
                const email = $(this).data('email');
                const phone = $(this).data('phone');
                const course = $(this).data('course');
                const location = $(this).data('location');
                const message = $(this).data('message');
                const date = $(this).data('date');
                const status = $(this).data('status');
                
                // Set modal values
                $('#modal-enquiry-id').val(enquiryId);
                $('#modal-name').text(name);
                $('#modal-email').text(email);
                $('#modal-phone').text(phone);
                $('#modal-course').text(course || 'N/A');
                $('#modal-location').text(location || 'N/A');
                $('#modal-message').text(message || 'No message provided.');
                $('#modal-date').text(date);
                $('#modal-status').val(status);
            });
        });
    </script>
</body>
</html>