<?php
/**
 * Debug Session - Check what's in the session
 */
require_once '../includes/autoload.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>Session Debug</h1>";
echo "<h2>Session Data:</h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h2>Auth Status:</h2>";
echo "isLoggedIn(): " . (Auth::isLoggedIn() ? 'YES' : 'NO') . "<br>";
echo "isCoachingOwnerLoggedIn(): " . (Auth::isCoachingOwnerLoggedIn() ? 'YES' : 'NO') . "<br>";
echo "getUserId(): " . (Auth::getUserId() ?? 'NULL') . "<br>";

if (isset($_SESSION['coaching_id'])) {
    echo "<h2>Coaching Center:</h2>";
    $coachingObj = new CoachingCenter();
    $coaching = $coachingObj->getById($_SESSION['coaching_id']);
    echo "<pre>";
    print_r($coaching);
    echo "</pre>";
}
?>