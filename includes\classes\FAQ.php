<?php
/**
 * FAQ Class
 * Handles operations related to FAQs
 */
class FAQ {
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Get all FAQs
     * @param array $filters Optional filters
     * @param int $page Page number
     * @param int $limit Records per page
     * @return array FAQs and pagination info
     */
    public function getAll($filters = [], $page = 1, $limit = 10) {
        $where = "1=1";
        $params = [];
        
        // Apply filters
        if (isset($filters['status']) && !empty($filters['status'])) {
            $where .= " AND status = ?";
            $params[] = $filters['status'];
        }
        
        if (isset($filters['category_id']) && !empty($filters['category_id'])) {
            $where .= " AND category_id = ?";
            $params[] = $filters['category_id'];
        }
        
        if (isset($filters['search']) && !empty($filters['search'])) {
            $where .= " AND (question LIKE ? OR answer LIKE ?)";
            $params[] = "%{$filters['search']}%";
            $params[] = "%{$filters['search']}%";
        }
        
        // Count total records
        $totalCount = $this->db->count('faqs', $where, $params);
        $totalPages = ceil($totalCount / $limit);
        
        // Ensure page is within valid range
        $page = max(1, min($page, $totalPages));
        $offset = ($page - 1) * $limit;
        
        // Get FAQs
        $sql = "SELECT f.*, fc.category_name 
                FROM faqs f 
                LEFT JOIN faq_categories fc ON f.category_id = fc.category_id 
                WHERE {$where} 
                ORDER BY f.display_order ASC, f.created_at DESC 
                LIMIT ?, ?";
        $params[] = $offset;
        $params[] = $limit;
        
        $faqs = $this->db->fetchAll($sql, $params);
        
        return [
            'faqs' => $faqs,
            'total_count' => $totalCount,
            'total_pages' => $totalPages,
            'current_page' => $page
        ];
    }
    
    /**
     * Get FAQ by ID
     * @param int $faqId FAQ ID
     * @return array|null FAQ data
     */
    public function getById($faqId) {
        $sql = "SELECT f.*, fc.category_name 
                FROM faqs f 
                LEFT JOIN faq_categories fc ON f.category_id = fc.category_id 
                WHERE f.faq_id = ?";
        return $this->db->fetchRow($sql, [$faqId]);
    }
    
    /**
     * Create new FAQ
     * @param array $data FAQ data
     * @return int|bool New FAQ ID or false on failure
     */
    public function create($data) {
        return $this->db->insert('faqs', $data);
    }
    
    /**
     * Update FAQ
     * @param int $faqId FAQ ID
     * @param array $data FAQ data
     * @return bool Success or failure
     */
    public function update($faqId, $data) {
        return $this->db->update('faqs', $data, 'faq_id = ?', [$faqId]);
    }
    
    /**
     * Delete FAQ
     * @param int $faqId FAQ ID
     * @return bool Success or failure
     */
    public function delete($faqId) {
        return $this->db->delete('faqs', 'faq_id = ?', [$faqId]);
    }
    
    /**
     * Get FAQ categories
     * @return array FAQ categories
     */
    public function getCategories() {
        $sql = "SELECT * FROM faq_categories ORDER BY display_order ASC, category_name ASC";
        return $this->db->fetchAll($sql);
    }
    
    /**
     * Get FAQs by category
     * @param int $categoryId Category ID
     * @param string $status Status filter (optional)
     * @return array FAQs
     */
    public function getByCategory($categoryId, $status = 'active') {
        $sql = "SELECT * FROM faqs WHERE category_id = ?";
        $params = [$categoryId];
        
        if (!empty($status)) {
            $sql .= " AND status = ?";
            $params[] = $status;
        }
        
        $sql .= " ORDER BY display_order ASC, created_at DESC";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Get count by status
     * @param string $status Status
     * @return int Count
     */
    public function getCountByStatus($status) {
        return $this->db->count('faqs', 'status = ?', [$status]);
    }
}