<?php
/**
 * Admin Coaching Reviews
 */
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    require_once '../includes/autoload.php';
    
    // Check user authentication
    $user = new User();
    if (!$user->isLoggedIn() || !$user->isAdmin()) {
        header('Location: login.php');
        exit;
    }
    
    // Get admin info
    $adminInfo = $user->getUserData();
    
    // Initialize settings
    $settings = Settings::getInstance();
    
    // Initialize review object
    $reviewObj = new Review();
    
    // Handle actions
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    $reviewId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    $message = '';
    
    if ($action === 'delete' && $reviewId > 0) {
        if ($reviewObj->delete($reviewId)) {
            $message = '<div class="alert alert-success">Review deleted successfully.</div>';
        } else {
            $message = '<div class="alert alert-danger">Failed to delete review.</div>';
        }
    } else if ($action === 'approve' && $reviewId > 0) {
        if ($reviewObj->update($reviewId, ['status' => 'approved'])) {
            $message = '<div class="alert alert-success">Review approved successfully.</div>';
        } else {
            $message = '<div class="alert alert-danger">Failed to approve review.</div>';
        }
    } else if ($action === 'reject' && $reviewId > 0) {
        if ($reviewObj->update($reviewId, ['status' => 'rejected'])) {
            $message = '<div class="alert alert-success">Review rejected successfully.</div>';
        } else {
            $message = '<div class="alert alert-danger">Failed to reject review.</div>';
        }
    }
    
    // Get reviews with pagination
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = 10;
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    $coachingId = isset($_GET['coaching_id']) ? (int)$_GET['coaching_id'] : 0;
    
    // Prepare filters
    $filters = [];
    if (!empty($status)) {
        $filters['status'] = $status;
    }
    if ($coachingId > 0) {
        $filters['coaching_id'] = $coachingId;
    }
    
    // Get reviews
    $result = $reviewObj->getAll($filters, $page, $limit);
    $reviews = $result['reviews'] ?? [];
    $totalPages = $result['total_pages'] ?? 1;
    $totalCount = $result['total_count'] ?? 0;
    
    // Get coaching centers for filter dropdown
    $coachingObj = new CoachingCenter();
    $coachings = $coachingObj->getAllForDropdown();
    
} catch (Exception $e) {
    die('<div style="color:red;padding:20px;border:2px solid red;margin:20px;">'
        . '<h2>Error</h2>'
        . '<p><strong>Message:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>'
        . '<p><strong>File:</strong> ' . htmlspecialchars($e->getFile()) . '</p>'
        . '<p><strong>Line:</strong> ' . htmlspecialchars($e->getLine()) . '</p>'
        . '</div>');
}

// Page title
$pageTitle = 'Coaching Reviews';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Admin Panel | <?php echo $settings->getSiteName(); ?></title>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo getAssetUrl($settings->getSiteFavicon()); ?>" type="image/x-icon">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Admin CSS -->
    <link rel="stylesheet" href="assets/css/admin.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <?php include 'templates/sidebar.php'; ?>
        
        <!-- Main Content -->
        <div class="admin-main" id="adminMain">
            <!-- Header -->
            <?php include 'templates/header.php'; ?>
            
            <!-- Content -->
            <div class="admin-content">
                <div class="page-title">
                    <div>
                        <h1><?php echo $pageTitle; ?></h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="dashboard.php">Home</a></li>
                                <li class="breadcrumb-item"><a href="coaching-centers.php">Coaching Centers</a></li>
                                <li class="breadcrumb-item active" aria-current="page"><?php echo $pageTitle; ?></li>
                            </ol>
                        </nav>
                    </div>
                </div>
                
                <?php echo $message; ?>
                
                <!-- Filters -->
                <div class="admin-card mb-4">
                    <div class="admin-card-body">
                        <form action="" method="get" class="row g-3">
                            <div class="col-md-5">
                                <label for="coaching_id" class="form-label">Coaching Center</label>
                                <select class="form-select" id="coaching_id" name="coaching_id">
                                    <option value="">All Coaching Centers</option>
                                    <?php foreach ($coachings as $coaching): ?>
                                        <option value="<?php echo $coaching['coaching_id']; ?>" <?php echo $coachingId == $coaching['coaching_id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($coaching['coaching_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">All Status</option>
                                    <option value="approved" <?php echo $status === 'approved' ? 'selected' : ''; ?>>Approved</option>
                                    <option value="pending" <?php echo $status === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                    <option value="rejected" <?php echo $status === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                                </select>
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <a href="coaching-reviews.php" class="btn btn-secondary w-100">
                                    <i class="fas fa-sync-alt"></i> Reset
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Reviews Table -->
                <div class="admin-card">
                    <div class="admin-card-header">
                        <h2 class="admin-card-title">
                            <i class="fas fa-star me-2"></i> All Reviews
                        </h2>
                        <span class="badge bg-primary"><?php echo $totalCount; ?> Total</span>
                    </div>
                    <div class="admin-card-body p-0">
                        <div class="table-responsive">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>User</th>
                                        <th>Coaching Center</th>
                                        <th>Rating</th>
                                        <th>Review</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($reviews)): ?>
                                        <?php foreach ($reviews as $review): ?>
                                            <tr>
                                                <td><?php echo $review['review_id']; ?></td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <?php if (!empty($review['profile_image'])): ?>
                                                            <img src="<?php echo getAssetUrl($review['profile_image']); ?>" alt="<?php echo htmlspecialchars($review['username']); ?>" class="me-2" style="width: 32px; height: 32px; object-fit: cover; border-radius: 50%;">
                                                        <?php else: ?>
                                                            <div class="placeholder-image me-2" style="width: 32px; height: 32px; background-color: #e9ecef; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                                                <i class="fas fa-user text-secondary"></i>
                                                            </div>
                                                        <?php endif; ?>
                                                        <div>
                                                            <?php echo htmlspecialchars($review['username']); ?>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td><?php echo htmlspecialchars($review['coaching_name']); ?></td>
                                                <td>
                                                    <div class="rating">
                                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                                            <?php if ($i <= $review['rating']): ?>
                                                                <i class="fas fa-star text-warning"></i>
                                                            <?php else: ?>
                                                                <i class="far fa-star text-warning"></i>
                                                            <?php endif; ?>
                                                        <?php endfor; ?>
                                                        <span class="ms-1"><?php echo $review['rating']; ?>/5</span>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="review-text">
                                                        <?php echo nl2br(htmlspecialchars(substr($review['review_text'], 0, 100))); ?>
                                                        <?php if (strlen($review['review_text']) > 100): ?>
                                                            <a href="#" data-bs-toggle="modal" data-bs-target="#reviewModal<?php echo $review['review_id']; ?>">...Read More</a>
                                                        <?php endif; ?>
                                                    </div>
                                                    
                                                    <!-- Review Modal -->
                                                    <div class="modal fade" id="reviewModal<?php echo $review['review_id']; ?>" tabindex="-1" aria-labelledby="reviewModalLabel<?php echo $review['review_id']; ?>" aria-hidden="true">
                                                        <div class="modal-dialog">
                                                            <div class="modal-content">
                                                                <div class="modal-header">
                                                                    <h5 class="modal-title" id="reviewModalLabel<?php echo $review['review_id']; ?>">Review by <?php echo htmlspecialchars($review['username']); ?></h5>
                                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                </div>
                                                                <div class="modal-body">
                                                                    <div class="rating mb-3">
                                                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                                                            <?php if ($i <= $review['rating']): ?>
                                                                                <i class="fas fa-star text-warning"></i>
                                                                            <?php else: ?>
                                                                                <i class="far fa-star text-warning"></i>
                                                                            <?php endif; ?>
                                                                        <?php endfor; ?>
                                                                        <span class="ms-1"><?php echo $review['rating']; ?>/5</span>
                                                                    </div>
                                                                    <p><?php echo nl2br(htmlspecialchars($review['review_text'])); ?></p>
                                                                    <div class="text-muted">
                                                                        <small>Posted on: <?php echo date('M d, Y', strtotime($review['created_at'])); ?></small>
                                                                    </div>
                                                                </div>
                                                                <div class="modal-footer">
                                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                                    <?php if ($review['status'] === 'pending'): ?>
                                                                        <a href="coaching-reviews.php?action=approve&id=<?php echo $review['review_id']; ?>" class="btn btn-success">Approve</a>
                                                                        <a href="coaching-reviews.php?action=reject&id=<?php echo $review['review_id']; ?>" class="btn btn-danger">Reject</a>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <?php if ($review['status'] === 'approved'): ?>
                                                        <span class="badge bg-success">Approved</span>
                                                    <?php elseif ($review['status'] === 'pending'): ?>
                                                        <span class="badge bg-warning">Pending</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">Rejected</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo date('M d, Y', strtotime($review['created_at'])); ?></td>
                                                <td>
                                                    <div class="btn-group">
                                                        <?php if ($review['status'] === 'pending'): ?>
                                                            <a href="coaching-reviews.php?action=approve&id=<?php echo $review['review_id']; ?>" class="btn btn-sm btn-success" title="Approve">
                                                                <i class="fas fa-check"></i>
                                                            </a>
                                                            <a href="coaching-reviews.php?action=reject&id=<?php echo $review['review_id']; ?>" class="btn btn-sm btn-warning" title="Reject">
                                                                <i class="fas fa-times"></i>
                                                            </a>
                                                        <?php endif; ?>
                                                        <a href="coaching-reviews.php?action=delete&id=<?php echo $review['review_id']; ?>" class="btn btn-sm btn-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this review?');">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="8" class="text-center">No reviews found.</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <div class="admin-card-footer">
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center mb-0">
                                    <?php if ($page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $page - 1; ?>&status=<?php echo urlencode($status); ?>&coaching_id=<?php echo $coachingId; ?>" aria-label="Previous">
                                                <span aria-hidden="true">&laquo;</span>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                    
                                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo urlencode($status); ?>&coaching_id=<?php echo $coachingId; ?>">
                                                <?php echo $i; ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>
                                    
                                    <?php if ($page < $totalPages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $page + 1; ?>&status=<?php echo urlencode($status); ?>&coaching_id=<?php echo $coachingId; ?>" aria-label="Next">
                                                <span aria-hidden="true">&raquo;</span>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Footer -->
            <?php include 'templates/footer.php'; ?>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Admin JS -->
    <script src="assets/js/admin.js"></script>
</body>
</html>