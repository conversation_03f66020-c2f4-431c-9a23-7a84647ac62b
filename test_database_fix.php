<?php
/**
 * Test script to verify the database fix
 */
require_once 'includes/autoload.php';

echo "<h1>Testing Database Fix</h1>";

try {
    // Get database instance
    $db = Database::getInstance();
    echo "<p>✓ Database connection successful</p>";
    
    // Test fetchAll with a simple query
    echo "<h2>Testing fetchAll method</h2>";
    $tables = $db->fetchAll("SHOW TABLES");
    if ($tables !== false) {
        echo "<p>✓ fetchAll works - Found " . count($tables) . " tables</p>";
    } else {
        echo "<p>✗ fetchAll failed</p>";
    }
    
    // Test fetchRow with a simple query
    echo "<h2>Testing fetchRow method</h2>";
    $tableCheck = $db->fetchRow("SHOW TABLES LIKE 'coaching_centers'");
    if ($tableCheck !== false) {
        echo "<p>✓ fetchRow works - coaching_centers table " . ($tableCheck ? "exists" : "does not exist") . "</p>";
    } else {
        echo "<p>✗ fetchRow failed</p>";
    }
    
    // Test fetchOne with a simple query
    echo "<h2>Testing fetchOne method</h2>";
    $count = $db->fetchOne("SELECT COUNT(*) FROM coaching_centers");
    if ($count !== false && $count !== null) {
        echo "<p>✓ fetchOne works - Found {$count} coaching centers</p>";
    } else {
        echo "<p>✗ fetchOne failed or returned null</p>";
    }
    
    // Test update method (this was causing the original error)
    echo "<h2>Testing update method</h2>";
    
    // First, let's get a coaching center to update
    $coaching = $db->fetchRow("SELECT * FROM coaching_centers LIMIT 1");
    if ($coaching) {
        $coachingId = $coaching['coaching_id'];
        echo "<p>Found coaching center with ID: {$coachingId}</p>";
        
        // Try a simple update that doesn't actually change anything
        $updateData = [
            'coaching_name' => $coaching['coaching_name'] // Same value, no actual change
        ];
        
        $result = $db->update('coaching_centers', $updateData, 'coaching_id = ?', [$coachingId]);
        if ($result) {
            echo "<p>✓ Update method works</p>";
        } else {
            echo "<p>✗ Update method failed</p>";
        }
    } else {
        echo "<p>No coaching centers found to test update</p>";
    }
    
    echo "<h2>Test Summary</h2>";
    echo "<p>If you see checkmarks (✓) above, the database fix is working correctly.</p>";
    echo "<p>The original error should now be resolved.</p>";
    
} catch (Exception $e) {
    echo "<p>✗ Error during testing: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
