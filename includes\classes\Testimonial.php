<?php
/**
 * Testimonial Class
 * Handles operations related to testimonials
 */
class Testimonial {
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Get all testimonials
     * @param array $filters Optional filters
     * @param int $page Page number
     * @param int $limit Records per page
     * @return array Testimonials and pagination info
     */
    public function getAll($filters = [], $page = 1, $limit = 10) {
        $where = "1=1";
        $params = [];
        
        // Apply filters
        if (isset($filters['status']) && !empty($filters['status'])) {
            $where .= " AND status = ?";
            $params[] = $filters['status'];
        }
        
        if (isset($filters['is_featured']) && $filters['is_featured'] !== null) {
            $where .= " AND is_featured = ?";
            $params[] = $filters['is_featured'] ? 1 : 0;
        }
        
        if (isset($filters['search']) && !empty($filters['search'])) {
            $where .= " AND (name LIKE ? OR position LIKE ? OR content LIKE ?)";
            $params[] = "%{$filters['search']}%";
            $params[] = "%{$filters['search']}%";
            $params[] = "%{$filters['search']}%";
        }
        
        // Count total records
        $totalCount = $this->db->count('testimonials', $where, $params);
        $totalPages = ceil($totalCount / $limit);
        
        // Ensure page is within valid range
        $page = max(1, min($page, $totalPages));
        $offset = ($page - 1) * $limit;
        
        // Get testimonials
        $sql = "SELECT * FROM testimonials WHERE {$where} ORDER BY created_at DESC LIMIT ?, ?";
        $params[] = $offset;
        $params[] = $limit;
        
        $testimonials = $this->db->fetchAll($sql, $params);
        
        return [
            'testimonials' => $testimonials,
            'total_count' => $totalCount,
            'total_pages' => $totalPages,
            'current_page' => $page
        ];
    }
    
    /**
     * Get featured testimonials
     * @param int $limit Number of testimonials to return
     * @return array Testimonials
     */
    public function getFeatured($limit = 5) {
        $sql = "SELECT * FROM testimonials WHERE status = 'active' AND is_featured = 1 ORDER BY created_at DESC LIMIT ?";
        return $this->db->fetchAll($sql, [$limit]);
    }
    
    /**
     * Get testimonial by ID
     * @param int $testimonialId Testimonial ID
     * @return array|null Testimonial data
     */
    public function getById($testimonialId) {
        $sql = "SELECT * FROM testimonials WHERE testimonial_id = ?";
        return $this->db->fetchRow($sql, [$testimonialId]);
    }
    
    /**
     * Create new testimonial
     * @param array $data Testimonial data
     * @return int|bool New testimonial ID or false on failure
     */
    public function create($data) {
        return $this->db->insert('testimonials', $data);
    }
    
    /**
     * Update testimonial
     * @param int $testimonialId Testimonial ID
     * @param array $data Testimonial data
     * @return bool Success or failure
     */
    public function update($testimonialId, $data) {
        return $this->db->update('testimonials', $data, 'testimonial_id = ?', [$testimonialId]);
    }
    
    /**
     * Delete testimonial
     * @param int $testimonialId Testimonial ID
     * @return bool Success or failure
     */
    public function delete($testimonialId) {
        return $this->db->delete('testimonials', 'testimonial_id = ?', [$testimonialId]);
    }
    
    /**
     * Get count by status
     * @param string $status Status
     * @return int Count
     */
    public function getCountByStatus($status) {
        return $this->db->count('testimonials', 'status = ?', [$status]);
    }
    
    /**
     * Get total count of testimonials
     * @param string $status Optional status filter
     * @return int Total count
     */
    public function getTotalCount($status = null) {
        $where = "1=1";
        $params = [];
        
        if ($status !== null) {
            $where .= " AND status = ?";
            $params[] = $status;
        }
        
        return $this->db->count('testimonials', $where, $params);
    }
}