<?php
// Database connection details
$host = 'localhost';
$dbname = 'coaching_directory';
$username = 'root';
$password = '';

try {
    // Connect to database
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check if users table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    $tableExists = $stmt->rowCount() > 0;
    
    if ($tableExists) {
        // Get column names
        $stmt = $pdo->query("DESCRIBE users");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        // Check if name column exists
        $nameColumnExists = in_array('name', $columns);
        
        if (!$nameColumnExists) {
            // Check if first_name and last_name columns exist
            $firstNameExists = in_array('first_name', $columns);
            $lastNameExists = in_array('last_name', $columns);
            
            if ($firstNameExists && $lastNameExists) {
                echo "Users table already exists with first_name and last_name columns.<br>";
            } else {
                // Add name column if it doesn't exist
                $pdo->exec("ALTER TABLE users ADD COLUMN name varchar(100) NOT NULL AFTER user_id");
                echo "Added name column to users table.<br>";
            }
        } else {
            echo "Users table already exists with name column.<br>";
        }
    } else {
        // Create users table
        $sql = "CREATE TABLE IF NOT EXISTS `users` (
            `user_id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(100) NOT NULL,
            `email` varchar(100) NOT NULL,
            `password` varchar(255) NOT NULL,
            `phone` varchar(20) DEFAULT NULL,
            `profile_image` varchar(255) DEFAULT NULL,
            `status` enum('active','inactive') NOT NULL DEFAULT 'active',
            `remember_token` varchar(255) DEFAULT NULL,
            `remember_expires` datetime DEFAULT NULL,
            `reset_token` varchar(255) DEFAULT NULL,
            `reset_expires` datetime DEFAULT NULL,
            `created_at` datetime NOT NULL,
            `updated_at` datetime DEFAULT NULL,
            PRIMARY KEY (`user_id`),
            UNIQUE KEY `email` (`email`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;";
        
        $pdo->exec($sql);
        echo "Users table created successfully!<br>";
    }
    
    // Create user_favorites table
    $sql = "CREATE TABLE IF NOT EXISTS `user_favorites` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `coaching_id` int(11) NOT NULL,
        `created_at` datetime NOT NULL,
        PRIMARY KEY (`id`),
        KEY `user_id` (`user_id`),
        KEY `coaching_id` (`coaching_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;";
    
    $pdo->exec($sql);
    echo "User favorites table created successfully!<br>";
    
    // Check if users table exists and get column names
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    $tableExists = $stmt->rowCount() > 0;
    
    if ($tableExists) {
        $stmt = $pdo->query("DESCRIBE users");
        $columns = array_map(function($col) { return $col['Field']; }, $stmt->fetchAll(PDO::FETCH_ASSOC));
        
        // Create default user with password 'user123'
        $hashedPassword = password_hash('user123', PASSWORD_DEFAULT);
        $now = date('Y-m-d H:i:s');
        
        // Check if user already exists
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE email = ?");
        $stmt->execute(['<EMAIL>']);
        $userExists = $stmt->fetchColumn() > 0;
        
        if (!$userExists) {
            // Check if name column exists
            if (in_array('name', $columns)) {
                $stmt = $pdo->prepare("INSERT INTO `users` (`name`, `email`, `password`, `status`, `created_at`) VALUES (?, ?, ?, ?, ?)");
                $stmt->execute(['Test User', '<EMAIL>', $hashedPassword, 'active', $now]);
            } else if (in_array('first_name', $columns) && in_array('last_name', $columns)) {
                $stmt = $pdo->prepare("INSERT INTO `users` (`first_name`, `last_name`, `email`, `password`, `status`, `created_at`) VALUES (?, ?, ?, ?, ?, ?)");
                $stmt->execute(['Test', 'User', '<EMAIL>', $hashedPassword, 'active', $now]);
            } else {
                echo "Could not determine user table structure.<br>";
                return;
            }
            
            echo "Default user created successfully!<br>";
            echo "Email: <EMAIL><br>";
            echo "Password: user123<br>";
        } else {
            echo "Default user already exists.<br>";
        }
    } else {
        echo "Users table does not exist.<br>";
    }
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>