<?php
/**
 * Admin Add Coaching Center
 */
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    require_once '../includes/autoload.php';
    
    // Check user authentication
    $user = new User();
    if (!$user->isLoggedIn() || !$user->isAdmin()) {
        header('Location: login.php');
        exit;
    }
    
    // Get admin info
    $adminInfo = $user->getUserData();
    
    // Initialize settings
    $settings = Settings::getInstance();
    
    // Initialize coaching center object
    $coachingObj = new CoachingCenter();
    
    // Get categories, states, cities, and facilities for dropdowns
    $categoryObj = new Category();
    $categories = $categoryObj->getAll();
    
    $stateObj = new State();
    $statesData = $stateObj->getAll();
    $states = $statesData['states'] ?? [];
    
    $cityObj = new City();
    $cities = [];
    
    $locationObj = new Location();
    $locations = [];
    
    $facilityObj = new Facility();
    $facilitiesData = $facilityObj->getAll();
    $facilities = $facilitiesData['facilities'] ?? [];
    
    // Handle form submission
    $message = '';
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Validate form data
        $errors = [];
        
        // Required fields
        $requiredFields = ['coaching_name', 'email', 'phone', 'address', 'state_id', 'city_id'];
        foreach ($requiredFields as $field) {
            if (empty($_POST[$field])) {
                $errors[] = ucfirst(str_replace('_', ' ', $field)) . ' is required.';
            }
        }
        
        // Email validation
        if (!empty($_POST['email']) && !filter_var($_POST['email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Please enter a valid email address.';
        }
        
        // Check if email already exists
        if (!empty($_POST['email'])) {
            $existingCoaching = $coachingObj->getByEmail($_POST['email']);
            if ($existingCoaching) {
                $errors[] = 'Email address is already in use.';
            }
        }
        
        // Password validation removed as per requirement
        
        // If no errors, process form
        if (empty($errors)) {
            // Prepare coaching data
            $coachingData = [
                'coaching_name' => $_POST['coaching_name'],
                'email' => $_POST['email'],
                'phone' => $_POST['phone'],
                'address' => $_POST['address'],
                'state_id' => $_POST['state_id'],
                'city_id' => $_POST['city_id'],
                'location_id' => !empty($_POST['location_id']) ? $_POST['location_id'] : null,
                'description' => !empty($_POST['description']) ? $_POST['description'] : null,
                'website' => !empty($_POST['website']) ? $_POST['website'] : null,
                'established_year' => !empty($_POST['established_year']) ? $_POST['established_year'] : null,
                'status' => $_POST['status'],
                'is_featured' => isset($_POST['is_featured']) ? 1 : 0,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            // Add categories and facilities
            if (!empty($_POST['categories'])) {
                $coachingData['categories'] = $_POST['categories'];
            }
            
            if (!empty($_POST['facilities'])) {
                $coachingData['facilities'] = $_POST['facilities'];
            }
            
            // Create coaching center
            $coachingId = $coachingObj->create($coachingData);
            
            if ($coachingId) {
                // Handle logo upload
                if (!empty($_FILES['logo']['name'])) {
                    $uploadDir = '../uploads/coaching/logos/';
                    $fileName = 'logo_' . $coachingId . '_' . time() . '.' . pathinfo($_FILES['logo']['name'], PATHINFO_EXTENSION);
                    $targetFile = $uploadDir . $fileName;
                    
                    if (move_uploaded_file($_FILES['logo']['tmp_name'], $targetFile)) {
                        $logoPath = 'uploads/coaching/logos/' . $fileName;
                        $coachingObj->update($coachingId, ['logo' => $logoPath]);
                    }
                }
                
                // Redirect to coaching centers page
                header('Location: coaching-centers.php?message=created');
                exit;
            } else {
                $message = '<div class="alert alert-danger">Failed to create coaching center. Please try again.</div>';
            }
        } else {
            $message = '<div class="alert alert-danger"><ul class="mb-0">';
            foreach ($errors as $error) {
                $message .= '<li>' . $error . '</li>';
            }
            $message .= '</ul></div>';
        }
    }
    
} catch (Exception $e) {
    die('<div style="color:red;padding:20px;border:2px solid red;margin:20px;">'
        . '<h2>Error</h2>'
        . '<p><strong>Message:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>'
        . '<p><strong>File:</strong> ' . htmlspecialchars($e->getFile()) . '</p>'
        . '<p><strong>Line:</strong> ' . htmlspecialchars($e->getLine()) . '</p>'
        . '</div>');
}

// Page title
$pageTitle = 'Add Coaching Center';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Admin Panel | <?php echo $settings->getSiteName(); ?></title>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo getAssetUrl($settings->getSiteFavicon()); ?>" type="image/x-icon">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
    
    <!-- Summernote CSS -->
    <link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs5.min.css" rel="stylesheet">
    
    <!-- Admin CSS -->
    <link rel="stylesheet" href="assets/css/admin.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <?php include 'templates/sidebar.php'; ?>
        
        <!-- Main Content -->
        <div class="admin-main" id="adminMain">
            <!-- Header -->
            <?php include 'templates/header.php'; ?>
            
            <!-- Content -->
            <div class="admin-content">
                <div class="page-title">
                    <div>
                        <h1><?php echo $pageTitle; ?></h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="dashboard.php">Home</a></li>
                                <li class="breadcrumb-item"><a href="coaching-centers.php">Coaching Centers</a></li>
                                <li class="breadcrumb-item active" aria-current="page"><?php echo $pageTitle; ?></li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <a href="coaching-centers.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
                
                <?php echo $message; ?>
                
                <!-- Add Coaching Center Form -->
                <div class="admin-card">
                    <div class="admin-card-header">
                        <h2 class="admin-card-title">
                            <i class="fas fa-building me-2"></i> Coaching Center Information
                        </h2>
                    </div>
                    <div class="admin-card-body">
                        <form action="" method="post" enctype="multipart/form-data">
                            <div class="row">
                                <!-- Basic Information -->
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="coaching_name" class="form-label">Coaching Center Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="coaching_name" name="coaching_name" value="<?php echo isset($_POST['coaching_name']) ? htmlspecialchars($_POST['coaching_name']) : ''; ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                        <input type="email" class="form-control" id="email" name="email" value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>" required>
                                    </div>
                                    
                                    
                                    <div class="mb-3">
                                        <label for="phone" class="form-label">Phone Number <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="phone" name="phone" value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="website" class="form-label">Website</label>
                                        <input type="url" class="form-control" id="website" name="website" value="<?php echo isset($_POST['website']) ? htmlspecialchars($_POST['website']) : ''; ?>">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="established_year" class="form-label">Established Year</label>
                                        <input type="number" class="form-control" id="established_year" name="established_year" value="<?php echo isset($_POST['established_year']) ? htmlspecialchars($_POST['established_year']) : ''; ?>" min="1900" max="<?php echo date('Y'); ?>">
                                    </div>
                                </div>
                                
                                <!-- Location Information -->
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="address" class="form-label">Address <span class="text-danger">*</span></label>
                                        <textarea class="form-control" id="address" name="address" rows="3" required><?php echo isset($_POST['address']) ? htmlspecialchars($_POST['address']) : ''; ?></textarea>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="state_id" class="form-label">State <span class="text-danger">*</span></label>
                                        <select class="form-select" id="state_id" name="state_id" required>
                                            <option value="">Select State</option>
                                            <?php foreach ($states as $state): ?>
                                                <option value="<?php echo $state['state_id']; ?>" <?php echo isset($_POST['state_id']) && $_POST['state_id'] == $state['state_id'] ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($state['state_name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="city_id" class="form-label">City <span class="text-danger">*</span></label>
                                        <select class="form-select" id="city_id" name="city_id" required>
                                            <option value="">Select City</option>
                                            <?php foreach ($cities as $city): ?>
                                                <option value="<?php echo $city['city_id']; ?>" <?php echo isset($_POST['city_id']) && $_POST['city_id'] == $city['city_id'] ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($city['city_name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="location_id" class="form-label">Location</label>
                                        <select class="form-select" id="location_id" name="location_id">
                                            <option value="">Select Location</option>
                                            <?php foreach ($locations as $location): ?>
                                                <option value="<?php echo $location['location_id']; ?>" <?php echo isset($_POST['location_id']) && $_POST['location_id'] == $location['location_id'] ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($location['location_name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="logo" class="form-label">Logo</label>
                                        <input type="file" class="form-control" id="logo" name="logo" accept="image/*">
                                        <small class="text-muted">Recommended size: 200x200 pixels</small>
                                    </div>
                                </div>
                                
                                <!-- Categories and Facilities -->
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="categories" class="form-label">Categories</label>
                                        <select class="form-select select2" id="categories" name="categories[]" multiple>
                                            <?php foreach ($categories as $category): ?>
                                                <option value="<?php echo $category['category_id']; ?>" <?php echo isset($_POST['categories']) && in_array($category['category_id'], $_POST['categories']) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($category['category_name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="facilities" class="form-label">Facilities</label>
                                        <select class="form-select select2" id="facilities" name="facilities[]" multiple>
                                            <?php foreach ($facilities as $facility): ?>
                                                <option value="<?php echo $facility['facility_id']; ?>" <?php echo isset($_POST['facilities']) && in_array($facility['facility_id'], $_POST['facilities']) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($facility['facility_name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                                
                                <!-- Description -->
                                <div class="col-12">
                                    <div class="mb-3">
                                        <label for="description" class="form-label">Description</label>
                                        <textarea class="form-control summernote" id="description" name="description" rows="6"><?php echo isset($_POST['description']) ? htmlspecialchars($_POST['description']) : ''; ?></textarea>
                                    </div>
                                </div>
                                
                                <!-- Status and Featured -->
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                        <select class="form-select" id="status" name="status" required>
                                            <option value="active" <?php echo isset($_POST['status']) && $_POST['status'] === 'active' ? 'selected' : ''; ?>>Active</option>
                                            <option value="pending" <?php echo isset($_POST['status']) && $_POST['status'] === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                            <option value="inactive" <?php echo isset($_POST['status']) && $_POST['status'] === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check form-switch mt-4">
                                            <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" <?php echo isset($_POST['is_featured']) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="is_featured">Featured Coaching Center</label>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Submit Button -->
                                <div class="col-12">
                                    <hr>
                                    <div class="d-flex justify-content-end">
                                        <button type="reset" class="btn btn-secondary me-2">Reset</button>
                                        <button type="submit" class="btn btn-primary">Create Coaching Center</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- Footer -->
            <?php include 'templates/footer.php'; ?>
        </div>
    </div>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    
    <!-- Summernote JS - Try multiple sources -->
    <script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs5.min.js"></script>
    <!-- Fallback if the first one fails -->
    <script>
        if (typeof $.fn.summernote !== 'function') {
            document.write('<script src="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.18/summernote-bs5.min.js"><\/script>');
            console.log('Loading Summernote from fallback source');
        }
    </script>
    
    <!-- Admin JS -->
    <script src="assets/js/admin.js"></script>
    
    <script>
        // Debug function to log AJAX requests
        $(document).ajaxSend(function(event, jqxhr, settings) {
            console.log('AJAX Request Sent:', settings.url, settings.data);
        });
        
        $(document).ready(function() {
            console.log('Document ready - initializing components');
            
            // Initialize Select2
            try {
                $('.select2').select2({
                    theme: 'bootstrap-5'
                });
                console.log('Select2 initialized');
            } catch (e) {
                console.error('Error initializing Select2:', e);
            }
            
            // Check if Summernote is available
            if (typeof $.fn.summernote === 'function') {
                try {
                    // Initialize Summernote
                    $('.summernote').summernote({
                        height: 200,
                        toolbar: [
                            ['style', ['style']],
                            ['font', ['bold', 'underline', 'clear']],
                            ['color', ['color']],
                            ['para', ['ul', 'ol', 'paragraph']],
                            ['table', ['table']],
                            ['insert', ['link']],
                            ['view', ['fullscreen', 'codeview', 'help']]
                        ]
                    });
                    console.log('Summernote initialized');
                } catch (e) {
                    console.error('Error initializing Summernote:', e);
                }
            } else {
                console.error('Summernote library not loaded properly');
                // Create a simple textarea fallback
                $('.summernote').css('height', '200px');
            }
            
            // Separate function to handle city loading
            function loadCitiesForState(stateId) {
                console.log('Loading cities for state ID:', stateId);
                
                if (!stateId) {
                    $('#city_id').empty().append('<option value="">Select City</option>');
                    $('#location_id').empty().append('<option value="">Select Location</option>');
                    return;
                }
                
                // Show loading indicator in the dropdown
                var citySelect = $('#city_id');
                citySelect.empty().append('<option value="">Loading cities...</option>');
                
                $.ajax({
                    url: 'ajax/get-cities.php',
                    type: 'POST',
                    data: {state_id: stateId},
                    dataType: 'json',
                    cache: false, // Prevent caching
                    success: function(data) {
                        console.log('Cities received:', data);
                        
                        citySelect.empty().append('<option value="">Select City</option>');
                        
                        // Check if data is an array or if it has an error property
                        if (data && Array.isArray(data) && data.length > 0) {
                            $.each(data, function(key, value) {
                                citySelect.append('<option value="' + value.city_id + '">' + value.city_name + '</option>');
                            });
                            console.log('Added ' + data.length + ' cities to dropdown');
                        } else if (data && data.error) {
                            console.error('Server error:', data.message);
                            citySelect.append('<option value="">Error loading cities</option>');
                        } else {
                            console.log('No cities found for this state');
                            citySelect.append('<option value="">No cities available</option>');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('AJAX Error:', status, error);
                        console.log('Response:', xhr.responseText);
                        citySelect.empty().append('<option value="">Error loading cities</option>');
                    }
                });
            }
            
            // Attach change event to state dropdown
            $('#state_id').on('change', function() {
                var stateId = $(this).val();
                loadCitiesForState(stateId);
            });
            
            // If state is already selected on page load, load its cities
            var initialStateId = $('#state_id').val();
            if (initialStateId) {
                loadCitiesForState(initialStateId);
            }
            
            // Separate function to handle location loading
            function loadLocationsForCity(cityId) {
                console.log('Loading locations for city ID:', cityId);
                
                if (!cityId) {
                    $('#location_id').empty().append('<option value="">Select Location</option>');
                    return;
                }
                
                // Show loading indicator in the dropdown
                var locationSelect = $('#location_id');
                locationSelect.empty().append('<option value="">Loading locations...</option>');
                
                $.ajax({
                    url: 'ajax/get-locations.php',
                    type: 'POST',
                    data: {city_id: cityId},
                    dataType: 'json',
                    cache: false, // Prevent caching
                    success: function(data) {
                        console.log('Locations received:', data);
                        
                        locationSelect.empty().append('<option value="">Select Location</option>');
                        
                        if (data && Array.isArray(data) && data.length > 0) {
                            $.each(data, function(key, value) {
                                locationSelect.append('<option value="' + value.location_id + '">' + value.location_name + '</option>');
                            });
                            console.log('Added ' + data.length + ' locations to dropdown');
                        } else if (data && data.error) {
                            console.error('Server error:', data.message);
                            locationSelect.append('<option value="">Error loading locations</option>');
                        } else {
                            console.log('No locations found for this city');
                            locationSelect.append('<option value="">No locations available</option>');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('AJAX Error:', status, error);
                        console.log('Response:', xhr.responseText);
                        locationSelect.empty().append('<option value="">Error loading locations</option>');
                    }
                });
            }
            
            // Attach change event to city dropdown
            $('#city_id').on('change', function() {
                var cityId = $(this).val();
                loadLocationsForCity(cityId);
            });
            
            // If city is already selected on page load, load its locations
            var initialCityId = $('#city_id').val();
            if (initialCityId) {
                loadLocationsForCity(initialCityId);
            }
        });
        
        // Ensure city dropdown functionality works even if there are other JS errors
        // This is outside the document.ready to make sure it runs
        $(function() {
            console.log('Initializing city dropdown functionality directly');
            
            // Direct binding of state change event
            $('#state_id').off('change').on('change', function() {
                var stateId = $(this).val();
                console.log('State changed (direct binding):', stateId);
                
                if (!stateId) {
                    $('#city_id').empty().append('<option value="">Select City</option>');
                    return;
                }
                
                // Simple AJAX call to get cities
                $.ajax({
                    url: 'ajax/get-cities.php',
                    type: 'POST',
                    data: {state_id: stateId},
                    dataType: 'json',
                    success: function(data) {
                        var citySelect = $('#city_id');
                        citySelect.empty().append('<option value="">Select City</option>');
                        
                        if (data && Array.isArray(data) && data.length > 0) {
                            $.each(data, function(i, city) {
                                citySelect.append('<option value="' + city.city_id + '">' + city.city_name + '</option>');
                            });
                        } else {
                            citySelect.append('<option value="">No cities available</option>');
                        }
                    },
                    error: function() {
                        $('#city_id').empty().append('<option value="">Error loading cities</option>');
                    }
                });
            });
        });
    </script>
</body>
</html>