<?php
require_once 'includes/autoload.php';

// Get database instance
$db = Database::getInstance();

// Get all tables
$tables = $db->fetchAll("SHOW TABLES FROM coaching_directory");

echo "<h2>Tables in coaching_directory database:</h2>";
echo "<pre>";
print_r($tables);
echo "</pre>";

// Check if coaching_locations table exists
$coachingLocationsExists = $db->fetchRow("SHOW TABLES LIKE 'coaching_locations'");
echo "<h3>coaching_locations table exists: " . ($coachingLocationsExists ? 'Yes' : 'No') . "</h3>";

// If coaching_locations exists, show its structure
if ($coachingLocationsExists) {
    $structure = $db->fetchAll("DESCRIBE coaching_locations");
    echo "<h3>coaching_locations structure:</h3>";
    echo "<pre>";
    print_r($structure);
    echo "</pre>";
}

// Check courses table structure
$coursesStructure = $db->fetchAll("DESCRIBE courses");
echo "<h3>courses table structure:</h3>";
echo "<pre>";
print_r($coursesStructure);
echo "</pre>";