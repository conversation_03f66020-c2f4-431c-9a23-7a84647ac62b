<?php
/**
 * Subscription Class
 * Handles coaching center subscriptions
 */
class Subscription {
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Get all subscription plans
     * @param bool $activeOnly Get only active plans
     * @return array Subscription plans
     */
    public function getPlans($activeOnly = true) {
        $sql = "SELECT * FROM subscription_plans";
        
        if ($activeOnly) {
            $sql .= " WHERE status = 'active'";
        }
        
        $sql .= " ORDER BY price ASC";
        
        return $this->db->fetchAll($sql);
    }
    
    /**
     * Get subscription plan by ID
     * @param int $planId Plan ID
     * @return array|null Subscription plan
     */
    public function getPlanById($planId) {
        return $this->db->fetchRow(
            "SELECT * FROM subscription_plans WHERE plan_id = ?",
            [$planId]
        );
    }
    
    /**
     * Get current subscription for coaching center
     * @param int $coachingId Coaching center ID
     * @return array|null Subscription data
     */
    public function getCurrentByCoaching($coachingId) {
        return $this->db->fetchRow(
            "SELECT s.*, p.plan_name, p.description, p.features 
             FROM coaching_subscriptions s
             JOIN subscription_plans p ON s.plan_id = p.plan_id
             WHERE s.coaching_id = ? AND s.is_active = 1 AND s.end_date >= CURDATE()
             ORDER BY s.end_date DESC
             LIMIT 1",
            [$coachingId]
        );
    }
    
    /**
     * Get all subscriptions for coaching center
     * @param int $coachingId Coaching center ID
     * @return array Subscriptions
     */
    public function getAllByCoaching($coachingId) {
        return $this->db->fetchAll(
            "SELECT s.*, p.plan_name 
             FROM coaching_subscriptions s
             JOIN subscription_plans p ON s.plan_id = p.plan_id
             WHERE s.coaching_id = ?
             ORDER BY s.end_date DESC",
            [$coachingId]
        );
    }
    
    /**
     * Add a new subscription
     * @param array $data Subscription data
     * @return int|bool Subscription ID or false on failure
     */
    public function add($data) {
        // Start transaction
        $this->db->beginTransaction();
        
        try {
            // Insert subscription
            $subscriptionId = $this->db->insert('coaching_subscriptions', $data);
            
            if (!$subscriptionId) {
                throw new Exception('Failed to insert subscription');
            }
            
            // Update coaching center featured status
            if ($data['is_active']) {
                $this->db->update(
                    'coaching_centers',
                    ['is_featured' => 1],
                    'coaching_id = ?',
                    [$data['coaching_id']]
                );
            }
            
            // Commit transaction
            $this->db->commit();
            
            return $subscriptionId;
        } catch (Exception $e) {
            // Rollback transaction
            $this->db->rollback();
            return false;
        }
    }
    
    /**
     * Update subscription
     * @param int $subscriptionId Subscription ID
     * @param array $data Subscription data
     * @return bool True if update successful
     */
    public function update($subscriptionId, $data) {
        // Start transaction
        $this->db->beginTransaction();
        
        try {
            // Get subscription
            $subscription = $this->getById($subscriptionId);
            
            if (!$subscription) {
                throw new Exception('Subscription not found');
            }
            
            // Update subscription
            $updated = $this->db->update(
                'coaching_subscriptions',
                $data,
                'subscription_id = ?',
                [$subscriptionId]
            );
            
            if (!$updated) {
                throw new Exception('Failed to update subscription');
            }
            
            // Update coaching center featured status if active status changed
            if (isset($data['is_active'])) {
                $this->updateCoachingFeaturedStatus($subscription['coaching_id']);
            }
            
            // Commit transaction
            $this->db->commit();
            
            return true;
        } catch (Exception $e) {
            // Rollback transaction
            $this->db->rollback();
            return false;
        }
    }
    
    /**
     * Get subscription by ID
     * @param int $subscriptionId Subscription ID
     * @return array|null Subscription data
     */
    public function getById($subscriptionId) {
        return $this->db->fetchRow(
            "SELECT s.*, p.plan_name, p.description, p.features 
             FROM coaching_subscriptions s
             JOIN subscription_plans p ON s.plan_id = p.plan_id
             WHERE s.subscription_id = ?",
            [$subscriptionId]
        );
    }
    
    /**
     * Cancel subscription
     * @param int $subscriptionId Subscription ID
     * @return bool True if cancellation successful
     */
    public function cancel($subscriptionId) {
        // Start transaction
        $this->db->beginTransaction();
        
        try {
            // Get subscription
            $subscription = $this->getById($subscriptionId);
            
            if (!$subscription) {
                throw new Exception('Subscription not found');
            }
            
            // Update subscription
            $updated = $this->db->update(
                'coaching_subscriptions',
                ['is_active' => 0],
                'subscription_id = ?',
                [$subscriptionId]
            );
            
            if (!$updated) {
                throw new Exception('Failed to cancel subscription');
            }
            
            // Update coaching center featured status
            $this->updateCoachingFeaturedStatus($subscription['coaching_id']);
            
            // Commit transaction
            $this->db->commit();
            
            return true;
        } catch (Exception $e) {
            // Rollback transaction
            $this->db->rollback();
            return false;
        }
    }
    
    /**
     * Update coaching center featured status
     * @param int $coachingId Coaching center ID
     * @return bool True if update successful
     */
    private function updateCoachingFeaturedStatus($coachingId) {
        // Check if coaching center has any active subscription
        $activeSubscription = $this->getCurrentByCoaching($coachingId);
        
        // Update coaching center featured status
        return $this->db->update(
            'coaching_centers',
            ['is_featured' => !empty($activeSubscription)],
            'coaching_id = ?',
            [$coachingId]
        );
    }
    
    /**
     * Check for expired subscriptions and update coaching centers
     * @return bool True if update successful
     */
    public function checkExpiredSubscriptions() {
        // Start transaction
        $this->db->beginTransaction();
        
        try {
            // Get expired subscriptions
            $expiredSubscriptions = $this->db->fetchAll(
                "SELECT * FROM coaching_subscriptions 
                 WHERE is_active = 1 AND end_date < CURDATE()"
            );
            
            // Update expired subscriptions
            foreach ($expiredSubscriptions as $subscription) {
                // Update subscription
                $this->db->update(
                    'coaching_subscriptions',
                    ['is_active' => 0],
                    'subscription_id = ?',
                    [$subscription['subscription_id']]
                );
                
                // Update coaching center featured status
                $this->updateCoachingFeaturedStatus($subscription['coaching_id']);
            }
            
            // Commit transaction
            $this->db->commit();
            
            return true;
        } catch (Exception $e) {
            // Rollback transaction
            $this->db->rollback();
            return false;
        }
    }
}