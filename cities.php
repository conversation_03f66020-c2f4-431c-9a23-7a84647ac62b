<?php
/**
 * Cities Page
 * Shows all cities
 */
require_once 'includes/autoload.php';

// Get settings
$settings = Settings::getInstance();

// Get all cities from database
$locationObj = new Location();
$cities = $locationObj->getAllCities();

// Page title and meta
$pageTitle = 'All Cities';
$pageDescription = 'Browse coaching centers by city on ' . $settings->getSiteName();
$pageKeywords = 'coaching cities, education by city, ' . $settings->getMetaKeywords();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?php echo getMetaTags($pageTitle . ' - ' . $settings->getSiteName(), $pageDescription, $pageKeywords); ?>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo getAssetUrl($settings->getSiteFavicon()); ?>" type="image/x-icon">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo getAssetUrl('css/style.css'); ?>">
</head>
<body>
    <!-- Header -->
    <?php include 'templates/header.php'; ?>
    
    <!-- Main Content -->
    <main>
        <!-- Page Header -->
        <section class="page-header">
            <div class="container">
                <h1><?php echo $pageTitle; ?></h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="index.php">Home</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Cities</li>
                    </ol>
                </nav>
            </div>
        </section>
        
        <!-- Cities Section -->
        <section class="cities-section">
            <div class="container">
                <div class="row">
                    <?php foreach ($cities as $city): ?>
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                            <div class="city-card">
                                <div class="city-image">
                                    <img src="<?php echo isset($city['image']) ? getUploadUrl($city['image']) : getAssetUrl('images/dummy/city.jpg'); ?>" alt="<?php echo $city['city_name']; ?>">
                                </div>
                                <div class="city-content">
                                    <h3><?php echo $city['city_name']; ?></h3>
                                    <p><?php echo $city['coaching_count']; ?> Coaching Centers</p>
                                    <a href="<?php echo getCityUrl($city['slug']); ?>" class="btn btn-outline-primary btn-sm">View All</a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
    </main>
    
    <!-- Footer -->
    <?php include 'templates/footer.php'; ?>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="<?php echo getAssetUrl('js/main.js'); ?>"></script>
</body>
</html>