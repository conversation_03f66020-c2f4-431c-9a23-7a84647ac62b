<?php
/**
 * SitemapGenerator Class
 * Generates XML sitemap for the website
 */
class SitemapGenerator {
    private $db;
    private $baseUrl;
    private $sitemapPath;
    private $changeFrequency;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = Database::getInstance();
        $this->baseUrl = getBaseUrl();
        $this->sitemapPath = ROOT_DIR . '/sitemap.xml';
        
        // Get sitemap frequency from settings
        $settings = Settings::getInstance();
        $this->changeFrequency = $settings->getSetting('sitemap_frequency', 'weekly');
    }
    
    /**
     * Generate sitemap
     * @return bool True if sitemap generated successfully
     */
    public function generate() {
        try {
            // Create XML document
            $xml = new DOMDocument('1.0', 'UTF-8');
            $xml->formatOutput = true;
            
            // Create urlset element
            $urlset = $xml->createElement('urlset');
            $urlset->setAttribute('xmlns', 'http://www.sitemaps.org/schemas/sitemap/0.9');
            $xml->appendChild($urlset);
            
            // Add static pages
            $this->addStaticPages($xml, $urlset);
            
            // Add coaching centers
            $this->addCoachingCenters($xml, $urlset);
            
            // Add courses
            $this->addCourses($xml, $urlset);
            
            // Add blog posts
            $this->addBlogPosts($xml, $urlset);
            
            // Add locations
            $this->addLocations($xml, $urlset);
            
            // Add categories
            $this->addCategories($xml, $urlset);
            
            // Save sitemap
            $xml->save($this->sitemapPath);
            
            return true;
        } catch (Exception $e) {
            error_log('Sitemap generation error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Add static pages to sitemap
     * @param DOMDocument $xml XML document
     * @param DOMElement $urlset Urlset element
     */
    private function addStaticPages($xml, $urlset) {
        $staticPages = [
            ['url' => '', 'priority' => '1.0', 'lastmod' => date('Y-m-d')],
            ['url' => 'about-us', 'priority' => '0.8', 'lastmod' => date('Y-m-d')],
            ['url' => 'contact-us', 'priority' => '0.8', 'lastmod' => date('Y-m-d')],
            ['url' => 'coaching-centers', 'priority' => '0.9', 'lastmod' => date('Y-m-d')],
            ['url' => 'courses', 'priority' => '0.9', 'lastmod' => date('Y-m-d')],
            ['url' => 'blog', 'priority' => '0.7', 'lastmod' => date('Y-m-d')],
            ['url' => 'faq', 'priority' => '0.6', 'lastmod' => date('Y-m-d')],
            ['url' => 'terms-of-service', 'priority' => '0.5', 'lastmod' => date('Y-m-d')],
            ['url' => 'privacy-policy', 'priority' => '0.5', 'lastmod' => date('Y-m-d')],
            ['url' => 'register', 'priority' => '0.6', 'lastmod' => date('Y-m-d')],
            ['url' => 'login', 'priority' => '0.6', 'lastmod' => date('Y-m-d')]
        ];
        
        foreach ($staticPages as $page) {
            $this->addUrl($xml, $urlset, $this->baseUrl . $page['url'], $page['lastmod'], $this->changeFrequency, $page['priority']);
        }
    }
    
    /**
     * Add coaching centers to sitemap
     * @param DOMDocument $xml XML document
     * @param DOMElement $urlset Urlset element
     */
    private function addCoachingCenters($xml, $urlset) {
        $coachingCenters = $this->db->fetchAll(
            "SELECT slug, updated_at FROM coaching_centers WHERE status = 'active'"
        );
        
        foreach ($coachingCenters as $center) {
            $lastmod = date('Y-m-d', strtotime($center['updated_at']));
            $this->addUrl($xml, $urlset, $this->baseUrl . 'coaching/' . $center['slug'], $lastmod, $this->changeFrequency, '0.8');
        }
    }
    
    /**
     * Add courses to sitemap
     * @param DOMDocument $xml XML document
     * @param DOMElement $urlset Urlset element
     */
    private function addCourses($xml, $urlset) {
        $courses = $this->db->fetchAll(
            "SELECT slug, updated_at FROM courses WHERE status = 'active'"
        );
        
        foreach ($courses as $course) {
            $lastmod = date('Y-m-d', strtotime($course['updated_at']));
            $this->addUrl($xml, $urlset, $this->baseUrl . 'course/' . $course['slug'], $lastmod, $this->changeFrequency, '0.7');
        }
    }
    
    /**
     * Add blog posts to sitemap
     * @param DOMDocument $xml XML document
     * @param DOMElement $urlset Urlset element
     */
    private function addBlogPosts($xml, $urlset) {
        $blogPosts = $this->db->fetchAll(
            "SELECT slug, updated_at FROM blog_posts WHERE status = 'published'"
        );
        
        foreach ($blogPosts as $post) {
            $lastmod = date('Y-m-d', strtotime($post['updated_at']));
            $this->addUrl($xml, $urlset, $this->baseUrl . 'blog/' . $post['slug'], $lastmod, $this->changeFrequency, '0.6');
        }
    }
    
    /**
     * Add locations to sitemap
     * @param DOMDocument $xml XML document
     * @param DOMElement $urlset Urlset element
     */
    private function addLocations($xml, $urlset) {
        // Add states
        $states = $this->db->fetchAll(
            "SELECT state_slug FROM states WHERE status = 'active'"
        );
        
        foreach ($states as $state) {
            $this->addUrl($xml, $urlset, $this->baseUrl . 'coaching-centers/' . $state['state_slug'], date('Y-m-d'), $this->changeFrequency, '0.7');
        }
        
        // Add cities
        $cities = $this->db->fetchAll(
            "SELECT c.city_slug, s.state_slug 
             FROM cities c
             JOIN states s ON c.state_id = s.state_id
             WHERE c.status = 'active'"
        );
        
        foreach ($cities as $city) {
            $this->addUrl($xml, $urlset, $this->baseUrl . 'coaching-centers/' . $city['state_slug'] . '/' . $city['city_slug'], date('Y-m-d'), $this->changeFrequency, '0.7');
        }
    }
    
    /**
     * Add categories to sitemap
     * @param DOMDocument $xml XML document
     * @param DOMElement $urlset Urlset element
     */
    private function addCategories($xml, $urlset) {
        $categories = $this->db->fetchAll(
            "SELECT category_slug FROM coaching_categories WHERE status = 'active'"
        );
        
        foreach ($categories as $category) {
            $this->addUrl($xml, $urlset, $this->baseUrl . 'category/' . $category['category_slug'], date('Y-m-d'), $this->changeFrequency, '0.7');
        }
    }
    
    /**
     * Add URL to sitemap
     * @param DOMDocument $xml XML document
     * @param DOMElement $urlset Urlset element
     * @param string $loc URL location
     * @param string $lastmod Last modified date (YYYY-MM-DD)
     * @param string $changefreq Change frequency
     * @param string $priority Priority
     */
    private function addUrl($xml, $urlset, $loc, $lastmod, $changefreq, $priority) {
        $url = $xml->createElement('url');
        
        $locElement = $xml->createElement('loc', $loc);
        $url->appendChild($locElement);
        
        $lastmodElement = $xml->createElement('lastmod', $lastmod);
        $url->appendChild($lastmodElement);
        
        $changefreqElement = $xml->createElement('changefreq', $changefreq);
        $url->appendChild($changefreqElement);
        
        $priorityElement = $xml->createElement('priority', $priority);
        $url->appendChild($priorityElement);
        
        $urlset->appendChild($url);
    }
}