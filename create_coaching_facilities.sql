-- Create coaching_facilities junction table if it doesn't exist
CREATE TABLE IF NOT EXISTS `coaching_facilities` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `coaching_id` int(11) NOT NULL,
  `facility_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  <PERSON><PERSON><PERSON> `coaching_id` (`coaching_id`),
  <PERSON><PERSON><PERSON> `facility_id` (`facility_id`),
  CONSTRAINT `coaching_facilities_ibfk_1` FOREIGN KEY (`coaching_id`) REFERENCES `coaching_centers` (`coaching_id`) ON DELETE CASCADE,
  CONSTRAINT `coaching_facilities_ibfk_2` FOREIGN KEY (`facility_id`) REFERENCES `facilities` (`facility_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;