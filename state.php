<?php
/**
 * State Page
 * Shows coaching centers in a specific state
 */
require_once 'includes/autoload.php';

// Get settings
$settings = Settings::getInstance();

// Get state ID and slug from URL
$stateId = isset($_GET['state_id']) ? intval($_GET['state_id']) : 0;
$slug = isset($_GET['slug']) ? $_GET['slug'] : '';

if (empty($stateId) || empty($slug)) {
    redirect('index.php');
}

// Get state details from database
$locationObj = new Location();
$state = $locationObj->getStateById($stateId);

// If state not found, redirect to home page
if (empty($state)) {
    redirect('index.php');
}

// Get cities in this state from database
$cities = $locationObj->getCitiesByState($stateId);

// Get coaching centers in this state
$coachingObj = new CoachingCenter();
$coachings = $coachingObj->getByState($stateId);

// If no coaching centers found, use dummy data
if (empty($coachings)) {
    $coachings = getDummyCoachingCenters(10);
}

// Page title and meta
$pageTitle = 'Coaching Centers in ' . $state['state_name'];
$pageDescription = 'Find the best coaching centers in ' . $state['state_name'] . ' on ' . $settings->getSiteName();
$pageKeywords = 'coaching centers in ' . $state['state_name'] . ', education in ' . $state['state_name'] . ', ' . $settings->getMetaKeywords();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?php echo getMetaTags($pageTitle . ' - ' . $settings->getSiteName(), $pageDescription, $pageKeywords); ?>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo getAssetUrl($settings->getSiteFavicon()); ?>" type="image/x-icon">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo getAssetUrl('css/style.css'); ?>">
</head>
<body>
    <!-- Header -->
    <?php include 'templates/header.php'; ?>
    
    <!-- Main Content -->
    <main>
        <!-- Page Header -->
        <section class="page-header">
            <div class="container">
                <h1><?php echo $pageTitle; ?></h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="index.php">Home</a></li>
                        <li class="breadcrumb-item active" aria-current="page"><?php echo $state['state_name']; ?></li>
                    </ol>
                </nav>
            </div>
        </section>
        
        <!-- State Description -->
        <section class="state-description">
            <div class="container">
                <div class="row">
                    <div class="col-md-12">
                        <div class="state-info">
                            <div class="state-image">
                                <img src="<?php echo isset($state['image']) ? getUploadUrl($state['image']) : getAssetUrl('images/dummy/state.jpg'); ?>" alt="<?php echo $state['state_name']; ?>">
                            </div>
                            <div class="state-content">
                                <h2><?php echo $state['state_name']; ?></h2>
                                <p><?php echo !empty($state['description']) ? $state['description'] : 'Find the best coaching centers in ' . $state['state_name'] . '.'; ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Cities in State Section -->
        <section class="cities-section">
            <div class="container">
                <div class="row">
                    <div class="col-md-12">
                        <h2>Cities in <?php echo $state['state_name']; ?></h2>
                    </div>
                </div>
                
                <div class="row">
                    <?php foreach ($cities as $city): ?>
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                            <div class="city-card">
                                <div class="city-image">
                                    <img src="<?php echo isset($city['image']) ? getUploadUrl($city['image']) : getAssetUrl('images/dummy/city.jpg'); ?>" alt="<?php echo $city['city_name']; ?>">
                                </div>
                                <div class="city-content">
                                    <h3><?php echo $city['city_name']; ?></h3>
                                    <p><?php echo $city['coaching_count']; ?> Coaching Centers</p>
                                    <a href="<?php echo getCityUrl($city['slug']); ?>" class="btn btn-outline-primary btn-sm">View All</a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
        
        <!-- Coaching Centers Section -->
        <section class="coaching-centers-section">
            <div class="container">
                <div class="row">
                    <div class="col-md-12">
                        <h2>Top Coaching Centers in <?php echo $state['state_name']; ?></h2>
                    </div>
                </div>
                
                <div class="coaching-list">
                    <?php foreach ($coachings as $coaching): ?>
                        <div class="coaching-list-item">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="coaching-image">
                                        <img src="<?php echo isset($coaching['logo']) ? getUploadUrl($coaching['logo']) : getAssetUrl('images/default-logo.png'); ?>" alt="<?php echo $coaching['coaching_name']; ?>">
                                        <?php if ($coaching['is_featured']): ?>
                                            <span class="featured-badge"><i class="fas fa-star"></i> Featured</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="coaching-content">
                                        <h3><a href="<?php echo getCoachingUrl($coaching['slug']); ?>"><?php echo $coaching['coaching_name']; ?></a></h3>
                                        <div class="coaching-meta">
                                            <span><i class="fas fa-map-marker-alt"></i> <?php echo $coaching['city_name']; ?>, <?php echo $coaching['state_name']; ?></span>
                                            <span><i class="fas fa-star"></i> <?php echo number_format($coaching['avg_rating'], 1); ?> (<?php echo $coaching['review_count']; ?> reviews)</span>
                                        </div>
                                        <div class="coaching-categories">
                                            <?php foreach ($coaching['categories'] as $category): ?>
                                                <a href="<?php echo getCategoryUrl($category['category_slug']); ?>" class="category-badge"><?php echo $category['category_name']; ?></a>
                                            <?php endforeach; ?>
                                        </div>
                                        <p class="coaching-description"><?php echo substr($coaching['description'], 0, 150); ?>...</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="coaching-actions">
                                        <div class="coaching-contact">
                                            <p><i class="fas fa-phone"></i> <?php echo $coaching['phone']; ?></p>
                                            <p><i class="fas fa-envelope"></i> <?php echo $coaching['email']; ?></p>
                                        </div>
                                        <a href="<?php echo getCoachingUrl($coaching['slug']); ?>" class="btn btn-primary btn-sm">View Details</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
        
        <!-- Popular Categories in State Section -->
        <section class="popular-categories-section">
            <div class="container">
                <div class="row">
                    <div class="col-md-12">
                        <h2>Popular Categories in <?php echo $state['state_name']; ?></h2>
                    </div>
                </div>
                
                <div class="row">
                    <?php 
                    $categoryObj = new Category();
                    $categories = $categoryObj->getPopular(6);
                    
                    if (empty($categories)) {
                        $categories = getDummyCategories(6);
                    }
                    ?>
                    
                    <?php foreach ($categories as $category): ?>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <a href="<?php echo getCategoryUrl($category['slug']); ?>" class="category-card hover-scale">
                                <div class="category-icon">
                                    <i class="<?php echo !empty($category['icon']) ? $category['icon'] : 'fas fa-book'; ?>"></i>
                                </div>
                                <h3><?php echo $category['category_name']; ?></h3>
                                <p><?php echo $category['coaching_count']; ?> Coaching Centers</p>
                            </a>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
    </main>
    
    <!-- Footer -->
    <?php include 'templates/footer.php'; ?>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="<?php echo getAssetUrl('js/main.js'); ?>"></script>
</body>
</html>