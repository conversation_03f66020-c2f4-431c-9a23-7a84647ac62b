<?php
/**
 * City Page
 * Shows coaching centers in a specific city
 */
require_once 'includes/autoload.php';

// Get settings
$settings = Settings::getInstance();

// Get city slug from URL
$slug = isset($_GET['slug']) ? $_GET['slug'] : '';

if (empty($slug)) {
    redirect('cities.php');
}

// Get city details from database
$locationObj = new Location();
$city = $locationObj->getCityBySlug($slug);

// If city not found, redirect to cities page
if (empty($city)) {
    redirect('cities.php');
}

// Get coaching centers in this city
$coachingObj = new CoachingCenter();
$coachingResult = $coachingObj->getByCity($city['city_id']);
$coachings = $coachingResult['coachings'] ?? [];

// If no coaching centers found, use dummy data
if (empty($coachings)) {
    $coachings = getDummyCoachingCenters(10);
}

// Page title and meta
$pageTitle = 'Coaching Centers in ' . $city['city_name'];
$pageDescription = 'Find the best coaching centers in ' . $city['city_name'] . ', ' . $city['state_name'] . ' on ' . $settings->getSiteName();
$pageKeywords = 'coaching centers in ' . $city['city_name'] . ', education in ' . $city['city_name'] . ', ' . $settings->getMetaKeywords();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?php echo getMetaTags($pageTitle . ' - ' . $settings->getSiteName(), $pageDescription, $pageKeywords); ?>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo getAssetUrl($settings->getSiteFavicon()); ?>" type="image/x-icon">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo getAssetUrl('css/style.css'); ?>">
    
    <style>
    /* Additional CSS for city page */
    .page-header {
        background-color: var(--dark-blue);
        color: var(--white);
        padding: var(--spacing-xl) 0;
        margin-bottom: var(--spacing-xl);
    }
    
    .page-header h1 {
        color: var(--white);
        margin-bottom: var(--spacing-sm);
    }
    
    .breadcrumb-item a {
        color: var(--light-blue);
    }
    
    .breadcrumb-item.active {
        color: var(--light-text);
    }
    
    .city-description {
        background-color: var(--light-gray);
        padding: var(--spacing-xl) 0;
        margin-bottom: var(--spacing-xl);
    }
    
    .city-info {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-lg);
    }
    
    @media (min-width: 768px) {
        .city-info {
            flex-direction: row;
            align-items: center;
        }
    }
    
    .city-image {
        flex: 0 0 300px;
        border-radius: var(--border-radius-lg);
        overflow: hidden;
        box-shadow: var(--box-shadow);
    }
    
    .city-image img {
        width: 100%;
        height: 250px;
        object-fit: cover;
    }
    
    .city-content {
        flex: 1;
    }
    
    .city-content h2 {
        color: var(--dark-blue);
        margin-bottom: var(--spacing-sm);
    }
    
    .coaching-centers-section {
        padding: var(--spacing-xl) 0;
    }
    
    .coaching-centers-section h2 {
        margin-bottom: var(--spacing-xl);
        color: var(--dark-blue);
        text-align: center;
    }
    
    .coaching-list {
        margin-top: var(--spacing-xl);
    }
    
    .coaching-list-item {
        background-color: var(--white);
        border-radius: var(--border-radius-md);
        box-shadow: var(--box-shadow);
        padding: var(--spacing-lg);
        margin-bottom: var(--spacing-lg);
        transition: all 0.3s ease;
    }
    
    .coaching-list-item:hover {
        transform: translateY(-5px);
        box-shadow: var(--box-shadow-hover);
    }
    
    .coaching-image {
        position: relative;
        border-radius: var(--border-radius-sm);
        overflow: hidden;
        margin-bottom: var(--spacing-sm);
        border: 1px solid var(--light-gray);
        padding: 5px;
        height: 120px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .coaching-image img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }
    
    .featured-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        background-color: var(--warning);
        color: var(--dark-text);
        font-size: 12px;
        padding: 3px 8px;
        border-radius: var(--border-radius-sm);
        font-weight: 600;
    }
    
    .coaching-content h3 {
        font-size: 20px;
        margin-bottom: var(--spacing-sm);
    }
    
    .coaching-content h3 a {
        color: var(--dark-blue);
        text-decoration: none;
        transition: color 0.3s ease;
    }
    
    .coaching-content h3 a:hover {
        color: var(--light-blue);
    }
    
    .coaching-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        margin-bottom: var(--spacing-sm);
        font-size: 14px;
        color: var(--dark-gray);
    }
    
    .coaching-meta i {
        color: var(--light-blue);
        margin-right: 5px;
    }
    
    .coaching-categories {
        margin-bottom: var(--spacing-sm);
    }
    
    .category-badge {
        display: inline-block;
        background-color: var(--light-gray);
        color: var(--dark-blue);
        padding: 3px 10px;
        border-radius: 20px;
        font-size: 12px;
        margin-right: 5px;
        margin-bottom: 5px;
        transition: all 0.3s ease;
    }
    
    .category-badge:hover {
        background-color: var(--light-blue);
        color: var(--white);
    }
    
    .coaching-description {
        font-size: 14px;
        color: var(--dark-gray);
        margin-bottom: var(--spacing-sm);
    }
    
    .coaching-actions {
        display: flex;
        flex-direction: column;
        height: 100%;
        justify-content: space-between;
    }
    
    .coaching-contact {
        margin-bottom: var(--spacing-md);
    }
    
    .coaching-contact p {
        margin-bottom: 5px;
        font-size: 14px;
    }
    
    .coaching-contact i {
        color: var(--light-blue);
        width: 20px;
        text-align: center;
        margin-right: 5px;
    }
    
    .popular-categories-section {
        background-color: var(--light-gray);
        padding: var(--spacing-xl) 0;
    }
    
    .popular-categories-section h2 {
        margin-bottom: var(--spacing-xl);
        color: var(--dark-blue);
        text-align: center;
    }
    
    .category-card {
        background-color: var(--white);
        border-radius: var(--border-radius-md);
        box-shadow: var(--box-shadow);
        padding: var(--spacing-lg);
        height: 100%;
        text-align: center;
        transition: all 0.3s ease;
        text-decoration: none;
        display: block;
    }
    
    .category-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--box-shadow-hover);
    }
    
    .category-icon {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        background-color: var(--light-blue);
        color: var(--white);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto var(--spacing-md);
        font-size: 28px;
    }
    
    .category-card h3 {
        color: var(--dark-blue);
        font-size: 18px;
        margin-bottom: var(--spacing-sm);
    }
    
    .category-card p {
        color: var(--dark-gray);
        font-size: 14px;
        margin-bottom: 0;
    }
    
    .hover-scale:hover {
        transform: translateY(-5px) scale(1.02);
    }
    </style>
</head>
<body>
    <!-- Header -->
    <?php include 'templates/header.php'; ?>
    
    <!-- Main Content -->
    <main>
        <!-- Page Header -->
        <section class="page-header">
            <div class="container">
                <h1><?php echo $pageTitle; ?></h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="index.php">Home</a></li>
                        <li class="breadcrumb-item"><a href="cities.php">Cities</a></li>
                        <li class="breadcrumb-item active" aria-current="page"><?php echo $city['city_name']; ?></li>
                    </ol>
                </nav>
            </div>
        </section>
        
        <!-- City Description -->
        <section class="city-description">
            <div class="container">
                <div class="row">
                    <div class="col-md-12">
                        <div class="city-info">
                            <div class="city-image">
                                <img src="<?php echo isset($city['image']) ? getUploadUrl($city['image']) : getAssetUrl('images/dummy/city.jpg'); ?>" alt="<?php echo $city['city_name']; ?>">
                            </div>
                            <div class="city-content">
                                <h2><?php echo $city['city_name']; ?>, <?php echo $city['state_name']; ?></h2>
                                <p><?php echo !empty($city['description']) ? $city['description'] : 'Find the best coaching centers in ' . $city['city_name'] . ', ' . $city['state_name'] . '.'; ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Coaching Centers Section -->
        <section class="coaching-centers-section">
            <div class="container">
                <div class="row">
                    <div class="col-md-12">
                        <h2>Coaching Centers in <?php echo $city['city_name']; ?></h2>
                    </div>
                </div>
                
                <div class="coaching-list">
                    <?php foreach ($coachings as $coaching): ?>
                        <div class="coaching-list-item">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="coaching-image">
                                        <img src="<?php echo isset($coaching['logo']) ? getUploadUrl($coaching['logo']) : getAssetUrl('images/default-logo.png'); ?>" alt="<?php echo htmlspecialchars($coaching['coaching_name']); ?>">
                                        <?php if (isset($coaching['is_featured']) && $coaching['is_featured']): ?>
                                            <span class="featured-badge"><i class="fas fa-star"></i> Featured</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="coaching-content">
                                        <h3><a href="<?php echo getCoachingUrl($coaching['slug']); ?>"><?php echo htmlspecialchars($coaching['coaching_name']); ?></a></h3>
                                        <div class="coaching-meta">
                                            <span><i class="fas fa-map-marker-alt"></i> <?php echo isset($coaching['city_name']) ? htmlspecialchars($coaching['city_name']) : htmlspecialchars($city['city_name']); ?>, <?php echo isset($coaching['state_name']) ? htmlspecialchars($coaching['state_name']) : htmlspecialchars($city['state_name']); ?></span>
                                            <span><i class="fas fa-star"></i> <?php echo isset($coaching['avg_rating']) ? number_format($coaching['avg_rating'], 1) : '0.0'; ?> (<?php echo isset($coaching['review_count']) ? $coaching['review_count'] : '0'; ?> reviews)</span>
                                        </div>
                                        <div class="coaching-categories">
                                            <?php if (isset($coaching['categories']) && is_array($coaching['categories'])): ?>
                                                <?php foreach ($coaching['categories'] as $category): ?>
                                                    <a href="<?php echo getCategoryUrl(isset($category['category_slug']) ? $category['category_slug'] : (isset($category['slug']) ? $category['slug'] : '')); ?>" class="category-badge"><?php echo htmlspecialchars($category['category_name']); ?></a>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </div>
                                        <p class="coaching-description"><?php echo isset($coaching['description']) ? htmlspecialchars(substr($coaching['description'], 0, 150)) . '...' : 'No description available.'; ?></p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="coaching-actions">
                                        <div class="coaching-contact">
                                            <p><i class="fas fa-phone"></i> <?php echo isset($coaching['phone']) ? htmlspecialchars($coaching['phone']) : 'N/A'; ?></p>
                                            <p><i class="fas fa-envelope"></i> <?php echo isset($coaching['email']) ? htmlspecialchars($coaching['email']) : 'N/A'; ?></p>
                                        </div>
                                        <a href="<?php echo getCoachingUrl($coaching['slug']); ?>" class="btn btn-primary btn-sm">View Details</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
        
        <!-- Popular Categories in City Section -->
        <section class="popular-categories-section">
            <div class="container">
                <div class="row">
                    <div class="col-md-12">
                        <h2>Popular Categories in <?php echo $city['city_name']; ?></h2>
                    </div>
                </div>
                
                <div class="row">
                    <?php 
                    $categoryObj = new Category();
                    $categories = $categoryObj->getPopular(6);
                    
                    if (empty($categories)) {
                        $categories = getDummyCategories(6);
                    }
                    
                    // Define background colors for category icons
                    $bgColors = ['bg-primary', 'bg-success', 'bg-info', 'bg-warning', 'bg-danger', 'bg-secondary'];
                    ?>
                    
                    <?php foreach ($categories as $index => $category): ?>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <a href="<?php echo getCategoryUrl(isset($category['slug']) ? $category['slug'] : ''); ?>" class="category-card hover-scale">
                                <div class="category-icon <?php echo $bgColors[$index % count($bgColors)]; ?>">
                                    <i class="<?php echo !empty($category['icon']) ? $category['icon'] : 'fas fa-book'; ?>"></i>
                                </div>
                                <h3><?php echo htmlspecialchars($category['category_name']); ?></h3>
                                <p><?php echo isset($category['coaching_count']) ? $category['coaching_count'] : '0'; ?> Coaching Centers</p>
                            </a>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
    </main>
    
    <!-- Footer -->
    <?php include 'templates/footer.php'; ?>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="<?php echo getAssetUrl('js/main.js'); ?>"></script>
</body>
</html>