<?php
require_once '../includes/autoload.php';
Auth::requireCoachingOwner();

$coachingId = $_SESSION['coaching_id'];
$coachingObj = new CoachingCenter();
$message = '';

// Handle image upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['upload_image'])) {
    if (!empty($_FILES['image']['name'])) {
        $uploadDir = '../uploads/coaching_gallery/';
        if (!is_dir($uploadDir)) mkdir($uploadDir, 0755, true);
        $fileName = 'gallery_' . $coachingId . '_' . time() . '_' . basename($_FILES['image']['name']);
        $targetFile = $uploadDir . $fileName;
        if (move_uploaded_file($_FILES['image']['tmp_name'], $targetFile)) {
            $caption = trim($_POST['caption'] ?? '');
            Database::getInstance()->insert('coaching_images', [
                'coaching_id' => $coachingId,
                'image_path' => 'coaching_gallery/' . $fileName,
                'caption' => $caption,
                'display_order' => 0
            ]);
            $message = '<div class="alert alert-success">Image uploaded.</div>';
        } else {
            $message = '<div class="alert alert-danger">Failed to upload image.</div>';
        }
    }
}

// Handle delete
if (isset($_GET['action'], $_GET['image_id']) && $_GET['action'] === 'delete') {
    $imageId = (int)$_GET['image_id'];
    $image = Database::getInstance()->fetchRow('SELECT * FROM coaching_images WHERE image_id = ? AND coaching_id = ?', [$imageId, $coachingId]);
    if ($image) {
        $deleted = Database::getInstance()->delete('coaching_images', 'image_id = ? AND coaching_id = ?', [$imageId, $coachingId]);
        if ($deleted && !empty($image['image_path']) && file_exists('../' . $image['image_path'])) {
            unlink('../' . $image['image_path']);
        }
        $message = $deleted ? '<div class="alert alert-success">Image deleted.</div>' : '<div class="alert alert-danger">Failed to delete image.</div>';
    }
}

// Fetch all gallery images for this coaching center
$images = Database::getInstance()->fetchAll('SELECT * FROM coaching_images WHERE coaching_id = ? ORDER BY display_order ASC, image_id DESC', [$coachingId]);
$pageTitle = 'Gallery';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Coaching Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="<?php echo getAssetUrl('css/admin.css'); ?>">
    <style>
        .gallery-img { max-width: 100%; max-height: 180px; object-fit: cover; border-radius: 6px; }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <?php include 'templates/sidebar.php'; ?>
        <div class="main-content">
            <?php include 'templates/header.php'; ?>
            <div class="container-fluid">
                <div class="row mb-4">
                    <div class="col-12">
                        <h1 class="page-title">Gallery</h1>
                        <p class="text-muted">Manage your coaching center's gallery images here.</p>
                    </div>
                </div>
                <?php echo $message; ?>
                <div class="row mb-4">
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header"><h5 class="mb-0">Upload New Image</h5></div>
                            <div class="card-body">
                                <form method="post" enctype="multipart/form-data">
                                    <div class="mb-3">
                                        <label class="form-label">Image</label>
                                        <input type="file" name="image" class="form-control" accept="image/*" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Caption</label>
                                        <input type="text" name="caption" class="form-control" maxlength="100">
                                    </div>
                                    <div class="d-grid">
                                        <button type="submit" name="upload_image" class="btn btn-primary">Upload</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <?php if (!empty($images)): ?>
                        <?php foreach ($images as $img): ?>
                            <div class="col-md-3 mb-4">
                                <div class="card h-100">
                                    <img src="<?php echo getUploadUrl($img['image_path']); ?>" class="gallery-img card-img-top" alt="Gallery Image">
                                    <div class="card-body">
                                        <p class="card-text"><?php echo htmlspecialchars($img['caption']); ?></p>
                                    </div>
                                    <div class="card-footer text-end">
                                        <a href="gallery.php?action=delete&image_id=<?php echo $img['image_id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Delete this image?');">Delete</a>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="col-12 text-center text-muted">No images in your gallery yet.</div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
